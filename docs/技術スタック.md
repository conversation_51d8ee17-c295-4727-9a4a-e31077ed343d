# 技術スタック

## プログラミング言語

### Python 3
- **バージョン**: Python 3 (Glue 4.0対応)
- **用途**: 
  - AWS Glue ETLジョブ
  - AWS Lambda関数
  - データ処理・変換ロジック

### SQL
- **用途**:
  - データベーススキーマ定義
  - データ変換クエリ
  - MERGE文によるデータ更新

## AWSサービス

### コンピューティング・処理

#### AWS Glue
- **バージョン**: 4.0
- **用途**: ETL処理エンジン
- **機能**:
  - データカタログ
  - Spark-based ETL処理
  - スケジューリング
  - メトリクス・ログ

#### AWS Lambda
- **ランタイム**: Python 3.13
- **用途**: 
  - イベント処理
  - ファイル存在チェック
  - 重複実行制御

#### AWS Step Functions
- **用途**: ワークフロー管理
- **機能**:
  - ジョブオーケストレーション
  - エラーハンドリング
  - 実行状態管理

### データストレージ

#### Amazon Redshift
- **用途**: データウェアハウス
- **機能**:
  - 列指向データベース
  - 大規模データ分析
  - 自動バックアップ

#### Amazon S3
- **用途**: 
  - データレイク
  - ファイルストレージ
  - スクリプト・設定保存
- **機能**:
  - 無制限ストレージ
  - バージョニング
  - ライフサイクル管理

### 管理・監視

#### Amazon EventBridge
- **用途**: スケジューリング
- **機能**:
  - Cronベーススケジュール
  - イベントルーティング

#### Amazon CloudWatch
- **用途**: 監視・ログ
- **機能**:
  - ログ集約
  - メトリクス監視
  - アラート

#### AWS Systems Manager Parameter Store
- **用途**: 設定管理
- **機能**:
  - パラメータ保存
  - 暗号化サポート

#### AWS Secrets Manager
- **用途**: 認証情報管理
- **機能**:
  - データベース認証情報
  - 自動ローテーション

## Pythonライブラリ・依存関係

### AWS Glue関連
```python
from awsglue.utils import getResolvedOptions
from awsglue.context import GlueContext
from awsglue.job import Job
from pyspark.context import SparkContext
```

### データベース接続
```python
redshift_connector==2.1.5  # Redshift接続ライブラリ
```

### AWS SDK
```python
import boto3  # AWS SDK for Python
```

### データ処理
```python
from pyspark.sql.types import *  # Spark SQL型定義
import configparser  # 設定ファイル処理
import json  # JSON処理
```

### ユーティリティ
```python
import datetime  # 日時処理
import logging  # ログ処理
import traceback  # エラートレース
import base64  # エンコーディング
```

## インフラストラクチャ

### Infrastructure as Code (IaC)

#### AWS CloudFormation
- **バージョン**: 2010-09-09
- **用途**: インフラ定義・デプロイ
- **リソース管理**:
  - Glue Jobs
  - Lambda Functions
  - Step Functions
  - EventBridge Rules
  - IAM Roles

### 設定管理

#### ConfigParser (Python)
- **用途**: 設定ファイル管理
- **ファイル形式**: INI形式
- **設定内容**:
  - インターフェース定義
  - データマッピング
  - ログメッセージ

## データベース技術

### Amazon Redshift
- **SQL方言**: PostgreSQL互換
- **データ型**:
  - VARCHAR
  - DECIMAL
  - TIMESTAMP
  - DATE

### データ処理パターン
- **MERGE文**: UPSERT処理
- **CAST関数**: データ型変換
- **NULLIF関数**: NULL値処理

## ファイル形式・プロトコル

### データファイル
- **CSV**: カンマ区切り値
- **エンコーディング**: UTF-8
- **区切り文字**: カスタマイズ可能
- **クォート文字**: カスタマイズ可能

### 設定ファイル
- **INI形式**: 設定ファイル
- **YAML**: CloudFormation テンプレート
- **JSON**: Lambda入出力

## 開発・運用ツール

### バージョン管理
- **Git**: ソースコード管理

### デプロイメント
- **AWS CloudFormation**: インフラデプロイ
- **S3**: スクリプト・設定デプロイ

### 監視・ログ
- **CloudWatch Logs**: ログ集約
- **CloudWatch Metrics**: メトリクス監視
- **カスタムログフォーマット**: 構造化ログ

## セキュリティ

### 認証・認可
- **IAM**: アクセス制御
- **IAM Roles**: サービス間認証
- **Secrets Manager**: 認証情報管理

### 暗号化
- **S3**: 保存時暗号化
- **Redshift**: 保存時暗号化
- **Parameter Store**: パラメータ暗号化

## 環境別設定

### 開発環境 (dev)
- **AWSアカウント**: 886436956581
- **リージョン**: ap-northeast-1

### ステージング環境 (stg)
- **AWSアカウント**: 869935081854
- **リージョン**: ap-northeast-1

### 本番環境 (prd)
- **AWSアカウント**: 879381279381
- **リージョン**: ap-northeast-1

## パフォーマンス最適化

### Spark設定
- **enable-metrics**: true
- **enable-observability-metrics**: true
- **enable-continuous-cloudwatch-log**: true
- **enable-glue-datacatalog**: true

### Redshift最適化
- **UNLOAD**: 並列データ出力
- **COPY**: 並列データ取り込み
- **一時ディレクトリ**: S3テンポラリ領域

## 品質保証

### データ検証
- **スキーマ検証**: 構造チェック
- **データ型チェック**: 型変換検証
- **NULL値処理**: NULLIF関数使用

### エラーハンドリング
- **例外処理**: try-catch構文
- **ログ出力**: 構造化ログ
- **リトライ機能**: Step Functions組み込み
