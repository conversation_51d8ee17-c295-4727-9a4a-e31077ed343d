# システムアーキテクチャ構成

## 全体アーキテクチャ

このプロジェクトは、AWS上に構築されたサーバーレス・マネージドサービス中心のデータウェアハウスアーキテクチャを採用しています。

### 主要コンポーネント

1. **AWS Glue** - ETL処理エンジン
2. **Amazon Redshift** - データウェアハウス
3. **Amazon S3** - データレイク・ストレージ
4. **AWS Lambda** - イベント処理・制御
5. **AWS Step Functions** - ワークフロー管理
6. **Amazon EventBridge** - スケジューリング
7. **AWS Systems Manager Parameter Store** - 設定管理
8. **AWS Secrets Manager** - 認証情報管理
9. **Amazon CloudWatch** - ログ・監視

## データフロー

### 1. データ取り込みフロー

```
外部システム → S3 (input-output/DLPF_DWH) → Lambda (ファイル存在チェック) → Step Functions → Glue Job (FILE_LOAD) → Redshift
```

### 2. データ変換フロー

```
Redshift (raw) → Glue Job (DB_DATA_COPY) → Redshift (work/analysis)
```

### 3. データ出力フロー

```
Redshift → Glue Job (DB_TO_FILE) → S3 (output) → 外部システム
```

## コンポーネント詳細

### AWS Glue Jobs

#### 1. DWH_JB_FILE_LOAD
- **目的**: CSVファイルからRedshiftへのデータロード
- **処理内容**:
  - S3からCSVファイル読み込み
  - データ検証・変換
  - Redshiftへのロード
  - ファイルのバックアップ・削除

#### 2. DWH_JB_DB_DATA_COPY
- **目的**: Redshift内でのデータコピー・変換
- **処理内容**:
  - ソーステーブルからターゲットテーブルへのデータコピー
  - データ変換処理

#### 3. DWH_JB_DB_TO_FILE
- **目的**: RedshiftからCSVファイルへのデータ出力
- **処理内容**:
  - Redshiftからのデータ抽出
  - CSVファイル生成
  - S3への出力

### AWS Lambda Functions

#### 1. DWH_LM_S3_FILE_EXISTS_CHECK
- **目的**: S3ファイルの存在チェック
- **処理内容**:
  - 指定されたS3パスのファイル存在確認
  - 最も古いフォルダの特定
  - ファイルパス情報の返却

#### 2. DWH_LM_DUPLICATE_EVENT_CHECK
- **目的**: 重複実行防止
- **処理内容**:
  - Step Functions実行の重複チェック
  - 重複時の適切な処理

### AWS Step Functions

Step Functionsは、各データ処理ワークフローを管理します。主なワークフローパターン：

1. **ファイル取り込みワークフロー**:
   ```
   ファイル存在チェック → Glue Job実行 → 完了通知
   ```

2. **データ出力ワークフロー**:
   ```
   Glue Job実行 (DB_TO_FILE) → 完了
   ```

## データストレージ構成

### Amazon S3

#### バケット構成
- **aws-glue-assets-{account-id}-ap-northeast-1**: Glueスクリプト・設定
- **s3-{env}-dlpf-if-{account-id}**: インターフェースファイル

#### フォルダ構成
```
s3://bucket/
├── input-output/DLPF_DWH/
│   └── YYYYMMDD/
│       └── {interface_id}.csv
├── scripts/dwh/
│   ├── config/
│   │   ├── common.config
│   │   └── log_message.config
│   └── SQL/
│       └── {interface_id}/
│           ├── postaction.sql
│           └── preaction.sql
└── temporary/
```

### Amazon Redshift

#### スキーマ構成
- **dwh_raw**: 生データ格納
- **dwh_work**: 作業用テーブル
- **dwh_analysis**: 分析用データ

## 環境分離

### 開発環境 (dev)
- AWSアカウント: ************
- リソース命名: dev-dlpf-*

### ステージング環境 (stg)
- AWSアカウント: ************
- リソース命名: stg-dlpf-*

### 本番環境 (prd)
- AWSアカウント: ************
- リソース命名: prd-dlpf-*

## セキュリティ・認証

### IAMロール
- **AWSGlueServiceRole-{env}-dlpf-glue-job-dwh**: Glue実行ロール
- **role-{env}-dlpf-sfn-dwh**: Step Functions実行ロール
- **role-{env}-dlpf-s3file-exists-check-dwh**: Lambda実行ロール
- **role-{env}-dlpf-evbr-invoke-lambda**: EventBridge実行ロール

### 認証情報管理
- **AWS Secrets Manager**: Redshift接続情報
- **Parameter Store**: 設定値・パラメータ

## 監視・ログ

### CloudWatch Logs
- Glue Job実行ログ
- Lambda実行ログ
- Step Functions実行ログ

### メトリクス
- Glue Job実行時間・成功率
- Lambda実行時間・エラー率
- Step Functions実行状況

## スケジューリング

### Amazon EventBridge
定期実行スケジュール例：
```yaml
DWH-EB-OMS-SL-001:
  ScheduleExpression: cron(30 15 * * ? *)  # 毎日15:30実行
```

## 拡張性・可用性

### 拡張性
- Glue Job: 自動スケーリング
- Redshift: クラスター拡張可能
- Lambda: 自動スケーリング

### 可用性
- マルチAZ構成
- 自動バックアップ
- 障害時の自動復旧機能
