# TIS-DLPF-DWH プロジェクト包括ドキュメント

## プロジェクト概要

TIS-DLPF-DWH（データレイクプラットフォーム データウェアハウス）は、AWS上に構築された企業向けデータウェアハウスソリューションです。複数のシステムからデータを収集・統合し、分析可能な形で提供する包括的なデータプラットフォームを実現しています。

### 主要な特徴

- **マルチソース対応**: OMS、POS、WMS、MDM、CRMなど多様なシステムからのデータ統合
- **サーバーレスアーキテクチャ**: AWS Glue、Lambda、Step Functionsを活用した効率的な処理
- **環境分離**: 開発・ステージング・本番環境の完全分離
- **高い可用性**: AWSマネージドサービスによる高可用性・自動復旧
- **包括的監視**: CloudWatchを活用した詳細な監視・ログ機能

## ドキュメント構成

このプロジェクトの詳細な分析結果は、以下のドキュメントに分かれています：

### 📋 [プロジェクト概要](./プロジェクト概要.md)
- プロジェクトの目的と主要機能
- ビジネス要件と対象ドメイン
- インターフェース定義の概要

### 🏗️ [アーキテクチャ構成](./アーキテクチャ構成.md)
- システム全体アーキテクチャ
- 主要コンポーネントの詳細
- データフローとワークフロー
- セキュリティ・認証構成

### 🔗 [AWSサービス依存関係](./AWSサービス依存関係.md)
- サービス間の依存関係図
- 各AWSサービスの役割と依存先
- IAMロール・権限の依存関係
- 障害時の影響範囲分析

### 🛠️ [技術スタック](./技術スタック.md)
- 使用技術・フレームワーク・ライブラリ
- AWSサービス構成
- 開発・運用ツール
- バージョン情報

### 🌐 [環境構成](./環境構成.md)
- 開発・ステージング・本番環境の詳細
- デプロイメント方法
- 設定管理・認証情報管理
- 環境間の移行プロセス

### 🗄️ [データベース設計](./データベース設計.md)
- Redshiftスキーマ構成
- 主要テーブル設計
- データ型・制約設計
- パフォーマンス最適化

### ⚙️ [ETLプロセス](./ETLプロセス.md)
- 3つの主要ETLジョブの詳細
- データ変換ロジック
- ワークフロー管理
- データ品質管理

### 📊 [監視・ログ機能](./監視・ログ機能.md)
- カスタムログフレームワーク
- エラーハンドリング戦略
- CloudWatch統合
- 運用監視・アラート

## クイックスタート

### 前提条件

- AWS CLI設定済み
- 適切なIAM権限
- 各環境のAWSアカウントアクセス

### デプロイ手順

1. **CloudFormationテンプレートのデプロイ**
   ```bash
   # 開発環境
   aws cloudformation deploy --template-file dwh/cloudformation/01.dev/cho-DWH-JB-FILE-LOAD.yaml --stack-name dwh-file-load-dev
   
   # ステージング環境
   aws cloudformation deploy --template-file dwh/cloudformation/02.stg/cho-DWH-JB-FILE-LOAD.yaml --stack-name dwh-file-load-stg
   
   # 本番環境
   aws cloudformation deploy --template-file dwh/cloudformation/03.prd/cho-DWH-JB-FILE-LOAD.yaml --stack-name dwh-file-load-prd
   ```

2. **スクリプト・設定ファイルのアップロード**
   ```bash
   # Glueスクリプトのアップロード
   aws s3 cp dwh/glue/source/ s3://aws-glue-assets-{account-id}-ap-northeast-1/scripts/dwh/ --recursive
   
   # 設定ファイルのアップロード
   aws s3 cp dwh/glue/config/ s3://aws-glue-assets-{account-id}-ap-northeast-1/scripts/dwh/config/ --recursive
   ```

3. **Parameter Storeの設定**
   ```bash
   # 必要なパラメータの設定
   aws ssm put-parameter --name "S3_BUCKET_NAME_GLUE" --value "aws-glue-assets-{account-id}-ap-northeast-1" --type String
   aws ssm put-parameter --name "SECRET_NAME" --value "redshift-secret" --type String
   ```

## 主要なデータフロー

### 1. ファイル取り込みフロー
```
外部システム → S3 → Lambda(ファイル存在チェック) → Step Functions → Glue(FILE_LOAD) → Redshift
```

### 2. データ変換フロー
```
Redshift(raw) → Glue(DB_DATA_COPY) → Redshift(work/analysis)
```

### 3. データ出力フロー
```
Redshift → Glue(DB_TO_FILE) → S3 → 外部システム
```

## 主要なインターフェース

| インターフェースID | 説明 | ソースシステム | 対象テーブル |
|---|---|---|---|
| IF-OMS-SL-001 | 売上実績データ | OMS | sales_record |
| IF-OMS-OR-001 | 注文データ | OMS | order_header |
| IF-MDM-PR-001 | 商品マスタ | MDM | commodity_header |
| IF-POS-SH-001 | 店舗マスタ | POS | shop_mst_raw |
| IF-WMS-ST-001 | 在庫データ | WMS | stock |

## 運用・保守

### 日常監視項目

- **Glueジョブ実行状況**: 成功率、実行時間
- **Lambda関数状況**: エラー率、実行時間
- **Step Functions状況**: 実行状態、失敗ジョブ
- **Redshift状況**: 接続状況、クエリパフォーマンス

### トラブルシューティング

1. **ログ確認**: CloudWatch Logsでエラーメッセージを確認
2. **メトリクス確認**: CloudWatch Metricsでパフォーマンス状況を確認
3. **設定確認**: Parameter Store、Secrets Managerの設定を確認
4. **ネットワーク確認**: VPC、セキュリティグループの設定を確認

### 定期メンテナンス

- **ログローテーション**: CloudWatch Logsの保持期間管理
- **バックアップ確認**: Redshiftスナップショットの確認
- **パフォーマンス最適化**: クエリパフォーマンスの分析・改善
- **セキュリティ更新**: IAMロール・ポリシーの見直し

## 拡張・カスタマイズ

### 新しいインターフェースの追加

1. **設定ファイル更新**: `common.config`にインターフェース定義を追加
2. **SQLファイル作成**: 必要に応じて`postaction.sql`、`preaction.sql`を作成
3. **CloudFormationテンプレート更新**: 新しいStep Functionsワークフローを定義
4. **テスト実行**: 開発環境でのテスト実行

### パフォーマンス最適化

- **Redshift**: 分散キー、ソートキーの最適化
- **Glue**: Sparkパラメータの調整
- **S3**: パーティション設計の最適化

## セキュリティ考慮事項

- **最小権限の原則**: IAMロールの適切な権限設定
- **データ暗号化**: 保存時・転送時の暗号化
- **アクセス制御**: VPC、セキュリティグループによるネットワーク制御
- **監査ログ**: CloudTrailによるAPI呼び出しの記録

## サポート・問い合わせ

プロジェクトに関する質問や問題については、以下の手順で対応してください：

1. **ドキュメント確認**: 本ドキュメント群の該当セクションを確認
2. **ログ確認**: CloudWatch Logsでエラー詳細を確認
3. **設定確認**: Parameter Store、設定ファイルの内容を確認
4. **チーム連絡**: 開発チームへの問い合わせ

## ライセンス・免責事項

このドキュメントは、TIS-DLPF-DWHプロジェクトの技術分析結果をまとめたものです。実際の運用においては、最新の設定やコードを確認してください。

## 更新履歴

| 日付 | バージョン | 更新内容 |
|---|---|---|
| 2025/01/17 | 1.0.0 | 初版作成 - コードベース分析による包括的ドキュメント作成 |

---

このドキュメントは、TIS-DLPF-DWHプロジェクトの包括的な技術ドキュメントです。各詳細ドキュメントと合わせてご活用ください。

## 関連ドキュメント

- [プロジェクト概要](./プロジェクト概要.md) - プロジェクトの目的と機能概要
- [アーキテクチャ構成](./アーキテクチャ構成.md) - システム構成とデータフロー
- [AWSサービス依存関係](./AWSサービス依存関係.md) - サービス間依存関係と影響分析
- [技術スタック](./技術スタック.md) - 使用技術とツール
- [環境構成](./環境構成.md) - 環境設定とデプロイメント
- [データベース設計](./データベース設計.md) - DB設計とスキーマ
- [ETLプロセス](./ETLプロセス.md) - データ処理フロー
- [監視・ログ機能](./監視・ログ機能.md) - 監視とログ管理
