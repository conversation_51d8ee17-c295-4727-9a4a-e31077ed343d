# データベース設計

## データベース概要

このプロジェクトは、Amazon Redshiftを使用したデータウェアハウスを構築しています。データベースは、複数のスキーマに分かれており、データの処理段階に応じて適切に分離されています。

## スキーマ構成

### 1. dwh_raw スキーマ
- **目的**: 外部システムから取り込んだ生データの格納
- **特徴**: 
  - 元データの形式を保持
  - データクレンジング前の状態
  - 各システムからの直接取り込み

### 2. dwh_work スキーマ
- **目的**: データ変換・加工処理の作業領域
- **特徴**:
  - 一時的なデータ格納
  - データ変換処理中の中間結果
  - 文字列型での日付データ保持

### 3. dwh_analysis スキーマ
- **目的**: 分析用に最適化されたデータの格納
- **特徴**:
  - 分析に適した形式に変換済み
  - パフォーマンス最適化済み
  - ビジネスロジック適用済み

## 主要テーブル設計

### 受注関連テーブル

#### order_header (注文ヘッダ)
```sql
CREATE TABLE order_header (
    order_no VARCHAR(64) NOT NULL,           -- 注文番号
    shop_code VARCHAR(64) NOT NULL,          -- 店舗コード
    order_datetime TIMESTAMP NOT NULL,       -- 注文日時
    customer_code VARCHAR(64) NOT NULL,      -- 顧客コード
    neo_customer_no VARCHAR(48),             -- NEO顧客番号
    guest_flg DECIMAL(1) NOT NULL,           -- ゲストフラグ
    last_name VARCHAR(80) NOT NULL,          -- 姓
    first_name VARCHAR(80),                  -- 名
    last_name_kana VARCHAR(160) NOT NULL,    -- 姓（カナ）
    first_name_kana VARCHAR(160),            -- 名（カナ）
    email VARCHAR(1024),                     -- メールアドレス
    birth_date DATE NOT NULL,                -- 生年月日
    sex DECIMAL(1) NOT NULL,                 -- 性別
    postal_code VARCHAR(28) NOT NULL,        -- 郵便番号
    prefecture_code VARCHAR(8) NOT NULL,     -- 都道府県コード
    address1 VARCHAR(16) NOT NULL,           -- 住所1
    address2 VARCHAR(200) NOT NULL,          -- 住所2
    address3 VARCHAR(1020) NOT NULL,         -- 住所3
    address4 VARCHAR(400),                   -- 住所4
    phone_number VARCHAR(64) NOT NULL,       -- 電話番号
    payment_method_no DECIMAL(8) NOT NULL,   -- 支払方法番号
    -- 監査フィールド
    orm_rowid DECIMAL(38) NOT NULL,
    created_user VARCHAR(400) NOT NULL,
    created_datetime TIMESTAMP NOT NULL,
    updated_user VARCHAR(400) NOT NULL,
    updated_datetime TIMESTAMP NOT NULL,
    dwh_created_user VARCHAR(400),
    dwh_created_datetime TIMESTAMP,
    dwh_updated_user VARCHAR(400),
    dwh_updated_datetime TIMESTAMP
);
```

### 商品関連テーブル

#### commodity_header (商品ヘッダ)
```sql
CREATE TABLE commodity_header (
    shop_code VARCHAR(64) NOT NULL,              -- 店舗コード
    commodity_code VARCHAR(64) NOT NULL,         -- 商品コード
    commodity_name VARCHAR(400) NOT NULL,        -- 商品名
    commodity_type DECIMAL(1) NOT NULL,          -- 商品タイプ
    represent_sku_code VARCHAR(96) NOT NULL,     -- 代表SKUコード
    represent_sku_unit_price DECIMAL(8) NOT NULL, -- 代表SKU単価
    stock_status_no DECIMAL(8),                  -- 在庫ステータス番号
    stock_management_type DECIMAL(1) NOT NULL,   -- 在庫管理タイプ
    age_limit_code DECIMAL(8) NOT NULL,          -- 年齢制限コード
    commodity_tax_type DECIMAL(1) NOT NULL,      -- 商品税タイプ
    tax_group_code VARCHAR(32) NOT NULL,         -- 税グループコード
    short_description VARCHAR(400),              -- 短い説明
    commodity_search_words VARCHAR(2000),        -- 商品検索ワード
    sale_start_datetime TIMESTAMP NOT NULL,      -- 販売開始日時
    sale_end_datetime TIMESTAMP NOT NULL,        -- 販売終了日時
    -- 監査フィールド
    orm_rowid DECIMAL(38) NOT NULL,
    created_user VARCHAR(400) NOT NULL,
    created_datetime TIMESTAMP NOT NULL,
    updated_user VARCHAR(400) NOT NULL,
    updated_datetime TIMESTAMP NOT NULL
);
```

#### category (カテゴリ)
```sql
CREATE TABLE category (
    category_code VARCHAR(64) NOT NULL,          -- カテゴリコード
    category_name_pc VARCHAR(80) NOT NULL,       -- カテゴリ名（PC）
    category_name_sp VARCHAR(40),                -- カテゴリ名（SP）
    parent_category_code VARCHAR(64) NOT NULL,   -- 親カテゴリコード
    path VARCHAR(1024) NOT NULL,                 -- パス
    depth DECIMAL(2) NOT NULL,                   -- 深度
    display_order DECIMAL(8) NOT NULL,           -- 表示順
    commodity_count DECIMAL(12),                 -- 商品数
    public_commodity_count DECIMAL(12),          -- 公開商品数
    -- 監査フィールド
    orm_rowid DECIMAL(38) NOT NULL,
    created_user VARCHAR(400) NOT NULL,
    created_datetime TIMESTAMP NOT NULL,
    updated_user VARCHAR(400) NOT NULL,
    updated_datetime TIMESTAMP NOT NULL
);
```

### 在庫関連テーブル

#### stock (在庫)
```sql
CREATE TABLE stock (
    shop_code VARCHAR(64) NOT NULL,                    -- 店舗コード
    sku_code VARCHAR(96) NOT NULL,                     -- SKUコード
    allocated_warehouse_code VARCHAR(24) NOT NULL,     -- 割当倉庫コード
    commodity_code VARCHAR(64) NOT NULL,               -- 商品コード
    wms_stock_quantity DECIMAL(8),                     -- WMS在庫数量
    stock_quantity DECIMAL(8) NOT NULL,                -- 在庫数量
    allocated_quantity DECIMAL(8) NOT NULL,            -- 引当数量
    reserved_quantity DECIMAL(8),                      -- 予約数量
    temporary_allocated_quantity DECIMAL(8) NOT NULL,  -- 一時引当数量
    arrival_reserved_quantity DECIMAL(8) NOT NULL,     -- 入荷予約数量
    temporary_reserved_quantity DECIMAL(8) NOT NULL,   -- 一時予約数量
    reservation_limit DECIMAL(8),                      -- 予約上限
    stock_threshold DECIMAL(8) NOT NULL,               -- 在庫閾値
    stock_arrival_date DATE,                           -- 在庫入荷日
    arrival_quantity DECIMAL(8),                       -- 入荷数量
    -- 監査フィールド
    orm_rowid DECIMAL(38) NOT NULL,
    created_user VARCHAR(400) NOT NULL,
    created_datetime TIMESTAMP NOT NULL,
    updated_user VARCHAR(400) NOT NULL,
    updated_datetime TIMESTAMP NOT NULL
);
```

### 売上関連テーブル

#### sales_record (売上実績)
```sql
CREATE TABLE sales_record (
    earnings_jisseki_no DECIMAL(15) NOT NULL,     -- 売上実績番号
    order_henpin_kbn VARCHAR(4) NOT NULL,         -- 注文返品区分
    sales_detail_kbn VARCHAR(8) NOT NULL,         -- 売上明細区分
    shipping_no VARCHAR(64),                      -- 出荷番号
    shipping_detail_no DECIMAL(16),               -- 出荷明細番号
    order_no VARCHAR(64),                         -- 注文番号
    order_detail_no DECIMAL(16),                  -- 注文明細番号
    shipping_date DATE NOT NULL,                  -- 出荷日
    sales_recording_date DATE NOT NULL,           -- 売上計上日
    marketing_channel VARCHAR(8) NOT NULL,        -- マーケティングチャネル
    shop_code VARCHAR(64),                        -- 店舗コード
    main_product_no VARCHAR(96),                  -- メイン商品番号
    product_no VARCHAR(96),                       -- 商品番号
    commodity_code VARCHAR(64),                   -- 商品コード
    commodity_name VARCHAR(400),                  -- 商品名
    commodity_kind VARCHAR(8),                    -- 商品種別
    commodity_group VARCHAR(20),                  -- 商品グループ
    commodity_series VARCHAR(20),                 -- 商品シリーズ
    commodity_segment VARCHAR(20),                -- 商品セグメント
    business_segment VARCHAR(20),                 -- 事業セグメント
    commodity_amount DECIMAL(8),                  -- 商品数量
    incurred_price DECIMAL(8),                    -- 発生価格
    tax_group_code VARCHAR(32),                   -- 税グループコード
    tax_no DECIMAL(3),                            -- 税番号
    tax_rate DECIMAL(3),                          -- 税率
    -- 監査フィールド
    orm_rowid DECIMAL(38) NOT NULL,
    created_user VARCHAR(400) NOT NULL,
    created_datetime TIMESTAMP NOT NULL,
    updated_user VARCHAR(400) NOT NULL,
    updated_datetime TIMESTAMP NOT NULL
);
```

### 店舗関連テーブル

#### shop_mst_raw (店舗マスタ)
```sql
CREATE TABLE shop_mst_raw (
    shop_cd VARCHAR(48) NOT NULL,              -- 店舗コード
    group_cd VARCHAR(40),                      -- グループコード
    area_cd VARCHAR(40),                       -- エリアコード
    district_cd VARCHAR(40),                   -- 地区コード
    shop_kind VARCHAR(8) NOT NULL,             -- 店舗種別
    shop_name_full VARCHAR(240) NOT NULL,      -- 店舗名（フル）
    shop_name_half VARCHAR(240),               -- 店舗名（半角）
    shop_name_short VARCHAR(240),              -- 店舗名（短縮）
    shop_name_english VARCHAR(240),            -- 店舗名（英語）
    zip_cd VARCHAR(60),                        -- 郵便番号
    prefecture_cd VARCHAR(40),                 -- 都道府県コード
    address1 VARCHAR(240),                     -- 住所1
    address2 VARCHAR(240),                     -- 住所2
    address3 VARCHAR(240),                     -- 住所3
    address4 VARCHAR(240),                     -- 住所4
    tel_num VARCHAR(60),                       -- 電話番号
    fax_num VARCHAR(60),                       -- FAX番号
    condition_kind VARCHAR(8) NOT NULL,        -- 状態種別
    condition_detail_kind VARCHAR(8) NOT NULL, -- 状態詳細種別
    channel_kind VARCHAR(40),                  -- チャネル種別
    is_stock_control DECIMAL(4) NOT NULL,      -- 在庫管理フラグ
    begin_date DATE,                           -- 開始日
    end_date DATE,                             -- 終了日
    shop_floor_space DECIMAL(6,2)              -- 店舗面積
);
```

## データ型設計

### 標準データ型

#### 文字列型
- **VARCHAR(n)**: 可変長文字列
- **用途**: 名前、コード、説明文など

#### 数値型
- **DECIMAL(p,s)**: 固定小数点数
- **用途**: 金額、数量、率など

#### 日時型
- **TIMESTAMP**: 日時（年月日時分秒）
- **DATE**: 日付（年月日）

### 監査フィールド

全てのテーブルには、以下の監査フィールドが含まれています：

```sql
-- 元システム監査フィールド
orm_rowid DECIMAL(38) NOT NULL,           -- 元システム行ID
created_user VARCHAR(400) NOT NULL,       -- 作成ユーザー
created_datetime TIMESTAMP NOT NULL,      -- 作成日時
updated_user VARCHAR(400) NOT NULL,       -- 更新ユーザー
updated_datetime TIMESTAMP NOT NULL,      -- 更新日時

-- DWH監査フィールド
dwh_created_user VARCHAR(400),            -- DWH作成ユーザー
dwh_created_datetime TIMESTAMP,           -- DWH作成日時
dwh_updated_user VARCHAR(400),            -- DWH更新ユーザー
dwh_updated_datetime TIMESTAMP            -- DWH更新日時
```

## ワークテーブル設計

### 命名規則
- **本テーブル**: テーブル名
- **ワークテーブル**: テーブル名_work
- **分析テーブル**: テーブル名_analysis

### データ型の違い

ワークテーブルでは、データ変換処理を考慮して、一部のフィールドが文字列型で定義されています：

```sql
-- 本テーブル
begin_date DATE,
end_date DATE,

-- ワークテーブル
begin_date VARCHAR(20),
end_date VARCHAR(20),
```

## データ変換パターン

### MERGE文によるUPSERT

データの更新は、MERGE文を使用したUPSERT処理で行われます：

```sql
MERGE INTO dwh_raw.sales_record USING dwh_raw.sales_record_work
ON sales_record.earnings_jisseki_no = sales_record_work.earnings_jisseki_no
WHEN MATCHED THEN UPDATE SET
    order_henpin_kbn = sales_record_work.order_henpin_kbn,
    -- 他のフィールド更新
WHEN NOT MATCHED THEN INSERT VALUES (
    sales_record_work.earnings_jisseki_no,
    -- 他のフィールド挿入
);
```

### データ型変換

文字列から適切なデータ型への変換処理：

```sql
CAST(NULLIF(sales_record_work.shipping_date, '') AS DATE),
CAST(NULLIF(sales_record_work.created_datetime, '') AS TIMESTAMP)
```

## インデックス・パフォーマンス

### 主キー設計
- 各テーブルには、ビジネスキーに基づく主キーが設定
- 複合キーの場合は、検索頻度の高いフィールドを先頭に配置

### 分散キー
- Redshiftの分散処理を最適化するため、適切な分散キーを設定
- 結合頻度の高いフィールドを分散キーとして選択

## データ品質管理

### NULL値処理
- NULLIF関数を使用して、空文字列をNULLに変換
- 必須フィールドには NOT NULL制約を設定

### データ整合性
- 外部キー制約に相当するビジネスルールをETL処理で実装
- データ検証ロジックをGlueジョブに組み込み
