# AWSサービス依存関係

## 概要

TIS-DLPF-DWHプロジェクトは、複数のAWSサービスが相互に連携して動作するデータウェアハウスシステムです。本ドキュメントでは、各AWSサービス間の依存関係を詳細に分析し、システムの理解と運用に必要な情報を提供します。

## 依存関係図

```mermaid
graph TB
    %% 外部システム
    ExtSys[外部システム] --> S3Input[S3: input-output/DLPF_DWH]
    
    %% スケジューリング層
    EB[EventBridge<br/>スケジューラー] --> LambdaDup[Lambda:<br/>DWH_LM_DUPLICATE_EVENT_CHECK]
    
    %% オーケストレーション層
    LambdaDup --> SFN[Step Functions:<br/>DWH-JN-*]
    SFN --> LambdaCheck[Lambda:<br/>DWH_LM_S3_FILE_EXISTS_CHECK]
    LambdaCheck --> S3Input
    
    %% ETL処理層
    SFN --> GlueFileLoad[Glue Job:<br/>DWH_JB_FILE_LOAD]
    SFN --> GlueDBCopy[Glue Job:<br/>DWH_JB_DB_DATA_COPY]
    SFN --> GlueDBToFile[Glue Job:<br/>DWH_JB_DB_TO_FILE]
    
    %% ストレージ層
    S3Input --> GlueFileLoad
    GlueFileLoad --> Redshift[(Redshift<br/>データウェアハウス)]
    GlueDBCopy --> Redshift
    Redshift --> GlueDBToFile
    GlueDBToFile --> S3Output[S3: 出力ファイル]
    S3Output --> ExtSysOut[外部システム]
    
    %% 設定・認証
    ParamStore[Parameter Store<br/>設定管理] --> GlueFileLoad
    ParamStore --> GlueDBCopy
    ParamStore --> GlueDBToFile
    ParamStore --> LambdaCheck
    
    SecretsManager[Secrets Manager<br/>認証情報] --> GlueFileLoad
    SecretsManager --> GlueDBCopy
    SecretsManager --> GlueDBToFile
    
    %% スクリプト・設定ストレージ
    S3Scripts[S3: aws-glue-assets<br/>スクリプト・設定] --> GlueFileLoad
    S3Scripts --> GlueDBCopy
    S3Scripts --> GlueDBToFile
    
    %% 監視・ログ
    CloudWatch[CloudWatch<br/>ログ・メトリクス] --> GlueFileLoad
    CloudWatch --> GlueDBCopy
    CloudWatch --> GlueDBToFile
    CloudWatch --> LambdaDup
    CloudWatch --> LambdaCheck
    CloudWatch --> SFN
```

## 主要な依存関係パターン

### 1. スケジューリング → オーケストレーション
```
EventBridge → Lambda (重複チェック) → Step Functions
```

### 2. ファイル処理フロー
```
S3 (入力) → Lambda (ファイル存在チェック) → Glue (FILE_LOAD) → Redshift
```

### 3. データ変換フロー
```
Redshift → Glue (DB_DATA_COPY) → Redshift
```

### 4. データ出力フロー
```
Redshift → Glue (DB_TO_FILE) → S3 (出力)
```

## サービス別依存関係詳細

### Amazon EventBridge

#### 依存関係
- **依存先**: Lambda (DWH_LM_DUPLICATE_EVENT_CHECK)
- **IAMロール**: role-dev-dlpf-evbr-invoke-lambda

#### 役割
- 定期スケジュール実行（cron式）
- 各ジョブネットの開始トリガー

#### 設定例
```yaml
ScheduleExpression: cron(30 15 * * ? *)  # 毎日15:30実行
```

### AWS Lambda

#### 1. DWH_LM_DUPLICATE_EVENT_CHECK

**依存関係**:
- **依存先**: Step Functions
- **依存元**: EventBridge
- **IAMロール**: role-dev-dlpf-s3file-exists-check-dwh

**役割**:
- Step Functions実行の重複防止
- 実行名の生成と管理

#### 2. DWH_LM_S3_FILE_EXISTS_CHECK

**依存関係**:
- **依存先**: S3 (ファイル存在確認)
- **依存元**: Step Functions
- **環境変数**: S3_BUCKET_NAME

**役割**:
- 入力ファイルの存在チェック
- 最も古いフォルダの特定

### AWS Step Functions

#### 依存関係
- **依存先**: 
  - Lambda (DWH_LM_S3_FILE_EXISTS_CHECK)
  - Glue Jobs (DWH_JB_*)
- **依存元**: Lambda (DWH_LM_DUPLICATE_EVENT_CHECK)
- **IAMロール**: role-dev-dlpf-sfn-dwh

#### 設定依存
- **Parameter Store**: 
  - DWH_RETRY_COUNT (リトライ回数)
  - DWH_RETRY_TIMEOUT (リトライ間隔)

#### 監視依存
- **CloudWatch Logs**: 実行ログの出力

### AWS Glue

#### 共通依存関係
全Glueジョブ（DWH_JB_FILE_LOAD、DWH_JB_DB_DATA_COPY、DWH_JB_DB_TO_FILE）が以下に依存：

**ストレージ**:
- **S3**: スクリプト保存 (aws-glue-assets-*)
- **S3**: 一時ファイル (temporary/)

**設定・認証**:
- **Parameter Store**: 設定値の取得
- **Secrets Manager**: Redshift認証情報

**監視**:
- **CloudWatch**: ログ・メトリクス出力

**権限**:
- **IAMロール**: AWSGlueServiceRole-dev-dlpf-glue-job-dwh

#### 個別依存関係

##### DWH_JB_FILE_LOAD
```
S3 (入力ファイル) → Redshift
S3 (バックアップ、一時ファイル)
```

##### DWH_JB_DB_DATA_COPY
```
Redshift → Redshift (スキーマ間コピー)
```

##### DWH_JB_DB_TO_FILE
```
Redshift → S3 (出力ファイル)
```

### Amazon Redshift

#### 依存関係
- **依存元**: 全Glueジョブ
- **認証**: Secrets Manager
- **ネットワーク**: VPC、セキュリティグループ

#### 特徴
- システムの中核データストア
- 全ETL処理の起点・終点

### Amazon S3

#### 用途別バケット構成

##### 1. 入力ファイル
- **バケット**: s3-dev-dlpf-if-*
- **用途**: 外部システムからのファイル受信
- **依存元**: Lambda (ファイル存在チェック)、Glue (FILE_LOAD)

##### 2. スクリプト・設定
- **バケット**: aws-glue-assets-*
- **用途**: Glueスクリプト、設定ファイル保存
- **依存元**: 全Glueジョブ

##### 3. 一時ファイル
- **パス**: temporary/
- **用途**: Glue処理中の一時データ
- **依存元**: 全Glueジョブ

##### 4. バックアップ
- **パス**: backup/
- **用途**: 処理済みファイルのバックアップ
- **依存元**: Glue (FILE_LOAD)

##### 5. 出力ファイル
- **パス**: output/
- **用途**: 外部システムへのファイル出力
- **依存元**: Glue (DB_TO_FILE)

### 設定・認証サービス

#### AWS Systems Manager Parameter Store

**依存元**:
- 全Glueジョブ
- Lambda関数
- Step Functions

**主要パラメータ**:
- S3_BUCKET_NAME_GLUE
- REDSHIFT_CONNECTION_GLUE
- REDSHIFT_TEMPDIR
- SECRET_NAME
- S3_BUCKET_NAME_PREFIX
- S3_CSV_OUTPUT_PATH
- S3_CSV_BACKUP_PATH
- DWH_UNLOAD_IAM_ROLE
- DWH_RETRY_COUNT
- DWH_RETRY_TIMEOUT

#### AWS Secrets Manager

**依存元**:
- 全Glueジョブ

**管理内容**:
- Redshift接続情報（host、username、password、database）

### 監視・ログサービス

#### Amazon CloudWatch

**CloudWatch Logs**:
- 全サービスのログ集約
- Step Functions、Glue、Lambda実行ログ

**CloudWatch Metrics**:
- パフォーマンス監視
- アラート設定
- 実行時間、成功率、エラー率

## IAMロール依存関係

### サービス別IAMロール

#### 1. AWSGlueServiceRole-dev-dlpf-glue-job-dwh
- **使用サービス**: 全Glueジョブ
- **権限**: S3、Redshift、Parameter Store、Secrets Manager、CloudWatch

#### 2. role-dev-dlpf-sfn-dwh
- **使用サービス**: Step Functions
- **権限**: Glue、Lambda、CloudWatch Logs

#### 3. role-dev-dlpf-s3file-exists-check-dwh
- **使用サービス**: Lambda (S3ファイル存在チェック)
- **権限**: S3、CloudWatch Logs

#### 4. role-dev-dlpf-evbr-invoke-lambda
- **使用サービス**: EventBridge
- **権限**: Lambda呼び出し

## 重要な依存関係の特徴

### 強い結合

#### Glue ↔ Redshift
- 直接的なデータベース接続
- 認証情報の共有
- ネットワーク設定の依存

#### Step Functions ↔ Glue
- 同期実行による強い依存
- 実行状態の監視
- エラー時の連鎖停止

#### Lambda ↔ S3
- ファイル存在チェックの必須依存
- 環境変数による設定依存

### 設定依存

#### Parameter Store
- 全サービスが設定値を参照
- 設定変更時の影響範囲が広い
- 中央集権的な設定管理

#### Secrets Manager
- データベース認証の中央管理
- 認証情報の自動ローテーション対応

#### S3 (設定)
- Glueスクリプト・設定ファイルの保存
- バージョン管理の重要性

### ネットワーク依存

#### VPC
- Redshiftクラスターの配置
- プライベートサブネット内での通信

#### セキュリティグループ
- Glue ↔ Redshift間の通信制御
- 最小権限の原則に基づく設定

## 障害時の影響範囲

### 単一障害点

#### 1. Amazon Redshift
- **影響**: 全ETL処理が停止
- **対策**: 自動バックアップ、Multi-AZ配置

#### 2. Parameter Store
- **影響**: 設定取得不可で全処理停止
- **対策**: 設定値のキャッシュ、冗長化

#### 3. Secrets Manager
- **影響**: データベース接続不可
- **対策**: 認証情報の複数リージョン配置

### 部分的影響

#### 1. S3 (入力)
- **影響**: 該当インターフェースのみ影響
- **対策**: ファイル配置の監視、アラート

#### 2. Lambda
- **影響**: 該当ワークフローのみ影響
- **対策**: リトライ機能、エラーハンドリング

#### 3. Step Functions
- **影響**: 該当ジョブネットのみ影響
- **対策**: 実行状態の監視、手動再実行

## 運用上の注意点

### 依存関係管理

1. **設定変更時の影響確認**
   - Parameter Store変更時の全サービス影響確認
   - Secrets Manager更新時の接続テスト

2. **ネットワーク設定の管理**
   - セキュリティグループ変更時の通信確認
   - VPC設定変更時の影響範囲確認

3. **IAMロール権限の管理**
   - 最小権限の原則維持
   - 定期的な権限レビュー

### 監視・アラート

1. **重要サービスの監視**
   - Redshift接続状況
   - Parameter Store可用性
   - Secrets Manager可用性

2. **依存関係の監視**
   - サービス間通信の監視
   - 実行フローの監視

3. **障害時の対応**
   - 影響範囲の迅速な特定
   - 復旧手順の明確化

この依存関係分析により、システムの設計思想、障害時の影響範囲、運用時の注意点が明確になります。特に、Redshift、Parameter Store、Secrets Managerは重要な共通依存先として、高い可用性が求められます。
