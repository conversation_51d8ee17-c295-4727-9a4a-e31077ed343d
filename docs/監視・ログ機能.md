# 監視・ログ機能

## ログ機能概要

このプロジェクトは、包括的なログ機能を実装しており、システムの動作状況、エラー情報、パフォーマンスメトリクスを詳細に記録・監視できる仕組みを提供しています。

## ログアーキテクチャ

### ログ収集・集約
```
Glue Jobs → CloudWatch Logs
Lambda Functions → CloudWatch Logs  
Step Functions → CloudWatch Logs
```

### ログ保存先
- **CloudWatch Logs**: 全てのログの中央集約
- **S3**: 長期保存・アーカイブ（オプション）

## カスタムログフレームワーク

### GlueLogger クラス

プロジェクト専用のログフレームワークが実装されています：

```python
class GlueLogger:
    """Glueジョブ用ログ出力クラス"""
    
    # ログレベル定数
    ERROR = logging.ERROR    # 40
    WARN = logging.WARNING   # 30
    INFO = logging.INFO      # 20
    DEBUG = logging.DEBUG    # 10
    TRACE = logging.DEBUG - 5 # 5 (独自定義)
```

### ログフォーマット

#### 標準フォーマット
```
[日時][ログレベル][ジョブネットID][メッセージID]メッセージ内容
```

#### 実装例
```python
self.msg_format = "[%s][%s][%s][%s]%s"
# 出力例: [2024/01/15 10:30:45][INFO][DWH-JN-OMS-SL-001][I_DWH_JB_FILE_LOAD_001]ファイル取り込み処理を開始します。
```

### ログレベル管理

#### 環境変数による制御
```python
log_level = os.environ.get("LOG_LEVEL", "INFO")
self.log_level = getattr(logging, log_level.upper(), logging.INFO)
```

#### レベル別出力制御
```python
def loggerByMsgId(self, msg_id: str, msg_values: Optional[Tuple[Any, ...]] = None):
    if msg_id[0] == "T" and self.log_level <= self.TRACE:
        self.trace(msg_id)
    elif msg_id[0] == "I" and self.log_level <= self.INFO:
        self.info(msg_id, msg_values)
    elif msg_id[0] == "W" and self.log_level <= self.WARN:
        self.warning(msg_id, msg_values)
    elif msg_id[0] == "E" and self.log_level <= self.ERROR:
        self.error(msg_id, msg_values)
```

## ログメッセージ管理

### メッセージ定義ファイル

ログメッセージは、設定ファイルで一元管理されています：

```ini
# log_message.config
[INFO]
I_DWH_JB_FILE_LOAD_001 = {%%s}ファイル取り込み処理を開始します。
I_DWH_JB_FILE_LOAD_002 = {%%s}Redshiftにデータロードが正常終了。
I_DWH_JB_FILE_LOAD_003 = {%%s}ファイル取り込み処理が正常終了。

[ERROR]
E_DWH_JB_FILE_LOAD_001 = {%%s}データ抽出エラー。(ファイルパス：{%%s})
E_DWH_JB_FILE_LOAD_002 = {%%s}データ整合性チェックエラー。(エラー内容：{%%s})
E_DWH_JB_FILE_LOAD_003 = {%%s}Redshiftにデータロードが異常終了。

[WARN]
W_DWH_JB_FILE_LOAD_001 = {%%s}警告メッセージ例
```

### メッセージID体系

#### 命名規則
- **I_**: 情報メッセージ
- **W_**: 警告メッセージ  
- **E_**: エラーメッセージ
- **T_**: トレースメッセージ
- **D_**: デバッグメッセージ

#### 構造
```
[レベル]_[システム]_[コンポーネント]_[処理]_[連番]
例: I_DWH_JB_FILE_LOAD_001
```

### 動的メッセージ生成

```python
def _get_message_content(self, level: str, msg_id: str, msg_values: Optional[Tuple[Any, ...]] = None) -> str:
    try:
        message_template = self.msg_file.get(level, msg_id)
        if msg_values:
            return message_template % msg_values
        return message_template
    except:
        return f"メッセージID {msg_id} が見つかりません"
```

## エラーハンドリング

### 階層的エラー処理

#### レベル1: データ抽出エラー
```python
try:
    data_frame = spark.read.format("csv").load(file_path, **options)
except:
    logger.error("E_DWH_JB_FILE_LOAD_001", msg_values=(if_id, file_path))
    raise ValueError("データ抽出エラー")
```

#### レベル2: データ検証エラー
```python
# ヘッダー検証
if set(expected_headers) != set(actual_headers):
    logger.error("E_DWH_JB_FILE_LOAD_002", msg_values=(if_id, "ヘッダー不一致"))
    raise ValueError("データ整合性チェックエラー")

# 必須項目チェック
for field in primary_key_fields:
    if data_frame.filter(col(field).isNull()).count() > 0:
        logger.error("E_DWH_JB_FILE_LOAD_002", msg_values=(if_id, f"必須項目{field}にNULL"))
        raise ValueError("データ整合性チェックエラー")
```

#### レベル3: データロードエラー
```python
try:
    # Redshiftにロード
    AmazonRedshift_TABLE = glueContext.write_dynamic_frame.from_options(...)
except:
    logger.error("E_DWH_JB_FILE_LOAD_003", msg_values=(if_id))
    raise RuntimeError("Redshiftにデータロードが異常終了")
```

### 例外情報の詳細記録

```python
try:
    # 処理実行
    pass
except Exception as e:
    stack_trace = traceback.format_exc()
    logger.error("E_DWH_JB_DB_DATA_COPY_002", msg_values=(jobnet_id, stack_trace))
    raise
```

## 監視機能

### CloudWatch統合

#### メトリクス有効化
```yaml
DefaultArguments:
  "--enable-metrics": "true"
  "--enable-observability-metrics": "true"
  "--enable-continuous-cloudwatch-log": "true"
```

#### カスタムメトリクス
- 処理件数
- 処理時間
- エラー率
- スループット

### Step Functions監視

#### 実行状態ログ
```yaml
LoggingConfiguration:
  Level: ALL
  IncludeExecutionData: true
  Destinations:
    - CloudWatchLogsLogGroup:
        LogGroupArn: !GetAtt StepFunctionsLogGroup.Arn
```

#### 実行履歴追跡
- 開始時刻
- 終了時刻
- 実行状態
- エラー詳細

### Lambda監視

#### 実行ログ
```python
# Lambda関数内でのログ出力
logger = logging.getLogger()
logger.setLevel("INFO")

def lambda_handler(event, context):
    logger.info(f"処理開始: {event}")
    # 処理実行
    logger.info("処理完了")
```

#### エラー処理
```python
try:
    # 処理実行
    pass
except Exception as e:
    logger.error(f"エラー発生: {e}")
    return {
        "statusCode": 500,
        "body": f"エラー: {str(e)}"
    }
```

## ログ分析・アラート

### CloudWatch Insights

#### クエリ例
```sql
-- エラーログの抽出
fields @timestamp, @message
| filter @message like /ERROR/
| sort @timestamp desc

-- 処理時間の分析
fields @timestamp, @duration
| filter @type = "REPORT"
| stats avg(@duration) by bin(5m)
```

### アラート設定

#### エラー率監視
```yaml
ErrorRateAlarm:
  Type: AWS::CloudWatch::Alarm
  Properties:
    AlarmName: DWH-Error-Rate-High
    MetricName: Errors
    Threshold: 5
    ComparisonOperator: GreaterThanThreshold
```

#### 処理時間監視
```yaml
DurationAlarm:
  Type: AWS::CloudWatch::Alarm
  Properties:
    AlarmName: DWH-Duration-Long
    MetricName: Duration
    Threshold: 300000  # 5分
    ComparisonOperator: GreaterThanThreshold
```

## 運用監視ダッシュボード

### CloudWatch Dashboard

#### 主要メトリクス
- Glueジョブ実行状況
- Lambda関数実行状況
- Step Functions実行状況
- エラー発生状況

#### 可視化項目
- 実行回数（時系列）
- 成功率（パーセンテージ）
- 平均実行時間（時系列）
- エラー分布（円グラフ）

## ログ保持・アーカイブ

### 保持期間設定
```yaml
LogGroup:
  Type: AWS::Logs::LogGroup
  Properties:
    LogGroupName: /aws/glue/jobs/DWH_JB_FILE_LOAD
    RetentionInDays: 30  # 30日間保持
```

### アーカイブ戦略
- **短期**: CloudWatch Logs（30日）
- **中期**: S3（1年）
- **長期**: S3 Glacier（7年）

## トラブルシューティング

### ログ検索手順

1. **エラー発生時刻の特定**
2. **関連ジョブネットIDの特定**
3. **エラーメッセージIDの確認**
4. **スタックトレースの分析**
5. **関連ログの横断検索**

### 一般的なエラーパターン

#### ファイル関連エラー
```
E_DWH_JB_FILE_LOAD_001: データ抽出エラー
→ ファイル形式、エンコーディング、パスを確認
```

#### データ整合性エラー
```
E_DWH_JB_FILE_LOAD_002: データ整合性チェックエラー
→ スキーマ定義、必須項目、データ型を確認
```

#### 接続エラー
```
E_DWH_JB_DB_DATA_COPY_005: Redshift接続エラー
→ 認証情報、ネットワーク設定を確認
```

## パフォーマンス監視

### 処理時間分析
- ジョブ別実行時間
- ステップ別処理時間
- ボトルネック特定

### リソース使用量監視
- CPU使用率
- メモリ使用量
- ネットワーク転送量

### スケーラビリティ監視
- 同時実行数
- キュー待機時間
- スループット変化
