# プロジェクト概要

## プロジェクト名
TIS-DLPF-DWH (データレイクプラットフォーム データウェアハウス)

## 目的
このプロジェクトは、複数のシステムやソースからデータを収集し、統合的なデータウェアハウスを構築することを目的としています。主に以下の機能を提供します：

1. 様々なシステムからのデータ取り込み
2. データの変換・加工・統合
3. データの保存と管理
4. データの出力・連携

## 主要機能

### データ取り込み機能
- ファイルベースのデータ取り込み
- データベースからのデータ取り込み
- 定期的なデータ取り込みスケジュール管理

### データ処理機能
- ETL（抽出・変換・ロード）処理
- データクレンジング
- データ変換・マッピング
- データ整合性チェック

### データ出力機能
- ファイル出力
- 他システムへのデータ連携

## ビジネス要件

このデータウェアハウスは以下のビジネス要件を満たすために設計されています：

1. **データ統合**: 複数のシステム（OMS、POS、WMS、MDM、CRMなど）からのデータを統合し、一元管理する
2. **データ品質確保**: データの整合性チェックや変換処理を通じて、高品質なデータを提供する
3. **効率的なデータ処理**: AWS Glueを活用した効率的なETL処理を実現する
4. **柔軟なデータ連携**: 様々なシステムとのデータ連携を可能にする
5. **環境分離**: 開発(dev)、ステージング(stg)、本番(prd)環境の明確な分離

## 主要なデータドメイン

コードベースの分析から、以下の主要なデータドメインが識別されます：

1. **受注管理 (OMS: Order Management System)**
   - 注文データ (order_header, order_detail)
   - 配送データ
   - キャンペーン情報

2. **商品管理 (MDM: Master Data Management)**
   - 商品情報 (commodity_header)
   - カテゴリ情報 (category)
   - 価格情報

3. **在庫管理 (Stock Management)**
   - 在庫データ (stock)
   - 在庫変動履歴
   - 入荷予定情報

4. **店舗管理 (Shop Management)**
   - 店舗マスタ (shop_mst_raw)
   - 店舗分析データ

5. **販売管理 (Sales Management)**
   - 売上データ (sales_record)
   - 売上明細
   - 返品データ

6. **顧客管理 (Customer Management)**
   - 顧客情報
   - 会員データ

## インターフェース定義

プロジェクトでは、多数のインターフェース（IF）が定義されており、それぞれが特定のデータドメインとデータ連携を担当しています。主なインターフェースグループは以下の通りです：

- **IF-OMS**: 受注管理システム関連
- **IF-MDM**: マスターデータ管理関連
- **IF-POS**: POSシステム関連
- **IF-WMS**: 倉庫管理システム関連
- **IF-CRM**: 顧客関係管理システム関連
- **IF-CDP**: 顧客データプラットフォーム関連
- **IF-CMN**: 共通機能関連
- **IF-APP**: アパレル関連

各インターフェースは、特定のデータテーブルとの間でデータの取り込みや出力を行います。
