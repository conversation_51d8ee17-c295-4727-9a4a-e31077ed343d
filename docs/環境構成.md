# 環境構成

## 環境概要

このプロジェクトは、3つの独立した環境で構成されています：

1. **開発環境 (dev)** - 開発・テスト用
2. **ステージング環境 (stg)** - 検証・受入テスト用
3. **本番環境 (prd)** - 実運用用

各環境は独立したAWSアカウントで構成され、同一のリージョン（ap-northeast-1）に展開されています。

## 環境別構成

### 開発環境 (dev)

- **AWSアカウント**: ************
- **目的**: 開発・単体テスト・結合テスト
- **命名規則**: dev-dlpf-*
- **CloudFormationテンプレート**: dwh/cloudformation/01.dev/

#### 主要リソース
- **S3バケット**: 
  - aws-glue-assets-************-ap-northeast-1
  - s3-dev-dlpf-if-************
- **IAMロール**:
  - AWSGlueServiceRole-dev-dlpf-glue-job-dwh
  - role-dev-dlpf-sfn-dwh
  - role-dev-dlpf-s3file-exists-check-dwh
  - role-dev-dlpf-evbr-invoke-lambda

### ステージング環境 (stg)

- **AWSアカウント**: 869935081854
- **目的**: 統合テスト・受入テスト・性能テスト
- **命名規則**: stg-dlpf-*
- **CloudFormationテンプレート**: dwh/cloudformation/02.stg/

#### 主要リソース
- **S3バケット**: 
  - aws-glue-assets-869935081854-ap-northeast-1
  - s3-stg-dlpf-if-869935081854
- **IAMロール**:
  - AWSGlueServiceRole-stg-dlpf-glue-job-dwh
  - role-stg-dlpf-sfn-dwh
  - role-stg-dlpf-s3file-exists-check-dwh
  - role-stg-dlpf-evbr-invoke-lambda

### 本番環境 (prd)

- **AWSアカウント**: 879381279381
- **目的**: 実運用
- **命名規則**: prd-dlpf-*
- **CloudFormationテンプレート**: dwh/cloudformation/03.prd/

#### 主要リソース
- **S3バケット**: 
  - aws-glue-assets-879381279381-ap-northeast-1
  - s3-prd-dlpf-if-879381279381
- **IAMロール**:
  - AWSGlueServiceRole-prd-dlpf-glue-job-dwh
  - role-prd-dlpf-sfn-dwh
  - role-prd-dlpf-s3file-exists-check-dwh
  - role-prd-dlpf-evbr-invoke-lambda

## デプロイメント方法

### CloudFormation

各環境のリソースは、CloudFormationテンプレートを使用して定義・デプロイされています。テンプレートは環境ごとに分離されており、環境固有の設定が含まれています。

#### テンプレート構成
```
dwh/cloudformation/
├── 01.dev/
│   ├── cho-DWH-JB-DB-DATA-COPY.yaml
│   ├── cho-DWH-JB-DB-TO-FILE.yaml
│   ├── cho-DWH-JB-FILE-LOAD.yaml
│   ├── cho-DWH-JN-*.yaml
│   ├── cho-DWH-LM-*.yaml
│   └── cho-DWH-SCHEDULE.yaml
├── 02.stg/
│   └── [同様の構成]
└── 03.prd/
    └── [同様の構成]
```

### スクリプト・設定デプロイ

Glueジョブスクリプトや設定ファイルは、S3バケットにデプロイされます。

#### デプロイパス
```
s3://aws-glue-assets-{account-id}-ap-northeast-1/scripts/dwh/
├── DWH_JB_DB_DATA_COPY.py
├── DWH_JB_DB_TO_FILE.py
├── DWH_JB_FILE_LOAD.py
├── config/
│   ├── common.config
│   └── log_message.config
└── SQL/
    └── [インターフェースごとのSQLファイル]
```

### Lambda関数デプロイ

Lambda関数は、ZIPファイルとしてS3にアップロードされ、CloudFormationからデプロイされます。

```yaml
MyLambdaFunction:
  Type: 'AWS::Lambda::Function'
  Properties:
    FunctionName: DWH_LM_S3_FILE_EXISTS_CHECK
    Handler: lambda_function.lambda_handler
    Runtime: python3.13
    Role: 'arn:aws:iam::************:role/role-dev-dlpf-s3file-exists-check-dwh'
    Code:
      S3Bucket: 'aws-glue-assets-************-ap-northeast-1'
      S3Key: 'scripts/dwh/DWH_LM_S3_FILE_EXISTS_CHECK.zip'
```

## 環境変数・設定管理

### Parameter Store

各環境の設定値は、AWS Systems Manager Parameter Storeに保存されています。

#### 主要パラメータ
- S3_BUCKET_NAME_GLUE
- REDSHIFT_CONNECTION_GLUE
- REDSHIFT_TEMPDIR
- SECRET_NAME
- S3_BUCKET_NAME_PREFIX
- S3_CSV_OUTPUT_PATH
- S3_CSV_BACKUP_PATH
- DWH_UNLOAD_IAM_ROLE

### 設定ファイル

共通設定は、S3に保存された設定ファイルで管理されています。

#### common.config
インターフェース定義、テーブルマッピング、ジョブネット設定などを含みます。

```ini
[IF-OMS-SL-001]
IF_ID=IF-OMS-SL-001
SCHEMA_NAME=dwh_raw
TABLE_NAME=sales_record
IF_GROUP=0
JOBNET_ID=DWH-JN-OMS-SL-001
```

#### log_message.config
ログメッセージ定義を含みます。

```ini
[INFO]
I_DWH_JB_FILE_LOAD_001 = {%%s}ファイル取り込み処理を開始します。
```

## 認証・認可

### IAMロール

各サービスは、最小権限の原則に基づいたIAMロールを使用して実行されます。

#### Glue実行ロール
- AWSGlueServiceRole-{env}-dlpf-glue-job-dwh
- S3、Redshift、Parameter Store、Secrets Managerへのアクセス権限

#### Lambda実行ロール
- role-{env}-dlpf-s3file-exists-check-dwh
- S3、CloudWatch Logsへのアクセス権限

#### Step Functions実行ロール
- role-{env}-dlpf-sfn-dwh
- Glue、Lambda、CloudWatch Logsへのアクセス権限

### Secrets Manager

データベース接続情報は、AWS Secrets Managerで管理されています。

```python
secrets = common.get_secret(secret_name)
redshift_host = secrets["host"]
redshift_user = secrets["username"]
redshift_password = secrets["password"]
redshift_db = secrets["database"]
```

## ネットワーク構成

### VPC

Redshiftクラスターは、VPC内にデプロイされています。Glueジョブは、必要に応じてVPC接続を使用してRedshiftにアクセスします。

### セキュリティグループ

Redshiftクラスターは、特定のセキュリティグループで保護されており、Glueジョブからのアクセスのみを許可しています。

## 監視・ログ

### CloudWatch Logs

各サービスのログは、CloudWatch Logsに集約されています。

#### ロググループ
- /aws/glue/jobs
- /aws/lambda/DWH_LM_*
- /aws/vendedlogs/states/DWH-JN-*

### CloudWatch Metrics

サービスのメトリクスは、CloudWatch Metricsで監視されています。

#### 主要メトリクス
- Glueジョブ実行時間
- Lambdaエラー率
- Step Functions実行状態

## 障害対策・バックアップ

### 自動バックアップ

Redshiftクラスターは、自動スナップショットが有効化されています。

### 障害復旧

Step Functionsは、ジョブの失敗時に再試行ロジックを実装しています。

### データ保全

処理済みファイルは、バックアップディレクトリに保存されます。

```python
# バックアップパス
s3_csv_backup_prefix = common.get_paramter_store_value("S3_CSV_BACKUP_PATH")
```

## 環境間の移行

### コード・設定の昇格

開発環境でテストされたコードと設定は、ステージング環境、本番環境へと順次昇格されます。

### 環境固有設定

環境固有の設定は、CloudFormationテンプレートとParameter Storeで管理されています。
