# ETLプロセス分析

## ETL概要

このプロジェクトのETL（Extract, Transform, Load）プロセスは、AWS Glueを中心とした3つの主要なジョブで構成されています。各ジョブは特定の役割を持ち、データの流れを効率的に処理します。

## ETLジョブ構成

### 1. DWH_JB_FILE_LOAD (ファイル取り込みジョブ)

#### 目的
外部システムから提供されるCSVファイルをRedshiftに取り込む

#### 処理フロー
```
S3 CSVファイル → データ検証 → 変換処理 → Redshift ロード → ファイルバックアップ
```

#### 主要機能

##### データ抽出 (Extract)
```python
# CSVファイル読み込み設定
options = {
    "header": True,
    "enforceSchema": False,
    "delimiter": delimiter_value,
    "quote": quote_value,
    "escape": escape_value,
    "ignoreLeadingWhiteSpace": True,
    "ignoreTrailingWhiteSpace": True,
    "multiLine": True,
    "nullValue": "",
    "schema": structTpyeSchema,
    "columnNameOfCorruptRecord": "_corrupt_record"
}
data_frame = spark.read.format("csv").load(file_path, **options)
```

##### データ変換 (Transform)
- **ヘッダー検証**: 期待されるカラム構成との照合
- **データ型変換**: 設定に基づく適切なデータ型への変換
- **データ検証**: 必須項目チェック、データ整合性チェック
- **マッピング処理**: ソースフィールドからターゲットフィールドへのマッピング

##### データロード (Load)
```python
# Redshiftへのロード
AmazonRedshift_TABLE = glueContext.write_dynamic_frame.from_options(
    frame=AmazonS3_CSV,
    connection_type="redshift",
    connection_options={
        "connectionName": redshift_connection_glue,
        "redshiftTmpDir": redshift_tempdir,
        "useConnectionProperties": "true",
        "dbtable": dbtable,
        "preactions": preactions_sql,
        "postactions": postactions_sql
    },
    transformation_ctx="AmazonRedshift_TABLE",
)
```

#### エラーハンドリング
- データ抽出エラー時のログ出力
- データ整合性チェックエラーの詳細記録
- Redshiftロードエラーの適切な処理

### 2. DWH_JB_DB_DATA_COPY (データベース内コピージョブ)

#### 目的
Redshift内でのデータコピー・変換処理

#### 処理フロー
```
Redshift ソーステーブル → データ変換 → Redshift ターゲットテーブル
```

#### 主要機能

##### データ抽出
```python
# ソーステーブルからのデータ抽出
source_schema = common.get_config_value(job_config_key, "SOURCE_SCHEMA")
source_table = common.get_config_value(job_config_key, "SOURCE_TABLE")
```

##### データ変換
- スキーマ間でのデータ移動
- 必要に応じたデータ変換処理
- ビジネスルールの適用

##### データロード
```python
# ターゲットテーブルへのロード
target_schema = common.get_config_value(job_config_key, "TARGET_SCHEMA")
target_table = common.get_config_value(job_config_key, "TARGET_TABLE")
```

### 3. DWH_JB_DB_TO_FILE (ファイル出力ジョブ)

#### 目的
Redshiftからデータを抽出してCSVファイルとして出力

#### 処理フロー
```
Redshift → UNLOAD処理 → S3 CSVファイル → ファイルマージ → 最終出力
```

#### 主要機能

##### データ抽出
```python
# Redshiftからのデータ抽出（UNLOAD）
unload_sql = f"""
UNLOAD ('{query_sql}')
TO 's3://{s3_bucket}/{output_path}'
IAM_ROLE '{iam_role}'
CSV HEADER
PARALLEL OFF
"""
```

##### ファイル処理
- 複数ファイルのマージ処理
- ヘッダー行の適切な処理
- ファイル形式の統一

##### バックアップ処理
- 既存ファイルのバックアップ
- 新しいファイルの配置

## データ変換ロジック

### マッピング設定

各インターフェースのマッピング情報は、設定ファイルで管理されています：

```ini
MAPPING_INFO={order_no\string\order_no\varchar};{shop_code\string\shop_code\varchar};{order_datetime\string\order_datetime\timestamp}
```

### データ型変換

#### 文字列から日付への変換
```sql
CAST(NULLIF(sales_record_work.shipping_date, '') AS DATE)
```

#### 文字列からタイムスタンプへの変換
```sql
CAST(NULLIF(sales_record_work.created_datetime, '') AS TIMESTAMP)
```

#### NULL値処理
```sql
NULLIF(field_value, '')  -- 空文字列をNULLに変換
```

### MERGE処理

データの更新は、MERGE文を使用したUPSERT処理で実装されています：

```sql
MERGE INTO dwh_raw.sales_record USING dwh_raw.sales_record_work
ON sales_record.earnings_jisseki_no = sales_record_work.earnings_jisseki_no
WHEN MATCHED THEN UPDATE SET
    -- 更新処理
WHEN NOT MATCHED THEN INSERT VALUES (
    -- 挿入処理
);
```

## ワークフロー管理

### Step Functions

各ETLジョブは、AWS Step Functionsによってオーケストレーションされています：

#### ファイル取り込みワークフロー
```json
{
  "StartAt": "ファイル存在チェック",
  "States": {
    "ファイル存在チェック": {
      "Type": "Task",
      "Resource": "arn:aws:lambda:...:function:DWH_LM_S3_FILE_EXISTS_CHECK",
      "Next": "Glue Job実行"
    },
    "Glue Job実行": {
      "Type": "Task",
      "Resource": "arn:aws:states:::glue:startJobRun.sync",
      "Parameters": {
        "JobName": "DWH_JB_FILE_LOAD"
      }
    }
  }
}
```

#### データ出力ワークフロー
```json
{
  "StartAt": "Run Glue Job",
  "States": {
    "Run Glue Job": {
      "Type": "Task",
      "Resource": "arn:aws:states:::glue:startJobRun.sync",
      "Parameters": {
        "JobName": "DWH_JB_DB_TO_FILE"
      }
    }
  }
}
```

### スケジューリング

EventBridgeによる定期実行：

```yaml
DWH-EB-OMS-SL-001:
  ScheduleExpression: cron(30 15 * * ? *)  # 毎日15:30実行
```

## データ品質管理

### データ検証

#### ヘッダー検証
```python
# 期待されるヘッダーとの照合
expected_headers = mapping_info.keys()
actual_headers = df.columns
if set(expected_headers) != set(actual_headers):
    raise ValueError("ヘッダー不一致")
```

#### データ整合性チェック
```python
# 必須項目チェック
primary_key_fields = eval(primary_key)
for field in primary_key_fields:
    if data_frame.filter(col(field).isNull()).count() > 0:
        raise ValueError(f"必須項目{field}にNULLが含まれています")
```

#### 破損レコード検出
```python
# 破損レコードのチェック
corrupt_count = data_frame.filter(col("_corrupt_record").isNotNull()).count()
if corrupt_count > 0:
    raise ValueError(f"破損レコードが{corrupt_count}件検出されました")
```

### エラーハンドリング

#### 段階的エラー処理
1. **データ抽出エラー**: ファイル読み込み失敗
2. **データ検証エラー**: スキーマ不一致、データ不整合
3. **データロードエラー**: Redshift接続・ロード失敗

#### ログ出力
```python
# エラーログの出力
logger.error("E_DWH_JB_FILE_LOAD_001", msg_values=(if_id, file_path))
```

## パフォーマンス最適化

### Spark設定
```yaml
DefaultArguments:
  "--enable-metrics": "true"
  "--enable-observability-metrics": "true"
  "--enable-continuous-cloudwatch-log": "true"
  "--enable-glue-datacatalog": "true"
```

### Redshift最適化

#### UNLOAD並列処理
```sql
UNLOAD (query)
TO 's3://bucket/path'
IAM_ROLE 'role'
CSV HEADER
PARALLEL OFF  -- 単一ファイル出力の場合
```

#### 一時ディレクトリ使用
```python
"redshiftTmpDir": redshift_tempdir  # S3一時領域の使用
```

## 監視・ログ

### 処理状況監視
- Glueジョブ実行時間
- データ処理件数
- エラー発生率

### ログレベル
- **INFO**: 処理開始・終了
- **WARN**: 警告事項
- **ERROR**: エラー詳細

### メトリクス
- 処理時間
- スループット
- エラー率

## ファイル管理

### バックアップ処理
```python
# 処理済みファイルのバックアップ
backup_path = f"{s3_csv_backup_prefix}/{if_id}/"
s3_client.copy_object(
    CopySource={'Bucket': source_bucket, 'Key': source_key},
    Bucket=target_bucket,
    Key=backup_key
)
```

### ファイル削除
```python
# 処理完了後のファイル削除
s3_client.delete_object(Bucket=bucket, Key=key)
```

## 設定管理

### インターフェース設定
```ini
[IF-OMS-SL-001]
IF_ID=IF-OMS-SL-001
SCHEMA_NAME=dwh_raw
TABLE_NAME=sales_record
JOBNET_ID=DWH-JN-OMS-SL-001
MAPPING_INFO={field1\type1\target1\targettype1};{field2\type2\target2\targettype2}
PRIMARY_KEY=[field1,field2]
```

### 動的設定読み込み
```python
# S3から設定ファイル読み込み
comm_def_obj = s3_client.get_object(
    Bucket=s3BucketNameGlue, 
    Key="scripts/dwh/config/common.config"
)
config.read_string(comm_def_obj["Body"].read().decode())
```
