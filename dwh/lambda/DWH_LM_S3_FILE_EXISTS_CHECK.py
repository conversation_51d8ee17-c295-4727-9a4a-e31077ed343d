import os  # 環境変数の取得やファイル操作に使用
import boto3  # AWS SDK、S3とのやり取りに使用
from datetime import datetime,timezone, timedelta
import logging

# loggerの初期化
logger = logging.getLogger()
logger.setLevel("INFO")

# S3 クライアントの初期化
s3_client = boto3.client('s3')

# S3フォルダプレフィックス
s3_path_prefix = "input-output/DLPF_DWH"

# TimezoneをJSTにする
jst = timezone(timedelta(hours=9))

def logformatter(log_level, jobnet_id, log_number):
    # ログ出力日時
    log_datetime = datetime.now(jst).strftime('%Y-%m-%d %H:%M:%S')
    # 設定ファイル作るまでもなさそうなメッセージdict
    log_message_dict={
        "I_DWH_LM_S3_FILE_EXISTS_CHECK_001":
            f"[{log_datetime}][INFO][{jobnet_id}][I_DWH_LM_S3_FILE_EXISTS_CHECK_001]処理を開始します。",
        "I_DWH_LM_S3_FILE_EXISTS_CHECK_002":
            f"[{log_datetime}][INFO][{jobnet_id}][I_DWH_LM_S3_FILE_EXISTS_CHECK_002]処理が正常終了しました。",
        "W_DWH_LM_S3_FILE_EXISTS_CHECK_001":
            f"[{log_datetime}][WARN][{jobnet_id}][W_DWH_LM_S3_FILE_EXISTS_CHECK_001]ディレクトリ名フォーマットに合わないディレクトリが存在します。",
        "W_DWH_LM_S3_FILE_EXISTS_CHECK_002":
            f"[{log_datetime}][ERROR][{jobnet_id}][W_DWH_LM_S3_FILE_EXISTS_CHECK_002]ファイル名フォーマットに合わないファイルが存在します。",
        "E_DWH_LM_S3_FILE_EXISTS_CHECK_001":
            f"[{log_datetime}][RRROR][{jobnet_id}][E_DWH_LM_S3_FILE_EXISTS_CHECK_001]今回処理可能なファイルの一部または全部が存在しないか、例外が発生しました",
        "E_DWH_LM_S3_FILE_EXISTS_CHECK_002":
            f"[{log_datetime}][RRROR][{jobnet_id}][E_DWH_LM_S3_FILE_EXISTS_CHECK_002]例外が発生しました",        
    }
    log_id = log_level[0] + "_DWH_LM_S3_FILE_EXISTS_CHECK_" + log_number

    return log_message_dict[log_id]

"""
対象IF_IDのフォルダ名の一部datetimeに対応した文字列から一番古い日時のフォルダを特定し、その配下のCSVファイルパスを返す
    - 対象IFの一番古いフォルダを探索する
    - 現在日時より未来の日時が含まれるフォルダは対象から除外する
    bucket_name: 対象バケット名
    ifid: 対象となるIFのID
    specified_date: 手動実行時オプション（日時を指定して実行する場合）、デフォルト空白文字
"""
def get_oldest_folder(bucket_name, ifid, jobnet_id, specified_date=""):
    prefix = f"{s3_path_prefix}/{ifid}_{specified_date}"

    # 指定したプレフィックスと区切り文字で、S3バケット内のフォルダリストを取得
    response = s3_client.list_objects_v2(Bucket=bucket_name, Prefix=prefix, Delimiter='/') 
    
    # フォルダがなければログに出力し、Noneを返す
    if 'CommonPrefixes' not in response:
        folder_not_exists_message=logformatter("ERROR", jobnet_id, "002")
        logger.error(f"{folder_not_exists_message}（対象のフォルダが存在しません。）")
        return None
    
    folders = response['CommonPrefixes']  # フォルダのリストを取得
    
    # 有効なフォルダを抽出し、日付順にソート
    valid_folders = []
    now_jst = datetime.now(jst).replace(tzinfo=None)

    for folder in folders:
        folder_name = folder['Prefix'].split('/')[-2]  # フォルダ名を取得
        try:
            # フォルダ名から日付部分を抽出し、datetime型に変換
            # IFフォルダの日時部は処理日時より未来の場合もあるため、こちらは日時での比較とする
            folder_date = datetime.strptime(folder_name.split('_')[1], '%Y%m%d%H%M%S')

            # 対象日時が現在時刻より未来であればvalid_folderにappendせずcontinueしスキップ
            if now_jst < folder_date:
                overdate_folder = logformatter("WARN", jobnet_id, "001")
                logger.warning(f"{overdate_folder}（{folder_name}）")
                continue

            valid_folders.append((ifid, folder_name, folder_date.strftime("%Y%m%d")))  # 有効なフォルダと日付を保存
        
        except ValueError as e:
            caught_value_exception = logformatter("ERROR", jobnet_id, "002")
            log.error(f"{caught_value_exception}（{e}）")
            continue  # 日付変換に失敗した場合はスキップ
    
    # 日付順にソートして最も古いフォルダを返す
    if valid_folders:
        valid_folders.sort(key=lambda x: x[1])  # 日付順にソート
        print(f"valid_folders={valid_folders[0]}")
        return valid_folders  # 最も古いフォルダを返す
    return None  # 有効なフォルダがない場合はNoneを返す

def lambda_handler(event, context):
    # 環境変数からS3バケット名を取得
    bucket_name = os.getenv('S3_BUCKET_NAME')  # 環境変数からS3バケット名を取得
    
    # イベントからif_idsとdateを取得
    if_ids = event.get("if_ids", [])  # if_idsリストを取得（存在しない場合は空リスト）
    target_date = event.get("target_date", "")  # 日付を取得（存在しない場合は空文字）
    # ログメッセージ用のジョブネットIDを取得
    jobnet_id = event.get("jobnet_id", "") 

    # 結果を格納する辞書を初期化
    result = {"if_ids": [], "file_path": [], "target_date": [], "status": []}

    # 2.4.2.1　開始ログを出力する
    start_processing_message = logformatter("INFO", jobnet_id, "001")
    logger.info(f"{start_processing_message}")
    
    # if_idsリストの各idに対して処理を実行
    for ifid in if_ids:
        # 最も古いフォルダを取得
        oldest_folder = get_oldest_folder(bucket_name, ifid, jobnet_id, target_date)

        if oldest_folder:
            # 対応するCSVファイルのS3パスを構築
            file_path = f"s3://{bucket_name}/{s3_path_prefix}/{oldest_folder[0][1]}/{ifid}.csv"  # ファイルパス
            key_path = f"{s3_path_prefix}/{oldest_folder[0][1]}/{ifid}.csv"  # S3キーのパス
            compare_date = oldest_folder[0][2]
            
            # CSVファイルの存在チェック
            try:
                s3_client.head_object(Bucket=bucket_name, Key=key_path)  # ファイルのヘッダー情報を取得
                result["if_ids"].append(ifid)  # ファイルが存在する場合、idを結果に追加
                result["file_path"].append(file_path)  # ファイルパスを結果に追加
                result["target_date"].append(compare_date) # 日付比較用日時を追加
            except s3_client.exceptions.ClientError as e:
                # ファイルが存在しない場合はエラーを表示し、ループを継続
                file_not_found_message = logformatter("WARN", jobnet_id, "002")
                logger.warning(f"{file_not_found_message}（{e}）")
                continue

    # ファイルが1つも見つからなかった場合
    if not result["file_path"]:  
        result["status"] = 1  # ファイル存在しない

    # 必要なIFがそろってなかった場合
    elif if_ids != result["if_ids"]:
        result.clear() # 内容をクリアしてステータスのみ返す
        result["status"] = 1
    
    # IF毎の日付が違うかどうか判定して、違っていればステータス１をresultにセットする
    elif len(set(result["target_date"])) != 1:
        different_date_each_if = logformatter("WARN", jobnet_id, "001")
        logger.warning(f"{different_date_each_if}")
        result.clear() # 内容をクリアしてステータスのみ返す
        result["status"] = 1 

    else:
        result["status"] = 0  # ファイル存在する
    
    # ステータスが0の場合、正常終了メッセージをログに出力する
    if result["status"] == 0:
        normal_end_message = logformatter("INFO", jobnet_id, "002")
        logger.info(f"{normal_end_message}")

    return result  # 結果を返す
