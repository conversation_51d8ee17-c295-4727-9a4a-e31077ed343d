import json
import boto3
import logging
import datetime

logger = logging.getLogger()
logger.setLevel("INFO")


def logformatter(log_level, jobnet_id, log_number):
    # ログ出力日時
    log_datetime = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    # 設定ファイル作るまでもなさそうなメッセージdict
    log_message_dict = {
        "I_duplicate_event_001": f"[{log_datetime}][INFO][{jobnet_id}][I_duplicate_event_001]処理を開始します。",
        "I_duplicate_event_002": f"[{log_datetime}][INFO][{jobnet_id}][I_duplicate_event_002]処理が正常終了しました。",
        "W_duplicate_event_001": f"[{log_datetime}][WARN][{jobnet_id}][W_duplicate_event_001]重複起動を防止しました。",
        "E_duplicate_event_001": f"[{log_datetime}][ERROR][{jobnet_id}][E_duplicate_event_001]異常終了しました。",
        "E_duplicate_event_002": f"[{log_datetime}][RRROR][{jobnet_id}][E_duplicate_event_002]例外が発生しました。",
    }
    log_id = log_level[0] + "_duplicate_event_" + log_number

    return log_message_dict[log_id]


"""
Lambda Handler
"""


def lambda_handler(event, context):
    # 値の取得他
    stepfunctions = boto3.client("stepfunctions")
    statemachine_arn = event["statemachine_arn"]
    jobnet_id = event["jobnet_id"]
    convertTime = event["time"].replace(":", "-")

    # 処理開始
    log_processing_start = logformatter("INFO", jobnet_id, "001")
    logger.info(f"{log_processing_start}")

    # stepFunctionsのnameを生成
    executeName = convertTime + jobnet_id

    # sstepFunctionsの起動
    try:
        response = stepfunctions.start_execution(
            stateMachineArn=statemachine_arn, name=executeName, input=json.dumps(event)
        )

    # 重複起動した場合、ログメッセージを出力し、ステータス200を返す
    except stepfunctions.exceptions.ExecutionAlreadyExists:
        # 重複起動をログに出力しつつ、正常終了もログに出力する
        duplicated_raunch_message = logformatter("WARN", jobnet_id, "001")
        logger.info(f"{duplicated_raunch_message}")
        normal_processing_end_message = logformatter("INFO", jobnet_id, "002")
        logger.info(f"{normal_processing_end_message}")
        return {"statusCode": 200, "body": f"{duplicated_raunch_message}"}
    # 重複起動以外の例外
    except stepfunctions.exceptions.ClientError as e:
        other_exeption_message = logformatter("ERROR", jobnet_id, "002")
        logger.error(f"{other_exeption_message}（{e}）")
        raise e

    else:
        normal_processing_end_message = logformatter("INFO", jobnet_id, "002")
        logger.info(f"{normal_processing_end_message}")
        return {"statusCode": 200, "body": f"{normal_processing_end_message}"}
