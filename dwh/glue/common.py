#  共通コンポーネント
import boto3
import configparser
import base64
from botocore.exceptions import ClientError
import json


# 共通部品
class common:
    def __init__(self):
        """
        Method:
            共通部品Commonのコンストラクタ
        Args:
            null
        """
        try:
            # AWS サービスのS3_Clientインスタンス作成処理
            self.__s3_client = self.create_aws_client("s3")
            # AWS サービスのSSM_Clientインスタンス作成処理
            self.__ssm_client = self.create_aws_client("ssm")
            self.__secret_manager_client = self.create_aws_client("secretsmanager")
            # パラメータストアの取得
            self.s3BucketNameGlue = self.__ssm_client.get_parameter(
                Name="S3_BUCKET_NAME_GLUE"
            )["Parameter"]["Value"]
            # 共通定義configファイルの取得
            self.comm_def_file = configparser.ConfigParser()
            comm_def_obj = self.__s3_client.get_object(
                Bucket=self.s3BucketNameGlue, Key="scripts/dwh/config/common.config"
            )
            self.comm_def_file.read_string(comm_def_obj["Body"].read().decode())
        except:
            print("ERROR:共通定義ファイルの読込が失敗しました。")
            raise

    def create_aws_client(self, service_name):
        """
        Method:
            AWS サービスのClientインスタンス作成処理
        Args:
            service_name: サービス名
        Return:
            AWS サービスのClientインスタンス
        """
        return boto3.client(service_name)

    def get_paramter_store_value(self, name):
        """
        Method:
            パラメータストアから値を取得(Glue用)
        Args:
            name: パラメータの項目値
        Return:
            パラメータの項目値(Glue利用)
        """
        return self.__ssm_client.get_parameter(Name=name)["Parameter"]["Value"]

    # Secrets Managerから接続情報を取得
    def get_secret(self, secret_name, region_name="ap-northeast-1"):
        # Secrets Managerクライアントを作成
        session = boto3.session.Session()
        client = session.client(service_name="secretsmanager", region_name=region_name)

        try:
            # シークレットを取得
            get_secret_value_response = client.get_secret_value(SecretId=secret_name)

            # シークレットが文字列である場合
            if "SecretString" in get_secret_value_response:
                secret = get_secret_value_response["SecretString"]
                return json.loads(secret)

            # バイナリである場合
            else:
                decoded_binary_secret = base64.b64decode(
                    get_secret_value_response["SecretBinary"]
                )
                return json.loads(decoded_binary_secret)

        except ClientError as e:
            raise e

    def get_config_value(self, job_id, key):
        """
        Method:
            コンフィグから値を取得(Glue用)
        Args:
            job_id: ジョブID
            key: コンフィグの項目キー
        Return:
            コンフィグの項目値
        """
        job_config_key = job_id.upper()
        return self.comm_def_file.get(job_config_key, key)

    def get_sql(self, job_id, pattern):
        """
        Method:
            クエリSQL(L3)を取得
        Args:
            job_id: ジョブID
            pattern: 全件はselect, 差分更新はpreaction、postaction
        Return:
            クエリSQL(L3)
        """
        job_config_key = job_id.upper()
        sqlQueryPath = self.get_config_value(job_config_key, "IF_ID")

        comm_def_obj = self.__s3_client.get_object(
            Bucket=self.s3BucketNameGlue,
            Key="SQL/" + sqlQueryPath + "/" + pattern + ".sql",
        )
        return comm_def_obj["Body"].read().decode()
