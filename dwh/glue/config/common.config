[IF-OMS-SL-001]
IF_ID=IF-OMS-SL-001
SCHEMA_NAME=dwh_raw
TABLE_NAME=sales_record
IF_GROUP=0
JOBNET_ID=DWH-JN-OMS-SL-001
MAPPING_INFO={earnings_jisseki_no\string\earnings_jisseki_no\decimal(15,0)};{order_henpin_kbn\string\order_henpin_kbn\varchar};{sales_detail_kbn\string\sales_detail_kbn\varchar};{shipping_no\string\shipping_no\varchar};{shipping_detail_no\string\shipping_detail_no\decimal(16,0)};{order_no\string\order_no\varchar};{order_detail_no\string\order_detail_no\decimal(16,0)};{shipping_date\string\shipping_date\varchar};{sales_recording_date\string\sales_recording_date\varchar};{marketing_channel\string\marketing_channel\varchar};{ext_payment_method_type\string\ext_payment_method_type\varchar};{shop_code\string\shop_code\varchar};{main_product_no\string\main_product_no\varchar};{product_no\string\product_no\varchar};{period_flg\string\period_flg\varchar};{baitai_code\string\baitai_code\varchar};{commodity_code\string\commodity_code\varchar};{hinban_code\string\hinban_code\varchar};{commodity_name\string\commodity_name\varchar};{commodity_kind\string\commodity_kind\varchar};{commodity_group\string\commodity_group\varchar};{commodity_series\string\commodity_series\varchar};{commodity_segment\string\commodity_segment\varchar};{business_segment\string\business_segment\varchar};{parent_commodity_code\string\parent_commodity_code\varchar};{commodity_amount\string\commodity_amount\decimal(8,0)};{campaign_instructions_code\string\campaign_instructions_code\varchar};{coupon_management_code\string\coupon_management_code\varchar};{incurred_price\string\incurred_price\decimal(8,0)};{tax_group_code\string\tax_group_code\varchar};{tax_no\string\tax_no\decimal(3,0)};{tax_rate\string\tax_rate\decimal(3,0)};{commodity_tax_type\string\commodity_tax_type\decimal(1,0)};{business_partner_code\string\business_partner_code\varchar};{sales_bumon_cd\string\sales_bumon_cd\varchar};{channel_cd\string\channel_cd\varchar};{sales_link_status\string\sales_link_status\decimal(1,0)};{sales_link_datetime\string\sales_link_datetime\varchar};{orm_rowid\string\orm_rowid\decimal(38,0)};{created_user\string\created_user\varchar};{created_datetime\string\created_datetime\varchar};{updated_user\string\updated_user\varchar};{updated_datetime\string\updated_datetime\varchar}
PRIMARY_KEY=[earnings_jisseki_no,order_henpin_kbn,sales_detail_kbn,shipping_date,sales_recording_date,marketing_channel,incurred_price,sales_link_status,orm_rowid,created_user,created_datetime,updated_user,updated_datetime]
[IF-POS-SL-001]
IF_ID=IF-POS-SL-001
SCHEMA_NAME=dwh_raw
TABLE_NAME=pos_value_discount_relation
IF_GROUP=0
JOBNET_ID=DWH-JN-POS-SL-001
MAPPING_INFO={shop_cd\string\shop_cd\varchar};{register_num\string\register_num\decimal(10,0)};{business_date\string\business_date\varchar};{receipt_num\string\receipt_num\decimal(10,0)};{line_num\string\line_num\decimal(10,0)};{target_line_num\string\target_line_num\decimal(10,0)};{discount_cd\string\discount_cd\varchar};{discount_kind\string\discount_kind\decimal(5,0)};{disc_rate\string\disc_rate\decimal(5,0)};{minus_amount\string\minus_amount\decimal(10,0)};{tax_inclusive\string\tax_inclusive\decimal(10,0)};{ins_biz_date\string\ins_biz_date\varchar};{upd_biz_date\string\upd_biz_date\varchar};{ins_date\string\ins_date\varchar};{upd_date\string\upd_date\varchar};{ins_user_id\string\ins_user_id\varchar};{upd_user_id\string\upd_user_id\varchar};{ins_pgm_id\string\ins_pgm_id\varchar};{upd_pgm_id\string\upd_pgm_id\varchar}
PRIMARY_KEY=[shop_cd,register_num,business_date,receipt_num,line_num,target_line_num,discount_cd,discount_kind,disc_rate,minus_amount,ins_date,upd_date,ins_user_id,upd_user_id,ins_pgm_id,upd_pgm_id]
[IF-CRM-ME-001]
IF_ID=IF-CRM-ME-001
SCHEMA_NAME=dwh_raw
TABLE_NAME=member
IF_GROUP=0
JOBNET_ID=DWH-JN-CRM-ME-001
MAPPING_INFO={id\string\id\varchar};{isdeleted\string\isdeleted\varchar};{masterrecordid\string\masterrecordid\varchar};{name\string\name\varchar};{lastname\string\lastname\varchar};{firstname\string\firstname\varchar};{salutation\string\salutation\varchar};{type\string\type\varchar};{recordtypeid\string\recordtypeid\varchar};{parentid\string\parentid\varchar};{personmailingstreet\string\personmailingstreet\varchar};{personmailingcity\string\personmailingcity\varchar};{personmailingstate\string\personmailingstate\varchar};{personmailingstatecode\string\personmailingstatecode\varchar};{personmailingpostalcode\string\personmailingpostalcode\varchar};{personmailingcountry\string\personmailingcountry\varchar};{personmailinglatitude\string\personmailinglatitude\decimal(18,0)};{personmailinglongitude\string\personmailinglongitude\decimal(18,0)};{personmailinggeocodeaccuracy\string\personmailinggeocodeaccuracy\varchar};{personmailingaddress\string\personmailingaddress\varchar};{shippingstreet\string\shippingstreet\varchar};{shippingcity\string\shippingcity\varchar};{shippingstate\string\shippingstate\varchar};{shippingpostalcode\string\shippingpostalcode\varchar};{shippingcountry\string\shippingcountry\varchar};{shippinglatitude\string\shippinglatitude\decimal(18,0)};{shippinglongitude\string\shippinglongitude\decimal(18,0)};{shippinggeocodeaccuracy\string\shippinggeocodeaccuracy\varchar};{shippingaddress\string\shippingaddress\varchar};{phone\string\phone\varchar};{fax\string\fax\varchar};{accountnumber\string\accountnumber\varchar};{website\string\website\varchar};{photourl\string\photourl\varchar};{sic\string\sic\varchar};{industry\string\industry\varchar};{annualrevenue\string\annualrevenue\decimal(15,0)};{numberofemployees\string\numberofemployees\decimal(18,0)};{ownership\string\ownership\varchar};{tickersymbol\string\tickersymbol\varchar};{description\string\description\varchar};{rating\string\rating\varchar};{site\string\site\varchar};{ownerid\string\ownerid\varchar};{createddate\string\createddate\varchar};{createdbyid\string\createdbyid\varchar};{lastmodifieddate\string\lastmodifieddate\varchar};{lastmodifiedbyid\string\lastmodifiedbyid\varchar};{systemmodstamp\string\systemmodstamp\varchar};{lastactivitydate\string\lastactivitydate\varchar};{lastvieweddate\string\lastvieweddate\varchar};{lastreferenceddate\string\lastreferenceddate\varchar};{ispersonaccount\string\ispersonaccount\varchar};{billingstreet\string\billingstreet\varchar};{billingcity\string\billingcity\varchar};{billingstate\string\billingstate\varchar};{billingpostalcode\string\billingpostalcode\varchar};{billingcountry\string\billingcountry\varchar};{billinglatitude\string\billinglatitude\decimal(18,0)};{billinglongitude\string\billinglongitude\decimal(18,0)};{billinggeocodeaccuracy\string\billinggeocodeaccuracy\varchar};{billingaddress\string\billingaddress\varchar};{personotherstreet\string\personotherstreet\varchar};{personothercity\string\personothercity\varchar};{personotherstate\string\personotherstate\varchar};{personotherpostalcode\string\personotherpostalcode\varchar};{personothercountry\string\personothercountry\varchar};{personotherlatitude\string\personotherlatitude\decimal(18,0)};{personotherlongitude\string\personotherlongitude\decimal(18,0)};{personothergeocodeaccuracy\string\personothergeocodeaccuracy\varchar};{personotheraddress\string\personotheraddress\varchar};{personmobilephone\string\personmobilephone\varchar};{personotherphone\string\personotherphone\varchar};{personassistantphone\string\personassistantphone\varchar};{personemail\string\personemail\varchar};{persontitle\string\persontitle\varchar};{persondepartment\string\persondepartment\varchar};{personassistantname\string\personassistantname\varchar};{personleadsource\string\personleadsource\varchar};{personbirthdate\string\personbirthdate\varchar};{personhasoptedoutofemail\string\personhasoptedoutofemail\varchar};{personhasoptedoutoffax\string\personhasoptedoutoffax\varchar};{persondonotcall\string\persondonotcall\varchar};{personlastcurequestdate\string\personlastcurequestdate\varchar};{personlastcuupdatedate\string\personlastcuupdatedate\varchar};{personemailbouncedreason\string\personemailbouncedreason\varchar};{personemailbounceddate\string\personemailbounceddate\varchar};{personindividualid\string\personindividualid\varchar};{personpronouns\string\personpronouns\varchar};{persongenderidentity\string\persongenderidentity\varchar};{jigsaw\string\jigsaw\varchar};{jigsawcompanyid\string\jigsawcompanyid\varchar};{accountsource\string\accountsource\varchar};{sicdesc\string\sicdesc\varchar};{gender__c\string\gender__c\varchar};{number__c\string\number__c\varchar};{preferredshipmentservice__c\string\preferredshipmentservice__c\varchar};{accountcloseddate__c\string\accountcloseddate__c\varchar};{accountclosedreason__c\string\accountclosedreason__c\varchar};{shopcardbarcode__c\string\shopcardbarcode__c\varchar};{personmailingaddress__c\string\personmailingaddress__c\varchar};{isemployee__c\string\isemployee__c\varchar};{isoptedinemalmagazine__c\string\isoptedinemalmagazine__c\varchar};{emailmagazineunsubscribeddate__c\string\emailmagazineunsubscribeddate__c\varchar};{emailmagazinesubscribeddate__c\string\emailmagazinesubscribeddate__c\varchar};{beautycatalogsendtype__c\string\beautycatalogsendtype__c\varchar};{healthcatalogsendtype__c\string\healthcatalogsendtype__c\varchar};{apparelcatalogsendtype__c\string\apparelcatalogsendtype__c\varchar};{medicinecatalogsendtype__c\string\medicinecatalogsendtype__c\varchar};{petcatalogsendtype__c\string\petcatalogsendtype__c\varchar};{faxpurchaseordersendtype__c\string\faxpurchaseordersendtype__c\varchar};{lastnamekana__c\string\lastnamekana__c\varchar};{firstnamekana__c\string\firstnamekana__c\varchar};{rank__c\string\rank__c\varchar};{source__c\string\source__c\varchar};{status__c\string\status__c\varchar};{memo__c\string\memo__c\varchar};{memoforstore__c\string\memoforstore__c\varchar};{isdhccreditcardowner__c\string\isdhccreditcardowner__c\varchar};{age__c\string\age__c\decimal(18,0)};{isoptedindm__c\string\isoptedindm__c\varchar};{isoptedincatalog__c\string\isoptedincatalog__c\varchar};{isunmailablepostway__c\string\isunmailablepostway__c\varchar};{isunmailablepost__c\string\isunmailablepost__c\varchar};{isstoporder__c\string\isstoporder__c\varchar};{isordermonitoring__c\string\isordermonitoring__c\varchar};{isrequiredcaution__c\string\isrequiredcaution__c\varchar};{guestorderonly__c\string\guestorderonly__c\varchar};{customernumber__c\string\customernumber__c\varchar};{isoptedinsurvey__c\string\isoptedinsurvey__c\varchar};{margedaccountid__c\string\margedaccountid__c\varchar};{rankexpirydate__c\string\rankexpirydate__c\varchar};{preferredcontactway__c\string\preferredcontactway__c\varchar};{lineminiappuserid__c\string\lineminiappuserid__c\varchar};{namekana__c\string\namekana__c\varchar};{birthdate__c\string\birthdate__c\varchar};{emailmagazineoptedouturl__c\string\emailmagazineoptedouturl__c\varchar};{personemail__c\string\personemail__c\varchar};{optinstoreemailmagazinestatus__c\string\optinstoreemailmagazinestatus__c\varchar};{storeemailmagazineemail__c\string\storeemailmagazineemail__c\varchar};{storeemailmagazineoptedoutkey__c\string\storeemailmagazineoptedoutkey__c\varchar};{storeemailmagazineoptedouturl__c\string\storeemailmagazineoptedouturl__c\varchar};{tonariwaid__c\string\tonariwaid__c\varchar};{storeemailmagazinestorecode__c\string\storeemailmagazinestorecode__c\varchar};{unmailablereason__c\string\unmailablereason__c\varchar}
PRIMARY_KEY=[id,ownerid,createddate,createdbyid,lastmodifieddate,lastmodifiedbyid,systemmodstamp,number__c,customernumber__c]
[IF-OMS-ME-001]
IF_ID=IF-OMS-ME-001
SCHEMA_NAME=dwh_raw
TABLE_NAME=card_info
IF_GROUP=0
JOBNET_ID=DWH-JN-OMS-ME-001
MAPPING_INFO={customer_code\string\customer_code\varchar};{credit_card_kanri_no\string\credit_card_kanri_no\varchar};{credit_card_kanri_detail_no\string\credit_card_kanri_detail_no\varchar};{credit_card_no\string\credit_card_no\varchar};{card_expire_year\string\card_expire_year\varchar};{card_expire_month\string\card_expire_month\varchar};{card_holder\string\card_holder\varchar};{card_brand\string\card_brand\varchar};{default_use_flag\string\default_use_flag\decimal(1,0)};{change_datetime\string\change_datetime\varchar};{change_channel_kbn\string\change_channel_kbn\varchar};{card_keep_ng_flg\string\card_keep_ng_flg\decimal(1,0)};{change_reason_kbn\string\change_reason_kbn\varchar};{change_reason\string\change_reason\varchar};{change_user_code\string\change_user_code\decimal(38,0)};{dhc_card_flg\string\dhc_card_flg\decimal(1,0)};{crm_card_id\string\crm_card_id\varchar};{crm_card_updated_datetime\string\crm_card_updated_datetime\varchar};{orm_rowid\string\orm_rowid\decimal(38,0)};{created_user\string\created_user\varchar};{created_datetime\string\created_datetime\varchar};{updated_user\string\updated_user\varchar};{updated_datetime\string\updated_datetime\varchar}
PRIMARY_KEY=[customer_code,credit_card_kanri_no,credit_card_kanri_detail_no,credit_card_no,card_holder,card_brand,default_use_flag,change_datetime,change_channel_kbn,card_keep_ng_flg,change_reason_kbn,change_reason,dhc_card_flg,orm_rowid,created_user,created_datetime,updated_user,updated_datetime]
[IF-OMS-CP-001]
IF_ID=IF-OMS-CP-001
SCHEMA_NAME=dwh_raw
TABLE_NAME=campaign_instructions
IF_GROUP=CP-IN-001
JOBNET_ID=DWH-JN-OMS-CP-001
MAPPING_INFO={campaign_instructions_code\string\campaign_instructions_code\varchar};{campaign_instructions_name\string\campaign_instructions_name\varchar};{campaign_type\string\campaign_type\varchar};{delete_flg\string\delete_flg\decimal(1,0)};{campaign_priority\string\campaign_priority\decimal(5,0)};{campaign_applied_scope\string\campaign_applied_scope\varchar};{campaign_use_limit\string\campaign_use_limit\varchar};{oneshot_order_limit\string\oneshot_order_limit\decimal(8,0)};{campaign_quantity_limit\string\campaign_quantity_limit\decimal(8,0)};{campaign_start_date\string\campaign_start_date\varchar};{campaign_end_date\string\campaign_end_date\varchar};{present_use_flg\string\present_use_flg\decimal(1,0)};{campaign_customer_flg\string\campaign_customer_flg\decimal(1,0)};{campaign_combi_limit_flg\string\campaign_combi_limit_flg\decimal(1,0)};{permanent_campaign_flg\string\permanent_campaign_flg\decimal(1,0)};{baitai_code\string\baitai_code\varchar};{campaign_description\string\campaign_description\varchar};{change_user_code\string\change_user_code\decimal(38,0)};{orm_rowid\string\orm_rowid\decimal(38,0)};{created_user\string\created_user\varchar};{created_datetime\string\created_datetime\varchar};{updated_user\string\updated_user\varchar};{updated_datetime\string\updated_datetime\varchar}
PRIMARY_KEY=[campaign_instructions_code,campaign_instructions_name,delete_flg,campaign_applied_scope,campaign_use_limit,campaign_start_date,campaign_end_date,present_use_flg,campaign_customer_flg,campaign_combi_limit_flg,permanent_campaign_flg,orm_rowid,created_user,created_datetime,updated_user,updated_datetime]
[IF-OMS-CP-002]
IF_ID=IF-OMS-CP-002
SCHEMA_NAME=dwh_raw
TABLE_NAME=campaign_customer
IF_GROUP=CP-IN-001
JOBNET_ID=DWH-JN-OMS-CP-001
MAPPING_INFO={campaign_instructions_code\string\campaign_instructions_code\varchar};{customer_code\string\customer_code\varchar};{joken_type\string\joken_type\varchar};{orm_rowid\string\orm_rowid\decimal(38,0)};{created_user\string\created_user\varchar};{created_datetime\string\created_datetime\varchar};{updated_user\string\updated_user\varchar};{updated_datetime\string\updated_datetime\varchar}
PRIMARY_KEY=[campaign_instructions_code,customer_code,joken_type,orm_rowid,created_user,created_datetime,updated_user,updated_datetime]
[IF-OMS-CP-003]
IF_ID=IF-OMS-CP-003
SCHEMA_NAME=dwh_raw
TABLE_NAME=campaign_order
IF_GROUP=CP-IN-001
JOBNET_ID=DWH-JN-OMS-CP-001
MAPPING_INFO={campaign_instructions_code\string\campaign_instructions_code\varchar};{campaign_group_no\string\campaign_group_no\decimal(8,0)};{joken_type\string\joken_type\varchar};{campaign_joken_no\string\campaign_joken_no\decimal(8,0)};{joken_kind1\string\joken_kind1\varchar};{joken_kind2\string\joken_kind2\varchar};{joken\string\joken\varchar};{joken_min\string\joken_min\decimal(8,0)};{joken_max\string\joken_max\decimal(8,0)};{regular_kaiji\string\regular_kaiji\decimal(5,0)};{joken_month_num\string\joken_month_num\decimal(3,0)};{commodity_name\string\commodity_name\varchar};{orm_rowid\string\orm_rowid\decimal(38,0)};{created_user\string\created_user\varchar};{created_datetime\string\created_datetime\varchar};{updated_user\string\updated_user\varchar};{updated_datetime\string\updated_datetime\varchar}
PRIMARY_KEY=[campaign_instructions_code,campaign_group_no,joken_type,campaign_joken_no,orm_rowid,created_user,created_datetime,updated_user,updated_datetime]
[IF-OMS-CP-004]
IF_ID=IF-OMS-CP-004
SCHEMA_NAME=dwh_raw
TABLE_NAME=campaign_order_group
IF_GROUP=CP-IN-001
JOBNET_ID=DWH-JN-OMS-CP-001
MAPPING_INFO={campaign_instructions_code\string\campaign_instructions_code\varchar};{campaign_group_no\string\campaign_group_no\decimal(8,0)};{campaign_joken_disp\string\campaign_joken_disp\varchar};{exclude_joken_disp\string\exclude_joken_disp\varchar};{orm_rowid\string\orm_rowid\decimal(38,0)};{created_user\string\created_user\varchar};{created_datetime\string\created_datetime\varchar};{updated_user\string\updated_user\varchar};{updated_datetime\string\updated_datetime\varchar}
PRIMARY_KEY=[campaign_instructions_code,campaign_group_no,orm_rowid,created_user,created_datetime,updated_user,updated_datetime]
[IF-OMS-CP-005]
IF_ID=IF-OMS-CP-005
SCHEMA_NAME=dwh_raw
TABLE_NAME=campaign_instructions_commodity
IF_GROUP=CP-IN-001
JOBNET_ID=DWH-JN-OMS-CP-001
MAPPING_INFO={campaign_instructions_code\string\campaign_instructions_code\varchar};{shop_code\string\shop_code\varchar};{commodity_code\string\commodity_code\varchar};{joken_type\string\joken_type\varchar};{orm_rowid\string\orm_rowid\decimal(38,0)};{created_user\string\created_user\varchar};{created_datetime\string\created_datetime\varchar};{updated_user\string\updated_user\varchar};{updated_datetime\string\updated_datetime\varchar}
PRIMARY_KEY=[campaign_instructions_code,shop_code,commodity_code,joken_type,orm_rowid,created_user,created_datetime,updated_user,updated_datetime]
[IF-OMS-CP-006]
IF_ID=IF-OMS-CP-006
SCHEMA_NAME=dwh_raw
TABLE_NAME=campaign_promotion
IF_GROUP=CP-IN-001
JOBNET_ID=DWH-JN-OMS-CP-001
MAPPING_INFO={campaign_instructions_code\string\campaign_instructions_code\varchar};{promotion_no\string\promotion_no\decimal(9,0)};{promotion_type\string\promotion_type\varchar};{shop_code\string\shop_code\varchar};{commodity_code\string\commodity_code\varchar};{commodity_name\string\commodity_name\varchar};{present_qt\string\present_qt\decimal(3,0)};{discount_rate\string\discount_rate\decimal(3,0)};{discount_amount\string\discount_amount\decimal(8,0)};{discount_retail_price\string\discount_retail_price\decimal(8,0)};{shipping_charge\string\shipping_charge\decimal(8,0)};{orm_rowid\string\orm_rowid\decimal(38,0)};{created_user\string\created_user\varchar};{created_datetime\string\created_datetime\varchar};{updated_user\string\updated_user\varchar};{updated_datetime\string\updated_datetime\varchar}
PRIMARY_KEY=[campaign_instructions_code,promotion_no,promotion_type,orm_rowid,created_user,created_datetime,updated_user,updated_datetime]
[IF-OMS-CP-007]
IF_ID=IF-OMS-CP-007
SCHEMA_NAME=dwh_raw
TABLE_NAME=campaign_combi_limit
IF_GROUP=CP-IN-001
JOBNET_ID=DWH-JN-OMS-CP-001
MAPPING_INFO={campaign_instructions_code\string\campaign_instructions_code\varchar};{campaign_combi_limit_code\string\campaign_combi_limit_code\varchar};{orm_rowid\string\orm_rowid\decimal(38,0)};{created_user\string\created_user\varchar};{created_datetime\string\created_datetime\varchar};{updated_user\string\updated_user\varchar};{updated_datetime\string\updated_datetime\varchar}
PRIMARY_KEY=[campaign_instructions_code,campaign_combi_limit_code,orm_rowid,created_user,created_datetime,updated_user,updated_datetime]
[IF-OMS-CP-008]
IF_ID=IF-OMS-CP-008
SCHEMA_NAME=dwh_raw
TABLE_NAME=campaign_count_customer
IF_GROUP=0
JOBNET_ID=DWH-JN-OMS-CP-002
MAPPING_INFO={campaign_instructions_code\string\campaign_instructions_code\varchar};{customer_code\string\customer_code\varchar};{campaign_used_count\string\campaign_used_count\decimal(8,0)};{orm_rowid\string\orm_rowid\decimal(38,0)};{created_user\string\created_user\varchar};{created_datetime\string\created_datetime\varchar};{updated_user\string\updated_user\varchar};{updated_datetime\string\updated_datetime\varchar}
PRIMARY_KEY=[campaign_instructions_code,customer_code,campaign_used_count,orm_rowid,created_user,created_datetime,updated_user,updated_datetime]
[IF-OMS-CP-009]
IF_ID=IF-OMS-CP-009
SCHEMA_NAME=dwh_raw
TABLE_NAME=campaign_count_commodity
IF_GROUP=0
JOBNET_ID=DWH-JN-OMS-CP-003
MAPPING_INFO={campaign_instructions_code\string\campaign_instructions_code\varchar};{customer_code\string\customer_code\varchar};{shop_code\string\shop_code\varchar};{commodity_code\string\commodity_code\varchar};{campaign_used_count\string\campaign_used_count\decimal(8,0)};{orm_rowid\string\orm_rowid\decimal(38,0)};{created_user\string\created_user\varchar};{created_datetime\string\created_datetime\varchar};{updated_user\string\updated_user\varchar};{updated_datetime\string\updated_datetime\varchar}
PRIMARY_KEY=[campaign_instructions_code,customer_code,shop_code,commodity_code,orm_rowid,created_user,created_datetime,updated_user,updated_datetime]
[IF-OMS-CP-010]
IF_ID=IF-OMS-CP-010
SCHEMA_NAME=dwh_raw
TABLE_NAME=order_campaign_use_amount
IF_GROUP=CP-IN-002
JOBNET_ID=DWH-JN-OMS-CP-004
MAPPING_INFO={order_no\string\order_no\varchar};{tax_group_code\string\tax_group_code\varchar};{tax_no\string\tax_no\decimal(3,0)};{use_code_type\string\use_code_type\varchar};{use_code\string\use_code\varchar};{use_amount\string\use_amount\decimal(8,0)};{tax_rate\string\tax_rate\decimal(3,0)};{orm_rowid\string\orm_rowid\decimal(38,0)};{created_user\string\created_user\varchar};{created_datetime\string\created_datetime\varchar};{updated_user\string\updated_user\varchar};{updated_datetime\string\updated_datetime\varchar}
PRIMARY_KEY=[order_no,tax_group_code,tax_no,use_code_type,use_code,use_amount,tax_rate,orm_rowid,created_user,created_datetime,updated_user,updated_datetime]
[IF-OMS-CP-011]
IF_ID=IF-OMS-CP-011
SCHEMA_NAME=dwh_raw
TABLE_NAME=order_campaign
IF_GROUP=CP-IN-002
JOBNET_ID=DWH-JN-OMS-CP-004
MAPPING_INFO={order_no\string\order_no\varchar};{campaign_instructions_code\string\campaign_instructions_code\varchar};{campaign_instructions_name\string\campaign_instructions_name\varchar};{campaign_description\string\campaign_description\varchar};{campaign_end_date\string\campaign_end_date\varchar};{orm_rowid\string\orm_rowid\decimal(38,0)};{created_user\string\created_user\varchar};{created_datetime\string\created_datetime\varchar};{updated_user\string\updated_user\varchar};{updated_datetime\string\updated_datetime\varchar}
PRIMARY_KEY=[order_no,campaign_instructions_code,campaign_instructions_name,campaign_end_date,orm_rowid,created_user,created_datetime,updated_user,updated_datetime]
[IF-OMS-CP-012]
IF_ID=IF-OMS-CP-012
SCHEMA_NAME=dwh_raw
TABLE_NAME=order_campaign_history
IF_GROUP=CP-IN-002
JOBNET_ID=DWH-JN-OMS-CP-004
MAPPING_INFO={order_history_id\string\order_history_id\decimal(38,0)};{order_no\string\order_no\varchar};{campaign_instructions_code\string\campaign_instructions_code\varchar};{campaign_instructions_name\string\campaign_instructions_name\varchar};{campaign_description\string\campaign_description\varchar};{campaign_end_date\string\campaign_end_date\varchar};{orm_rowid\string\orm_rowid\decimal(38,0)};{created_user\string\created_user\varchar};{created_datetime\string\created_datetime\varchar};{updated_user\string\updated_user\varchar};{updated_datetime\string\updated_datetime\varchar}
PRIMARY_KEY=[order_history_id,order_no,campaign_instructions_code,campaign_instructions_name,campaign_end_date,orm_rowid,created_user,created_datetime,updated_user,updated_datetime]
[IF-OMS-CO-001]
IF_ID=IF-OMS-CO-001
SCHEMA_NAME=dwh_raw
TABLE_NAME=coupon
IF_GROUP=CO-IN-001
JOBNET_ID=DWH-JN-OMS-CO-001
MAPPING_INFO={coupon_management_code\string\coupon_management_code\varchar};{coupon_code\string\coupon_code\varchar};{coupon_name\string\coupon_name\varchar};{coupon_invalid_flag\string\coupon_invalid_flag\decimal(1,0)};{coupon_type\string\coupon_type\decimal(1,0)};{coupon_issue_type\string\coupon_issue_type\decimal(1,0)};{coupon_use_limit\string\coupon_use_limit\decimal(1,0)};{coupon_use_purchase_price\string\coupon_use_purchase_price\decimal(8,0)};{coupon_discount_type\string\coupon_discount_type\decimal(1,0)};{coupon_discount_rate\string\coupon_discount_rate\decimal(3,0)};{coupon_discount_price\string\coupon_discount_price\decimal(8,0)};{coupon_start_datetime\string\coupon_start_datetime\varchar};{coupon_end_datetime\string\coupon_end_datetime\varchar};{coupon_limit_display_period\string\coupon_limit_display_period\decimal(3,0)};{coupon_limit_display\string\coupon_limit_display\varchar};{coupon_description\string\coupon_description\varchar};{coupon_message\string\coupon_message\varchar};{coupon_kbn\string\coupon_kbn\varchar};{coupon_post_in_charge\string\coupon_post_in_charge\varchar};{coupon_commodity_flag\string\coupon_commodity_flag\decimal(1,0)};{marketing_channel_list\string\marketing_channel_list\varchar};{goods_group\string\goods_group\varchar};{commodity_category_code\string\commodity_category_code\varchar};{commodity_series\string\commodity_series\varchar};{orm_rowid\string\orm_rowid\decimal(38,0)};{created_user\string\created_user\varchar};{created_datetime\string\created_datetime\varchar};{updated_user\string\updated_user\varchar};{updated_datetime\string\updated_datetime\varchar}
PRIMARY_KEY=[coupon_management_code,coupon_name,coupon_invalid_flag,coupon_type,coupon_use_limit,coupon_use_purchase_price,coupon_discount_type,coupon_start_datetime,coupon_end_datetime,coupon_post_in_charge,coupon_commodity_flag,orm_rowid,created_user,created_datetime,updated_user,updated_datetime]
[IF-OMS-CO-002]
IF_ID=IF-OMS-CO-002
SCHEMA_NAME=dwh_raw
TABLE_NAME=coupon_customer
IF_GROUP=CO-IN-001
JOBNET_ID=DWH-JN-OMS-CO-001
MAPPING_INFO={coupon_management_code\string\coupon_management_code\varchar};{customer_code\string\customer_code\varchar};{neo_customer_no\string\neo_customer_no\varchar};{coupon_issue_status\string\coupon_issue_status\decimal(1,0)};{coupon_used_count\string\coupon_used_count\decimal(8,0)};{coupon_used_date\string\coupon_used_date\varchar};{baitai_code\string\baitai_code\varchar};{orm_rowid\string\orm_rowid\decimal(38,0)};{created_user\string\created_user\varchar};{created_datetime\string\created_datetime\varchar};{updated_user\string\updated_user\varchar};{updated_datetime\string\updated_datetime\varchar}
PRIMARY_KEY=[coupon_management_code,customer_code,coupon_issue_status,coupon_used_count,orm_rowid,created_user,created_datetime,updated_user,updated_datetime]
[IF-OMS-CO-003]
IF_ID=IF-OMS-CO-003
SCHEMA_NAME=dwh_raw
TABLE_NAME=coupon_commodity
IF_GROUP=CO-IN-001
JOBNET_ID=DWH-JN-OMS-CO-001
MAPPING_INFO={coupon_management_code\string\coupon_management_code\varchar};{shop_code\string\shop_code\varchar};{commodity_code\string\commodity_code\varchar};{orm_rowid\string\orm_rowid\decimal(38,0)};{created_user\string\created_user\varchar};{created_datetime\string\created_datetime\varchar};{updated_user\string\updated_user\varchar};{updated_datetime\string\updated_datetime\varchar}
PRIMARY_KEY=[coupon_management_code,shop_code,commodity_code,orm_rowid,created_user,created_datetime,updated_user,updated_datetime]
[IF-CMN-ST-001]
IF_ID=IF-CMN-ST-001
SCHEMA_NAME=dwh_raw
TABLE_NAME=stock_list
IF_GROUP=0
JOBNET_ID=DWH-JN-CMN-ST-001
MAPPING_INFO={corp_cd\string\corp_cd\varchar};{data_date\string\data_date\varchar};{center_code\string\center_code\varchar};{stock_group_code\string\stock_group_code\varchar};{shipping_code\string\shipping_code\varchar};{product_cd\string\product_cd\varchar};{first_day_stock_count\string\first_day_stock_count\decimal(7,0)};{arrival_quantity\string\arrival_quantity\decimal(7,0)};{shipping_quantity\string\shipping_quantity\decimal(7,0)};{arrival_quantity_irregular\string\arrival_quantity_irregular\decimal(7,0)};{shipping_quantity_irregular\string\shipping_quantity_irregular\decimal(7,0)};{carryover_stock_count\string\carryover_stock_count\decimal(7,0)}
PRIMARY_KEY=[]
[IF-OMS-ST-001]
IF_ID=IF-OMS-ST-001
SCHEMA_NAME=dwh_raw
TABLE_NAME=stock
IF_GROUP=0
JOBNET_ID=DWH-JN-OMS-ST-001
MAPPING_INFO={shop_code\string\shop_code\varchar};{sku_code\string\sku_code\varchar};{allocated_warehouse_code\string\allocated_warehouse_code\varchar};{commodity_code\string\commodity_code\varchar};{wms_stock_quantity\string\wms_stock_quantity\decimal(8,0)};{stock_quantity\string\stock_quantity\decimal(8,0)};{allocated_quantity\string\allocated_quantity\decimal(8,0)};{reserved_quantity\string\reserved_quantity\decimal(8,0)};{temporary_allocated_quantity\string\temporary_allocated_quantity\decimal(8,0)};{arrival_reserved_quantity\string\arrival_reserved_quantity\decimal(8,0)};{temporary_reserved_quantity\string\temporary_reserved_quantity\decimal(8,0)};{reservation_limit\string\reservation_limit\decimal(8,0)};{stock_threshold\string\stock_threshold\decimal(8,0)};{stock_arrival_date\string\stock_arrival_date\varchar};{arrival_quantity\string\arrival_quantity\decimal(8,0)};{orm_rowid\string\orm_rowid\decimal(38,0)};{created_user\string\created_user\varchar};{created_datetime\string\created_datetime\varchar};{updated_user\string\updated_user\varchar};{updated_datetime\string\updated_datetime\varchar}
PRIMARY_KEY=[shop_code,sku_code,allocated_warehouse_code,commodity_code,stock_quantity,allocated_quantity,temporary_allocated_quantity,arrival_reserved_quantity,temporary_reserved_quantity,stock_threshold,orm_rowid,created_user,created_datetime,updated_user,updated_datetime]
[IF-OMS-ST-002]
IF_ID=IF-OMS-ST-002
SCHEMA_NAME=dwh_raw
TABLE_NAME=stock_io_detail
IF_GROUP=0
JOBNET_ID=DWH-JN-OMS-ST-002
MAPPING_INFO={stock_io_id\string\stock_io_id\decimal(38,0)};{shop_code\string\shop_code\varchar};{stock_io_date\string\stock_io_date\varchar};{sku_code\string\sku_code\varchar};{stock_io_quantity\string\stock_io_quantity\decimal(8,0)};{stock_io_type\string\stock_io_type\decimal(1,0)};{memo\string\memo\varchar};{orm_rowid\string\orm_rowid\decimal(38,0)};{created_user\string\created_user\varchar};{created_datetime\string\created_datetime\varchar};{updated_user\string\updated_user\varchar};{updated_datetime\string\updated_datetime\varchar}
PRIMARY_KEY=[stock_io_id,shop_code,stock_io_date,sku_code,stock_io_quantity,stock_io_type,orm_rowid,created_user,created_datetime,updated_user,updated_datetime]
[IF-POS-ST-001]
IF_ID=IF-POS-ST-001
SCHEMA_NAME=dwh_raw
TABLE_NAME=stock_month
IF_GROUP=0
JOBNET_ID=DWH-JN-POS-ST-001
MAPPING_INFO={business_year_month\string\business_year_month\varchar};{shop_cd\string\shop_cd\varchar};{item_cd\string\item_cd\varchar};{brand_cd\string\brand_cd\varchar};{proper_item_kind\string\proper_item_kind\varchar};{retail_price\string\retail_price\decimal(10,0)};{seling_price\string\seling_price\decimal(10,0)};{good_stock_count\string\good_stock_count\decimal(10,0)};{defective_stock_count\string\defective_stock_count\decimal(10,0)};{pending_stock_count\string\pending_stock_count\decimal(10,0)};{reserving_stock_count\string\reserving_stock_count\decimal(10,0)};{take_out_stock_count\string\take_out_stock_count\decimal(10,0)};{shipment_stock_count\string\shipment_stock_count\decimal(10,0)};{reservation_stock_count\string\reservation_stock_count\decimal(10,0)};{reserve_count1\string\reserve_count1\decimal(10,0)};{reserve_count2\string\reserve_count2\decimal(10,0)};{reserve_count3\string\reserve_count3\decimal(10,0)};{reserve_count4\string\reserve_count4\decimal(10,0)};{reserve_count5\string\reserve_count5\decimal(10,0)};{ins_biz_date\string\ins_biz_date\varchar};{upd_biz_date\string\upd_biz_date\varchar};{ins_date\string\ins_date\varchar};{upd_date\string\upd_date\varchar};{ins_user_id\string\ins_user_id\varchar};{upd_user_id\string\upd_user_id\varchar};{ins_pgm_id\string\ins_pgm_id\varchar};{upd_pgm_id\string\upd_pgm_id\varchar}
PRIMARY_KEY=[business_year_month,shop_cd,item_cd,brand_cd,proper_item_kind,retail_price,seling_price,good_stock_count,defective_stock_count,pending_stock_count,reserving_stock_count,take_out_stock_count,shipment_stock_count,reservation_stock_count,ins_date,upd_date,ins_user_id,upd_user_id,ins_pgm_id,upd_pgm_id]
[IF-OMS-OR-001]
IF_ID=IF-OMS-OR-001
SCHEMA_NAME=dwh_raw
TABLE_NAME=order_header
IF_GROUP=OR-IN-001
JOBNET_ID=DWH-JN-OMS-OR-001
MAPPING_INFO={order_no\string\order_no\varchar};{shop_code\string\shop_code\varchar};{order_datetime\string\order_datetime\varchar};{customer_code\string\customer_code\varchar};{neo_customer_no\string\neo_customer_no\varchar};{guest_flg\string\guest_flg\decimal(1,0)};{last_name\string\last_name\varchar};{first_name\string\first_name\varchar};{last_name_kana\string\last_name_kana\varchar};{first_name_kana\string\first_name_kana\varchar};{email\string\email\varchar};{birth_date\string\birth_date\varchar};{sex\string\sex\decimal(1,0)};{postal_code\string\postal_code\varchar};{prefecture_code\string\prefecture_code\varchar};{address1\string\address1\varchar};{address2\string\address2\varchar};{address3\string\address3\varchar};{address4\string\address4\varchar};{corporation_post_name\string\corporation_post_name\varchar};{phone_number\string\phone_number\varchar};{advance_later_flg\string\advance_later_flg\decimal(1,0)};{payment_method_no\string\payment_method_no\decimal(8,0)};{payment_method_type\string\payment_method_type\varchar};{payment_method_name\string\payment_method_name\varchar};{ext_payment_method_type\string\ext_payment_method_type\varchar};{payment_commission\string\payment_commission\decimal(8,0)};{payment_commission_tax_gr_code\string\payment_commission_tax_gr_code\varchar};{payment_commission_tax_no\string\payment_commission_tax_no\decimal(3,0)};{payment_commission_tax_rate\string\payment_commission_tax_rate\decimal(3,0)};{payment_commission_tax\string\payment_commission_tax\decimal(10,0)};{payment_commission_tax_type\string\payment_commission_tax_type\decimal(1,0)};{coupon_management_code\string\coupon_management_code\varchar};{coupon_code\string\coupon_code\varchar};{coupon_name\string\coupon_name\varchar};{coupon_type\string\coupon_type\decimal(1,0)};{coupon_use_purchase_price\string\coupon_use_purchase_price\decimal(8,0)};{coupon_discount_type\string\coupon_discount_type\decimal(1,0)};{coupon_discount_price\string\coupon_discount_price\decimal(8,0)};{coupon_discount_rate\string\coupon_discount_rate\decimal(3,0)};{coupon_used_amount\string\coupon_used_amount\decimal(8,0)};{coupon_start_datetime\string\coupon_start_datetime\varchar};{coupon_end_datetime\string\coupon_end_datetime\varchar};{coupon_kbn\string\coupon_kbn\varchar};{goods_group\string\goods_group\varchar};{commodity_category_code\string\commodity_category_code\varchar};{commodity_series\string\commodity_series\varchar};{coupon_commodity_code_display\string\coupon_commodity_code_display\varchar};{baitai_name\string\baitai_name\varchar};{used_point\string\used_point\decimal(8,0)};{total_amount\string\total_amount\decimal(8,0)};{ec_promotion_id\string\ec_promotion_id\varchar};{ec_promotion_name\string\ec_promotion_name\varchar};{ec_promotion_discount_price\string\ec_promotion_discount_price\decimal(8,0)};{ec_campaign_id\string\ec_campaign_id\varchar};{ec_campaign_name\string\ec_campaign_name\varchar};{payment_date\string\payment_date\varchar};{payment_limit_date\string\payment_limit_date\varchar};{payment_status\string\payment_status\decimal(1,0)};{ext_payment_status\string\ext_payment_status\varchar};{customer_group_code\string\customer_group_code\varchar};{data_transport_status\string\data_transport_status\decimal(1,0)};{order_status\string\order_status\decimal(1,0)};{ext_order_status\string\ext_order_status\varchar};{tax_reference_date\string\tax_reference_date\varchar};{cancel_date\string\cancel_date\varchar};{client_group\string\client_group\varchar};{caution\string\caution\varchar};{message\string\message\varchar};{payment_order_id\string\payment_order_id\varchar};{cvs_code\string\cvs_code\varchar};{payment_receipt_no\string\payment_receipt_no\varchar};{payment_receipt_url\string\payment_receipt_url\varchar};{receipt_no\string\receipt_no\varchar};{customer_no\string\customer_no\varchar};{confirm_no\string\confirm_no\varchar};{career_key\string\career_key\varchar};{order_create_error_code\string\order_create_error_code\varchar};{order_display_status\string\order_display_status\decimal(1,0)};{order_kind_kbn\string\order_kind_kbn\varchar};{marketing_channel\string\marketing_channel\varchar};{original_order_no\string\original_order_no\varchar};{external_order_no\string\external_order_no\varchar};{order_recieve_datetime\string\order_recieve_datetime\varchar};{order_update_datetime\string\order_update_datetime\varchar};{order_update_reason_kbn\string\order_update_reason_kbn\varchar};{cancel_reason_kbn\string\cancel_reason_kbn\varchar};{uncollectible_date\string\uncollectible_date\varchar};{order_total_price\string\order_total_price\decimal(10,0)};{account_receivable_balance\string\account_receivable_balance\decimal(10,0)};{appropriate_amount\string\appropriate_amount\decimal(10,0)};{bill_address_kbn\string\bill_address_kbn\varchar};{receipt_flg\string\receipt_flg\decimal(1,0)};{receipt_to\string\receipt_to\varchar};{receipt_detail\string\receipt_detail\varchar};{bill_price\string\bill_price\decimal(10,0)};{bill_no\string\bill_no\varchar};{bill_print_count\string\bill_print_count\decimal(3,0)};{authority_result_kbn\string\authority_result_kbn\varchar};{authority_no\string\authority_no\varchar};{card_password\string\card_password\varchar};{authority_approval_no\string\authority_approval_no\varchar};{authority_date\string\authority_date\varchar};{authority_price\string\authority_price\decimal(10,0)};{authority_cancel_approval_no\string\authority_cancel_approval_no\varchar};{authority_cancel_date\string\authority_cancel_date\varchar};{credit_payment_no\string\credit_payment_no\varchar};{credit_payment_date\string\credit_payment_date\varchar};{credit_payment_price\string\credit_payment_price\decimal(10,0)};{credit_cancel_payment_no\string\credit_cancel_payment_no\varchar};{credit_cancel_payment_date\string\credit_cancel_payment_date\varchar};{credit_result_kbn\string\credit_result_kbn\varchar};{card_brand\string\card_brand\varchar};{credit_card_kanri_no\string\credit_card_kanri_no\varchar};{credit_card_kanri_detail_no\string\credit_card_kanri_detail_no\varchar};{credit_card_no\string\credit_card_no\varchar};{credit_card_meigi\string\credit_card_meigi\varchar};{credit_card_valid_year\string\credit_card_valid_year\varchar};{credit_card_valid_month\string\credit_card_valid_month\varchar};{credit_card_pay_count\string\credit_card_pay_count\varchar};{payment_bar_code\string\payment_bar_code\varchar};{amzn_charge_permission_id\string\amzn_charge_permission_id\varchar};{amzn_charge_id\string\amzn_charge_id\varchar};{amzn_charge_status\string\amzn_charge_status\varchar};{amzn_authorization_datetime\string\amzn_authorization_datetime\varchar};{amzn_capture_initiated_datetime\string\amzn_capture_initiated_datetime\varchar};{amzn_captured_datetime\string\amzn_captured_datetime\varchar};{amzn_canceled_datetime\string\amzn_canceled_datetime\varchar};{order_user_code\string\order_user_code\decimal(38,0)};{order_user\string\order_user\varchar};{change_user_code\string\change_user_code\decimal(38,0)};{change_user\string\change_user\varchar};{demand_kbn\string\demand_kbn\varchar};{demand1_ref_date\string\demand1_ref_date\varchar};{demand1_date\string\demand1_date\varchar};{demand1_limit_date\string\demand1_limit_date\varchar};{demand1_amount\string\demand1_amount\decimal(8,0)};{demand1_bar_code\string\demand1_bar_code\varchar};{demand2_ref_date\string\demand2_ref_date\varchar};{demand2_date\string\demand2_date\varchar};{demand2_limit_date\string\demand2_limit_date\varchar};{demand2_amount\string\demand2_amount\decimal(8,0)};{demand2_bar_code\string\demand2_bar_code\varchar};{demand3_ref_date\string\demand3_ref_date\varchar};{demand3_date\string\demand3_date\varchar};{demand3_limit_date\string\demand3_limit_date\varchar};{demand3_amount\string\demand3_amount\decimal(8,0)};{demand3_bar_code\string\demand3_bar_code\varchar};{kashidaore_date\string\kashidaore_date\varchar};{demand_exclude_reason_kbn\string\demand_exclude_reason_kbn\varchar};{demand_exclude_start_date\string\demand_exclude_start_date\varchar};{demand_exclude_end_date\string\demand_exclude_end_date\varchar};{bill_sei_kj\string\bill_sei_kj\varchar};{bill_mei_kj\string\bill_mei_kj\varchar};{bill_sei_kn\string\bill_sei_kn\varchar};{bill_mei_kn\string\bill_mei_kn\varchar};{bill_tel_no\string\bill_tel_no\varchar};{bill_zipcd\string\bill_zipcd\varchar};{bill_addr1\string\bill_addr1\varchar};{bill_addr2\string\bill_addr2\varchar};{bill_addr3\string\bill_addr3\varchar};{bill_addr4\string\bill_addr4\varchar};{bill_corporation_post_name\string\bill_corporation_post_name\varchar};{nohinsyo_uketsuke_tanto\string\nohinsyo_uketsuke_tanto\varchar};{grant_plan_point_prod\string\grant_plan_point_prod\decimal(10,0)};{grant_plan_point_other\string\grant_plan_point_other\decimal(10,0)};{grant_plan_point_total\string\grant_plan_point_total\decimal(10,0)};{grant_point_prod\string\grant_point_prod\decimal(10,0)};{grant_point_other\string\grant_point_other\decimal(10,0)};{grant_point_total\string\grant_point_total\decimal(10,0)};{reduction_plan_point_total\string\reduction_plan_point_total\decimal(10,0)};{reduction_point_total\string\reduction_point_total\decimal(10,0)};{subtotal_before_campaign\string\subtotal_before_campaign\decimal(10,0)};{subtotal_after_campaign\string\subtotal_after_campaign\decimal(10,0)};{total_before_campaign\string\total_before_campaign\decimal(10,0)};{orm_rowid\string\orm_rowid\decimal(38,0)};{created_user\string\created_user\varchar};{created_datetime\string\created_datetime\varchar};{updated_user\string\updated_user\varchar};{updated_datetime\string\updated_datetime\varchar}
PRIMARY_KEY=[order_no,shop_code,order_datetime,customer_code,guest_flg,last_name,last_name_kana,birth_date,sex,postal_code,prefecture_code,address1,address2,address3,phone_number,advance_later_flg,payment_method_no,payment_method_type,ext_payment_method_type,payment_commission,payment_commission_tax_gr_code,payment_commission_tax_no,payment_commission_tax_rate,payment_commission_tax,payment_commission_tax_type,coupon_used_amount,total_amount,payment_status,ext_payment_status,data_transport_status,order_status,ext_order_status,tax_reference_date,order_create_error_code,order_kind_kbn,marketing_channel,order_recieve_datetime,order_update_datetime,order_total_price,account_receivable_balance,appropriate_amount,bill_address_kbn,receipt_flg,bill_price,bill_print_count,demand_kbn,bill_sei_kj,bill_sei_kn,bill_tel_no,bill_zipcd,bill_addr1,bill_addr2,bill_addr3,orm_rowid,created_user,created_datetime,updated_user,updated_datetime]
[IF-OMS-OR-002]
IF_ID=IF-OMS-OR-002
SCHEMA_NAME=dwh_raw
TABLE_NAME=order_payment_mng
IF_GROUP=0
JOBNET_ID=DWH-JN-OMS-OR-002
MAPPING_INFO={order_no\string\order_no\varchar};{order_payment_no\string\order_payment_no\decimal(8,0)};{payment_type\string\payment_type\varchar};{payment_identify_code\string\payment_identify_code\varchar};{payment_process_type\string\payment_process_type\varchar};{payment_process_status\string\payment_process_status\varchar};{payment_process_price\string\payment_process_price\decimal(8,0)};{payment_process_datetime\string\payment_process_datetime\varchar};{payment_process_send_content\string\payment_process_send_content\varchar};{payment_process_receive_content\string\payment_process_receive_content\varchar};{henpin_request_no\string\henpin_request_no\varchar};{orm_rowid\string\orm_rowid\decimal(38,0)};{created_user\string\created_user\varchar};{created_datetime\string\created_datetime\varchar};{updated_user\string\updated_user\varchar};{updated_datetime\string\updated_datetime\varchar}
PRIMARY_KEY=[order_no,order_payment_no,orm_rowid,created_user,created_datetime,updated_user,updated_datetime]
[IF-OMS-OR-003]
IF_ID=IF-OMS-OR-003
SCHEMA_NAME=dwh_raw
TABLE_NAME=order_detail
IF_GROUP=OR-IN-001
JOBNET_ID=DWH-JN-OMS-OR-001
MAPPING_INFO={order_no\string\order_no\varchar};{order_detail_no\string\order_detail_no\decimal(16,0)};{shop_code\string\shop_code\varchar};{sku_code\string\sku_code\varchar};{commodity_code\string\commodity_code\varchar};{commodity_name\string\commodity_name\varchar};{commodity_kind\string\commodity_kind\varchar};{baitai_code\string\baitai_code\varchar};{baitai_name\string\baitai_name\varchar};{hinban_code\string\hinban_code\varchar};{standard_detail1_name\string\standard_detail1_name\varchar};{standard_detail2_name\string\standard_detail2_name\varchar};{purchasing_amount\string\purchasing_amount\decimal(8,0)};{unit_price\string\unit_price\decimal(8,0)};{retail_price\string\retail_price\decimal(8,0)};{retail_tax\string\retail_tax\decimal(10,0)};{commodity_tax_group_code\string\commodity_tax_group_code\varchar};{commodity_tax_no\string\commodity_tax_no\decimal(3,0)};{commodity_tax_rate\string\commodity_tax_rate\decimal(3,0)};{commodity_tax\string\commodity_tax\decimal(10,0)};{commodity_tax_type\string\commodity_tax_type\decimal(1,0)};{campaign_code\string\campaign_code\varchar};{campaign_name\string\campaign_name\varchar};{campaign_instructions_code\string\campaign_instructions_code\varchar};{campaign_instructions_name\string\campaign_instructions_name\varchar};{campaign_discount_rate\string\campaign_discount_rate\decimal(3,0)};{campaign_discount_price\string\campaign_discount_price\decimal(8,0)};{present_campaign_instructions_code\string\present_campaign_instructions_code\varchar};{present_order_detail_no\string\present_order_detail_no\decimal(16,0)};{age_limit_code\string\age_limit_code\decimal(8,0)};{age_limit_name\string\age_limit_name\varchar};{age\string\age\decimal(3,0)};{age_limit_confirm_type\string\age_limit_confirm_type\decimal(1,0)};{applied_point_rate\string\applied_point_rate\decimal(8,0)};{benefits_code\string\benefits_code\varchar};{benefits_name\string\benefits_name\varchar};{benefits_commodity_code\string\benefits_commodity_code\varchar};{stock_management_type\string\stock_management_type\decimal(1,0)};{stock_allocated_kbn\string\stock_allocated_kbn\varchar};{allocated_warehouse_code\string\allocated_warehouse_code\varchar};{allocated_quantity\string\allocated_quantity\decimal(8,0)};{arrival_reserved_quantity\string\arrival_reserved_quantity\decimal(8,0)};{cancel_quantity\string\cancel_quantity\decimal(5,0)};{henpin_qt\string\henpin_qt\decimal(5,0)};{coupon_management_code\string\coupon_management_code\varchar};{coupon_code\string\coupon_code\varchar};{coupon_name\string\coupon_name\varchar};{coupon_discount_rate\string\coupon_discount_rate\decimal(3,0)};{coupon_discount_price\string\coupon_discount_price\decimal(8,0)};{ec_promotion_id\string\ec_promotion_id\varchar};{ec_promotion_name\string\ec_promotion_name\varchar};{ec_promotion_discount_price\string\ec_promotion_discount_price\decimal(8,0)};{ec_campaign_id\string\ec_campaign_id\varchar};{ec_campaign_name\string\ec_campaign_name\varchar};{adjustment_price\string\adjustment_price\decimal(8,0)};{keihi_hurikae_target_flg\string\keihi_hurikae_target_flg\decimal(1,0)};{member_price_applied_flg\string\member_price_applied_flg\decimal(1,0)};{shipping_charge_target_flg\string\shipping_charge_target_flg\decimal(1,0)};{regular_contract_no\string\regular_contract_no\varchar};{regular_contract_detail_no\string\regular_contract_detail_no\decimal(3,0)};{regular_kaiji\string\regular_kaiji\decimal(5,0)};{regular_check_memo\string\regular_check_memo\varchar};{total_commodity_buy_count\string\total_commodity_buy_count\decimal(5,0)};{total_commodity_regular_kaiji\string\total_commodity_regular_kaiji\decimal(5,0)};{regular_total_commodity_regular_kaiji\string\regular_total_commodity_regular_kaiji\decimal(5,0)};{commodity_category_code\string\commodity_category_code\varchar};{total_category_buy_count\string\total_category_buy_count\decimal(5,0)};{total_categoryregular_kaiji\string\total_categoryregular_kaiji\decimal(5,0)};{regular_total_categoryregular_kaiji\string\regular_total_categoryregular_kaiji\decimal(5,0)};{commodity_subcategory_code\string\commodity_subcategory_code\varchar};{total_subcategory_buy_count\string\total_subcategory_buy_count\decimal(5,0)};{total_subcategoryregular_kaiji\string\total_subcategoryregular_kaiji\decimal(5,0)};{regular_total_subcategoryregular_kaiji\string\regular_total_subcategoryregular_kaiji\decimal(5,0)};{commodity_subsubcategory_code\string\commodity_subsubcategory_code\varchar};{total_subsubcategory_buy_count\string\total_subsubcategory_buy_count\decimal(5,0)};{total_subsubcategoryregular_kaiji\string\total_subsubcategoryregular_kaiji\decimal(5,0)};{regular_total_subsubcategoryregular_kaiji\string\regular_total_subsubcategoryregular_kaiji\decimal(5,0)};{grant_plan_point_prod_detail\string\grant_plan_point_prod_detail\decimal(10,0)};{reduction_plan_point_prod_detail\string\reduction_plan_point_prod_detail\decimal(10,0)};{orm_rowid\string\orm_rowid\decimal(38,0)};{created_user\string\created_user\varchar};{created_datetime\string\created_datetime\varchar};{updated_user\string\updated_user\varchar};{updated_datetime\string\updated_datetime\varchar}
PRIMARY_KEY=[order_no,order_detail_no,shop_code,sku_code,commodity_code,commodity_name,commodity_kind,baitai_code,baitai_name,hinban_code,purchasing_amount,unit_price,retail_price,retail_tax,commodity_tax_group_code,commodity_tax_no,commodity_tax_rate,commodity_tax,commodity_tax_type,stock_management_type,stock_allocated_kbn,allocated_quantity,arrival_reserved_quantity,cancel_quantity,henpin_qt,keihi_hurikae_target_flg,member_price_applied_flg,shipping_charge_target_flg,regular_kaiji,total_commodity_buy_count,total_commodity_regular_kaiji,regular_total_commodity_regular_kaiji,total_category_buy_count,total_categoryregular_kaiji,regular_total_categoryregular_kaiji,total_subcategory_buy_count,total_subcategoryregular_kaiji,regular_total_subcategoryregular_kaiji,total_subsubcategory_buy_count,total_subsubcategoryregular_kaiji,regular_total_subsubcategoryregular_kaiji,orm_rowid,created_user,created_datetime,updated_user,updated_datetime]
[IF-OMS-OR-004]
IF_ID=IF-OMS-OR-004
SCHEMA_NAME=dwh_raw
TABLE_NAME=regular_sale_cont_header
IF_GROUP=OR-IN-002
JOBNET_ID=DWH-JN-OMS-OR-003
MAPPING_INFO={regular_contract_no\string\regular_contract_no\varchar};{shop_code\string\shop_code\varchar};{regular_sale_cont_datetime\string\regular_sale_cont_datetime\varchar};{customer_code\string\customer_code\varchar};{neo_customer_no\string\neo_customer_no\varchar};{payment_method_no\string\payment_method_no\decimal(8,0)};{address_no\string\address_no\decimal(8,0)};{regular_sale_cont_status\string\regular_sale_cont_status\decimal(1,0)};{next_delivery_request_date\string\next_delivery_request_date\varchar};{external_order_no\string\external_order_no\varchar};{order_user_code\string\order_user_code\decimal(38,0)};{regular_update_datetime\string\regular_update_datetime\varchar};{change_user_code\string\change_user_code\decimal(38,0)};{regular_update_reason_kbn\string\regular_update_reason_kbn\varchar};{otodoke_hope_time_kbn\string\otodoke_hope_time_kbn\varchar};{marketing_channel\string\marketing_channel\varchar};{delivery_type_no\string\delivery_type_no\decimal(8,0)};{shipping_method_flg\string\shipping_method_flg\decimal(1,0)};{ext_payment_method_type\string\ext_payment_method_type\varchar};{card_brand\string\card_brand\varchar};{credit_card_kanri_no\string\credit_card_kanri_no\varchar};{credit_card_kanri_detail_no\string\credit_card_kanri_detail_no\varchar};{credit_card_no\string\credit_card_no\varchar};{credit_card_meigi\string\credit_card_meigi\varchar};{credit_card_valid_year\string\credit_card_valid_year\varchar};{credit_card_valid_month\string\credit_card_valid_month\varchar};{credit_card_pay_count\string\credit_card_pay_count\varchar};{amzn_charge_permission_id\string\amzn_charge_permission_id\varchar};{bill_address_kbn\string\bill_address_kbn\varchar};{bill_print_otodoke_id\string\bill_print_otodoke_id\varchar};{o_name_disp_kbn\string\o_name_disp_kbn\varchar};{delivery_note_flg\string\delivery_note_flg\decimal(1,0)};{include_flg\string\include_flg\decimal(1,0)};{receipt_flg\string\receipt_flg\decimal(1,0)};{receipt_to\string\receipt_to\varchar};{receipt_detail\string\receipt_detail\varchar};{first_shipping_date\string\first_shipping_date\varchar};{lastest_shipping_date\string\lastest_shipping_date\varchar};{first_delivery_date\string\first_delivery_date\varchar};{lastest_delivery_date\string\lastest_delivery_date\varchar};{regular_stop_date\string\regular_stop_date\varchar};{regular_stop_reason_kbn\string\regular_stop_reason_kbn\varchar};{regular_hold_date\string\regular_hold_date\varchar};{regular_hold_clear_date\string\regular_hold_clear_date\varchar};{regular_kaiji\string\regular_kaiji\decimal(5,0)};{shipped_regular_count\string\shipped_regular_count\decimal(3,0)};{delivery_memo\string\delivery_memo\varchar};{regular_hold_reason_kbn\string\regular_hold_reason_kbn\varchar};{niyose_flg\string\niyose_flg\decimal(1,0)};{orm_rowid\string\orm_rowid\decimal(38,0)};{created_user\string\created_user\varchar};{created_datetime\string\created_datetime\varchar};{updated_user\string\updated_user\varchar};{updated_datetime\string\updated_datetime\varchar}
PRIMARY_KEY=[regular_contract_no,shop_code,regular_sale_cont_datetime,customer_code,payment_method_no,regular_sale_cont_status,shipping_method_flg,ext_payment_method_type,bill_address_kbn,o_name_disp_kbn,delivery_note_flg,include_flg,receipt_flg,regular_kaiji,shipped_regular_count,niyose_flg,orm_rowid,created_user,created_datetime,updated_user,updated_datetime]
[IF-OMS-OR-005]
IF_ID=IF-OMS-OR-005
SCHEMA_NAME=dwh_raw
TABLE_NAME=regular_sale_cont_detail
IF_GROUP=OR-IN-002
JOBNET_ID=DWH-JN-OMS-OR-003
MAPPING_INFO={regular_contract_no\string\regular_contract_no\varchar};{regular_contract_detail_no\string\regular_contract_detail_no\decimal(3,0)};{shop_code\string\shop_code\varchar};{sku_code\string\sku_code\varchar};{commodity_code\string\commodity_code\varchar};{contract_amount\string\contract_amount\decimal(8,0)};{commodity_name\string\commodity_name\varchar};{commodity_subcategory_code\string\commodity_subcategory_code\varchar};{commodity_subcategory_code_name\string\commodity_subcategory_code_name\varchar};{baitai_code\string\baitai_code\varchar};{regular_cycle_delivery_kbn\string\regular_cycle_delivery_kbn\varchar};{regular_cycle_kijun_date\string\regular_cycle_kijun_date\varchar};{regular_kind\string\regular_kind\varchar};{regular_cycle_day_int\string\regular_cycle_day_int\varchar};{regular_cycle_day\string\regular_cycle_day\decimal(2,0)};{regular_cycle_mon_interval\string\regular_cycle_mon_interval\decimal(2,0)};{regular_cycle_mon_interval_day\string\regular_cycle_mon_interval_day\decimal(2,0)};{regular_cycle_week_num\string\regular_cycle_week_num\decimal(2,0)};{regular_cycle_week_kbn\string\regular_cycle_week_kbn\varchar};{regular_cycle_week_mon\string\regular_cycle_week_mon\varchar};{regular_cycle_week_tue\string\regular_cycle_week_tue\varchar};{regular_cycle_week_wed\string\regular_cycle_week_wed\varchar};{regular_cycle_week_thu\string\regular_cycle_week_thu\varchar};{regular_cycle_week_fri\string\regular_cycle_week_fri\varchar};{regular_cycle_week_sat\string\regular_cycle_week_sat\varchar};{regular_cycle_week_sun\string\regular_cycle_week_sun\varchar};{regular_cycle_week_hol\string\regular_cycle_week_hol\varchar};{cycle_disp_name\string\cycle_disp_name\varchar};{next_shipping_plan_date\string\next_shipping_plan_date\varchar};{next_shipping_date\string\next_shipping_date\varchar};{next_delivery_plan_date\string\next_delivery_plan_date\varchar};{next_delivery_date\string\next_delivery_date\varchar};{lastest_delivery_date\string\lastest_delivery_date\varchar};{regular_kaiji\string\regular_kaiji\decimal(5,0)};{shipped_regular_count\string\shipped_regular_count\decimal(3,0)};{regular_sale_stop_from\string\regular_sale_stop_from\decimal(5,0)};{regular_sale_stop_to\string\regular_sale_stop_to\decimal(5,0)};{hasso_souko_cd\string\hasso_souko_cd\varchar};{shipping_area\string\shipping_area\varchar};{regular_check_memo\string\regular_check_memo\varchar};{regular_memo_hold_flg\string\regular_memo_hold_flg\decimal(1,0)};{souko_shiji\string\souko_shiji\varchar};{next_regular_sale_stop_status\string\next_regular_sale_stop_status\varchar};{regular_stop_reason_kbn\string\regular_stop_reason_kbn\varchar};{orm_rowid\string\orm_rowid\decimal(38,0)};{created_user\string\created_user\varchar};{created_datetime\string\created_datetime\varchar};{updated_user\string\updated_user\varchar};{updated_datetime\string\updated_datetime\varchar}
PRIMARY_KEY=[regular_contract_no,regular_contract_detail_no,shop_code,sku_code,commodity_code,contract_amount,commodity_name,baitai_code,regular_cycle_delivery_kbn,regular_kind,next_shipping_date,regular_kaiji,shipped_regular_count,regular_memo_hold_flg,next_regular_sale_stop_status,orm_rowid,created_user,created_datetime,updated_user,updated_datetime]
[IF-OMS-OR-006]
IF_ID=IF-OMS-OR-006
SCHEMA_NAME=dwh_raw
TABLE_NAME=regular_sale_cont_composition
IF_GROUP=OR-IN-002
JOBNET_ID=DWH-JN-OMS-OR-003
MAPPING_INFO={regular_contract_no\string\regular_contract_no\varchar};{regular_contract_detail_no\string\regular_contract_detail_no\decimal(3,0)};{composition_no\string\composition_no\decimal(2,0)};{shop_code\string\shop_code\varchar};{parent_commodity_code\string\parent_commodity_code\varchar};{parent_sku_code\string\parent_sku_code\varchar};{child_commodity_code\string\child_commodity_code\varchar};{child_sku_code\string\child_sku_code\varchar};{commodity_name\string\commodity_name\varchar};{composition_quantity\string\composition_quantity\decimal(2,0)};{regular_sale_composition_no\string\regular_sale_composition_no\varchar};{regular_sale_composition_name\string\regular_sale_composition_name\varchar};{regular_sale_commodity_type\string\regular_sale_commodity_type\varchar};{regular_order_count_min_limit\string\regular_order_count_min_limit\decimal(5,0)};{regular_order_count_max_limit\string\regular_order_count_max_limit\decimal(5,0)};{regular_order_count_interval\string\regular_order_count_interval\decimal(5,0)};{orm_rowid\string\orm_rowid\decimal(38,0)};{created_user\string\created_user\varchar};{created_datetime\string\created_datetime\varchar};{updated_user\string\updated_user\varchar};{updated_datetime\string\updated_datetime\varchar}
PRIMARY_KEY=[regular_contract_no,regular_contract_detail_no,composition_no,shop_code,parent_commodity_code,parent_sku_code,child_commodity_code,child_sku_code,commodity_name,composition_quantity,regular_sale_composition_no,orm_rowid,created_user,created_datetime,updated_user,updated_datetime]
[IF-OMS-SP-001]
IF_ID=IF-OMS-SP-001
SCHEMA_NAME=dwh_raw
TABLE_NAME=shipping_detail
IF_GROUP=SP-IN-001
JOBNET_ID=DWH-JN-OMS-SP-001
MAPPING_INFO={shipping_no\string\shipping_no\varchar};{shipping_detail_no\string\shipping_detail_no\decimal(16,0)};{shop_code\string\shop_code\varchar};{sku_code\string\sku_code\varchar};{unit_price\string\unit_price\decimal(8,0)};{discount_price\string\discount_price\decimal(8,0)};{discount_amount\string\discount_amount\decimal(8,0)};{retail_price\string\retail_price\decimal(8,0)};{retail_tax_group_code\string\retail_tax_group_code\varchar};{retail_tax_no\string\retail_tax_no\decimal(3,0)};{retail_tax_rate\string\retail_tax_rate\decimal(3,0)};{retail_tax\string\retail_tax\decimal(10,0)};{purchasing_amount\string\purchasing_amount\decimal(8,0)};{gift_code\string\gift_code\varchar};{gift_name\string\gift_name\varchar};{gift_price\string\gift_price\decimal(8,0)};{gift_tax_group_code\string\gift_tax_group_code\varchar};{gift_tax_no\string\gift_tax_no\decimal(3,0)};{gift_tax_rate\string\gift_tax_rate\decimal(3,0)};{gift_tax\string\gift_tax\decimal(10,0)};{gift_tax_type\string\gift_tax_type\decimal(1,0)};{noshi_code\string\noshi_code\varchar};{noshi_name\string\noshi_name\varchar};{noshi_price\string\noshi_price\decimal(8,0)};{noshi_tax_group_code\string\noshi_tax_group_code\varchar};{noshi_tax_no\string\noshi_tax_no\decimal(3,0)};{noshi_tax_rate\string\noshi_tax_rate\decimal(3,0)};{noshi_tax\string\noshi_tax\decimal(10,0)};{noshi_tax_type\string\noshi_tax_type\decimal(1,0)};{noshi_nameplate\string\noshi_nameplate\varchar};{noshi_message\string\noshi_message\varchar};{air_transport_flg\string\air_transport_flg\decimal(1,0)};{delivery_note_no_disp_flg\string\delivery_note_no_disp_flg\decimal(1,0)};{hasso_souko_cd\string\hasso_souko_cd\varchar};{shipping_hold_kbn\string\shipping_hold_kbn\varchar};{shipping_hold_date\string\shipping_hold_date\varchar};{order_detail_no\string\order_detail_no\decimal(16,0)};{tracking_out_flg\string\tracking_out_flg\decimal(1,0)};{souko_shiji\string\souko_shiji\varchar};{orm_rowid\string\orm_rowid\decimal(38,0)};{created_user\string\created_user\varchar};{created_datetime\string\created_datetime\varchar};{updated_user\string\updated_user\varchar};{updated_datetime\string\updated_datetime\varchar}
PRIMARY_KEY=[shipping_no,shipping_detail_no,shop_code,sku_code,unit_price,retail_price,retail_tax_group_code,retail_tax_no,retail_tax_rate,retail_tax,purchasing_amount,air_transport_flg,delivery_note_no_disp_flg,shipping_hold_kbn,tracking_out_flg,orm_rowid,created_user,created_datetime,updated_user,updated_datetime]
[IF-OMS-SP-002]
IF_ID=IF-OMS-SP-002
SCHEMA_NAME=dwh_raw
TABLE_NAME=shipping_header
IF_GROUP=SP-IN-001
JOBNET_ID=DWH-JN-OMS-SP-001
MAPPING_INFO={shipping_no\string\shipping_no\varchar};{order_no\string\order_no\varchar};{shop_code\string\shop_code\varchar};{customer_code\string\customer_code\varchar};{neo_customer_no\string\neo_customer_no\varchar};{address_no\string\address_no\decimal(8,0)};{address_last_name\string\address_last_name\varchar};{address_first_name\string\address_first_name\varchar};{address_last_name_kana\string\address_last_name_kana\varchar};{address_first_name_kana\string\address_first_name_kana\varchar};{postal_code\string\postal_code\varchar};{prefecture_code\string\prefecture_code\varchar};{address1\string\address1\varchar};{address2\string\address2\varchar};{address3\string\address3\varchar};{address4\string\address4\varchar};{corporation_post_name\string\corporation_post_name\varchar};{phone_number\string\phone_number\varchar};{delivery_remark\string\delivery_remark\varchar};{acquired_point\string\acquired_point\decimal(9,0)};{delivery_slip_no\string\delivery_slip_no\varchar};{shipping_charge\string\shipping_charge\decimal(8,0)};{shipping_charge_tax_type\string\shipping_charge_tax_type\decimal(1,0)};{shipping_charge_tax_group_code\string\shipping_charge_tax_group_code\varchar};{shipping_charge_tax_no\string\shipping_charge_tax_no\decimal(3,0)};{shipping_charge_tax_rate\string\shipping_charge_tax_rate\decimal(3,0)};{shipping_charge_tax\string\shipping_charge_tax\decimal(10,0)};{delivery_type_no\string\delivery_type_no\decimal(8,0)};{shipping_method\string\shipping_method\varchar};{delivery_type_name\string\delivery_type_name\varchar};{delivery_appointed_date\string\delivery_appointed_date\varchar};{delivery_appointed_time_start\string\delivery_appointed_time_start\decimal(2,0)};{delivery_appointed_time_end\string\delivery_appointed_time_end\decimal(2,0)};{arrival_date\string\arrival_date\varchar};{arrival_time_start\string\arrival_time_start\decimal(2,0)};{arrival_time_end\string\arrival_time_end\decimal(2,0)};{fixed_sales_status\string\fixed_sales_status\decimal(1,0)};{shipping_status\string\shipping_status\decimal(1,0)};{shipping_direct_date\string\shipping_direct_date\varchar};{shipping_date\string\shipping_date\varchar};{original_shipping_no\string\original_shipping_no\varchar};{return_item_date\string\return_item_date\varchar};{return_item_type\string\return_item_type\decimal(1,0)};{shipping_area\string\shipping_area\varchar};{delivery_note_flg\string\delivery_note_flg\decimal(1,0)};{include_flg\string\include_flg\decimal(1,0)};{delivery_memo\string\delivery_memo\varchar};{shipping_bill_price\string\shipping_bill_price\decimal(10,0)};{shipping_dokon_shiji_code\string\shipping_dokon_shiji_code\varchar};{o_name_disp_kbn\string\o_name_disp_kbn\varchar};{member_stage\string\member_stage\varchar};{possession_point\string\possession_point\decimal(10,0)};{sales_recording_date\string\sales_recording_date\varchar};{prod_pack_type\string\prod_pack_type\varchar};{shipping_method_kbn\string\shipping_method_kbn\varchar};{box_code\string\box_code\varchar};{delivery_note_message\string\delivery_note_message\varchar};{orm_rowid\string\orm_rowid\decimal(38,0)};{created_user\string\created_user\varchar};{created_datetime\string\created_datetime\varchar};{updated_user\string\updated_user\varchar};{updated_datetime\string\updated_datetime\varchar}
PRIMARY_KEY=[shipping_no,order_no,shop_code,address_no,address_last_name,address_last_name_kana,postal_code,prefecture_code,address1,address2,address3,shipping_charge,shipping_charge_tax_type,shipping_charge_tax_group_code,shipping_charge_tax_no,shipping_charge_tax_rate,shipping_charge_tax,delivery_type_no,shipping_method,fixed_sales_status,shipping_status,shipping_area,delivery_note_flg,include_flg,shipping_bill_price,o_name_disp_kbn,orm_rowid,created_user,created_datetime,updated_user,updated_datetime]
[IF-OMS-SP-003]
IF_ID=IF-OMS-SP-003
SCHEMA_NAME=dwh_raw
TABLE_NAME=shipping_detail_composition
IF_GROUP=SP-IN-001
JOBNET_ID=DWH-JN-OMS-SP-001
MAPPING_INFO={shipping_no\string\shipping_no\varchar};{shipping_detail_no\string\shipping_detail_no\decimal(16,0)};{composition_no\string\composition_no\decimal(2,0)};{shop_code\string\shop_code\varchar};{parent_commodity_code\string\parent_commodity_code\varchar};{parent_sku_code\string\parent_sku_code\varchar};{child_commodity_code\string\child_commodity_code\varchar};{child_sku_code\string\child_sku_code\varchar};{commodity_name\string\commodity_name\varchar};{standard_detail1_name\string\standard_detail1_name\varchar};{standard_detail2_name\string\standard_detail2_name\varchar};{unit_price\string\unit_price\decimal(8,0)};{discount_amount\string\discount_amount\decimal(8,0)};{retail_price\string\retail_price\decimal(8,0)};{retail_tax\string\retail_tax\decimal(10,0)};{commodity_tax_group_code\string\commodity_tax_group_code\varchar};{commodity_tax_no\string\commodity_tax_no\decimal(3,0)};{commodity_tax_rate\string\commodity_tax_rate\decimal(3,0)};{commodity_tax\string\commodity_tax\decimal(10,0)};{commodity_tax_type\string\commodity_tax_type\decimal(1,0)};{composition_quantity\string\composition_quantity\decimal(2,0)};{stock_management_type\string\stock_management_type\decimal(1,0)};{stock_allocated_kbn\string\stock_allocated_kbn\varchar};{allocated_warehouse_code\string\allocated_warehouse_code\varchar};{allocated_quantity\string\allocated_quantity\decimal(8,0)};{arrival_reserved_quantity\string\arrival_reserved_quantity\decimal(8,0)};{orm_rowid\string\orm_rowid\decimal(38,0)};{created_user\string\created_user\varchar};{created_datetime\string\created_datetime\varchar};{updated_user\string\updated_user\varchar};{updated_datetime\string\updated_datetime\varchar}
PRIMARY_KEY=[shipping_no,shipping_detail_no,composition_no,shop_code,parent_commodity_code,parent_sku_code,child_commodity_code,child_sku_code,commodity_name,unit_price,retail_price,retail_tax,commodity_tax_group_code,commodity_tax_no,commodity_tax_rate,commodity_tax,commodity_tax_type,composition_quantity,stock_management_type,stock_allocated_kbn,allocated_quantity,arrival_reserved_quantity,orm_rowid,created_user,created_datetime,updated_user,updated_datetime]
[IF-MDM-PR-001]
IF_ID=IF-MDM-PR-001
SCHEMA_NAME=dwh_raw
TABLE_NAME=address_item_code_linkage
IF_GROUP=PR-IN-001
JOBNET_ID=DWH-JN-MDM-PR-001
MAPPING_INFO={mdm_integration_management_cd\string\mdm_integration_management_cd\decimal(19,0)};{address_item_code\string\address_item_code\varchar};{mdm_integration_management_cd_nk\string\mdm_integration_management_cd_nk\decimal(19,0)};{insert_date\string\insert_date\varchar};{insert_id\string\insert_id\varchar};{modify_date\string\modify_date\varchar};{modify_id\string\modify_id\varchar}
PRIMARY_KEY=[mdm_integration_management_cd,address_item_code,insert_date,insert_id]
[IF-MDM-PR-002]
IF_ID=IF-MDM-PR-002
SCHEMA_NAME=dwh_raw
TABLE_NAME=accountin_pattern_gb
IF_GROUP=PR-IN-001
JOBNET_ID=DWH-JN-MDM-PR-001
MAPPING_INFO={accountin_pattern_gb\string\accountin_pattern_gb\varchar};{accountin_pattern_gb_name\string\accountin_pattern_gb_name\varchar};{accountin_pattern_gb_nk\string\accountin_pattern_gb_nk\varchar};{insert_date\string\insert_date\varchar};{insert_id\string\insert_id\varchar};{modify_date\string\modify_date\varchar};{modify_id\string\modify_id\varchar}
PRIMARY_KEY=[accountin_pattern_gb,insert_date,insert_id]
[IF-MDM-PR-003]
IF_ID=IF-MDM-PR-003
SCHEMA_NAME=dwh_raw
TABLE_NAME=period_price_linkage
IF_GROUP=PR-IN-001
JOBNET_ID=DWH-JN-MDM-PR-001
MAPPING_INFO={mdm_integration_management_cd\string\mdm_integration_management_cd\decimal(19,0)};{tax_exc\string\tax_exc\decimal(10,0)};{tax_inc\string\tax_inc\decimal(10,0)};{tax\string\tax\decimal(10,0)};{tax_rate\string\tax_rate\decimal(10,0)};{apply_start_date\string\apply_start_date\varchar};{mdm_integration_management_cd_nk\string\mdm_integration_management_cd_nk\decimal(19,0)};{insert_date\string\insert_date\varchar};{insert_id\string\insert_id\varchar};{modify_date\string\modify_date\varchar};{modify_id\string\modify_id\varchar}
PRIMARY_KEY=[mdm_integration_management_cd,apply_start_date,insert_date,insert_id]
[IF-MDM-PR-004]
IF_ID=IF-MDM-PR-004
SCHEMA_NAME=dwh_raw
TABLE_NAME=dgroup
IF_GROUP=PR-IN-001
JOBNET_ID=DWH-JN-MDM-PR-001
MAPPING_INFO={lgroup\string\lgroup\varchar};{mgroup\string\mgroup\varchar};{sgroup\string\sgroup\varchar};{dgroup\string\dgroup\varchar};{dgroup_name\string\dgroup_name\varchar};{lgroup_nk\string\lgroup_nk\varchar};{insert_date\string\insert_date\varchar};{insert_id\string\insert_id\varchar};{modify_date\string\modify_date\varchar};{modify_id\string\modify_id\varchar}
PRIMARY_KEY=[lgroup,mgroup,sgroup,dgroup,insert_date,insert_id]
[IF-MDM-PR-005]
IF_ID=IF-MDM-PR-005
SCHEMA_NAME=dwh_raw
TABLE_NAME=business_segment
IF_GROUP=PR-IN-001
JOBNET_ID=DWH-JN-MDM-PR-001
MAPPING_INFO={business_segment\string\business_segment\varchar};{business_segment_name\string\business_segment_name\varchar};{business_segment_nk\string\business_segment_nk\varchar};{insert_date\string\insert_date\varchar};{insert_id\string\insert_id\varchar};{modify_date\string\modify_date\varchar};{modify_id\string\modify_id\varchar}
PRIMARY_KEY=[business_segment,insert_date,insert_id]
[IF-MDM-PR-006]
IF_ID=IF-MDM-PR-006
SCHEMA_NAME=dwh_raw
TABLE_NAME=product_series
IF_GROUP=PR-IN-001
JOBNET_ID=DWH-JN-MDM-PR-001
MAPPING_INFO={product_series\string\product_series\varchar};{product_series_name\string\product_series_name\varchar};{product_series_nk\string\product_series_nk\varchar};{insert_date\string\insert_date\varchar};{insert_id\string\insert_id\varchar};{modify_date\string\modify_date\varchar};{modify_id\string\modify_id\varchar}
PRIMARY_KEY=[product_series,insert_date,insert_id]
[IF-MDM-PR-007]
IF_ID=IF-MDM-PR-007
SCHEMA_NAME=dwh_raw
TABLE_NAME=product_segment
IF_GROUP=PR-IN-001
JOBNET_ID=DWH-JN-MDM-PR-001
MAPPING_INFO={product_segment\string\product_segment\varchar};{product_segment_name\string\product_segment_name\varchar};{product_segment_nk\string\product_segment_nk\varchar};{insert_date\string\insert_date\varchar};{insert_id\string\insert_id\varchar};{modify_date\string\modify_date\varchar};{modify_id\string\modify_id\varchar}
PRIMARY_KEY=[product_segment,insert_date,insert_id]
[IF-MDM-PR-008]
IF_ID=IF-MDM-PR-008
SCHEMA_NAME=dwh_raw
TABLE_NAME=product_cat
IF_GROUP=PR-IN-001
JOBNET_ID=DWH-JN-MDM-PR-001
MAPPING_INFO={product_cat\string\product_cat\varchar};{product_cat_name\string\product_cat_name\varchar};{product_cat_nk\string\product_cat_nk\varchar};{insert_date\string\insert_date\varchar};{insert_id\string\insert_id\varchar};{modify_date\string\modify_date\varchar};{modify_id\string\modify_id\varchar}
PRIMARY_KEY=[product_cat,insert_date,insert_id]
[IF-MDM-PR-009]
IF_ID=IF-MDM-PR-009
SCHEMA_NAME=dwh_raw
TABLE_NAME=product_linkage
IF_GROUP=PR-IN-001
JOBNET_ID=DWH-JN-MDM-PR-001
MAPPING_INFO={mdm_integration_management_cd\string\mdm_integration_management_cd\decimal(19,0)};{product_picture_id\string\product_picture_id\decimal(10,0)};{product_no\string\product_no\decimal(10,0)};{mail_order_product_cd\string\mail_order_product_cd\varchar};{store_sales_product_cd\string\store_sales_product_cd\varchar};{warehouse_management_cd\string\warehouse_management_cd\varchar};{jan\string\jan\varchar};{jan_issue_flg\string\jan_issue_flg\varchar};{main_product_no\string\main_product_no\decimal(10,0)};{core_product_name\string\core_product_name\varchar};{web_product_name\string\web_product_name\varchar};{product_name\string\product_name\varchar};{registration_name\string\registration_name\varchar};{law_cat_cd\string\law_cat_cd\varchar};{product_segment\string\product_segment\varchar};{business_segment\string\business_segment\varchar};{product_cat\string\product_cat\varchar};{product_series\string\product_series\varchar};{sale_start_date\string\sale_start_date\varchar};{period_set_sales_channel_1\string\period_set_sales_channel_1\varchar};{sales_channel_1_sale_start_date\string\sales_channel_1_sale_start_date\varchar};{sales_channel_1_sale_end_date\string\sales_channel_1_sale_end_date\varchar};{period_set_sales_channel_2\string\period_set_sales_channel_2\varchar};{sales_channel_2_sale_start_date\string\sales_channel_2_sale_start_date\varchar};{sales_channel_2_sale_end_date\string\sales_channel_2_sale_end_date\varchar};{period_set_sales_channel_3\string\period_set_sales_channel_3\varchar};{sales_channel_3_sale_start_date\string\sales_channel_3_sale_start_date\varchar};{sales_channel_3_sale_end_date\string\sales_channel_3_sale_end_date\varchar};{sale_status\string\sale_status\varchar};{lgroup\string\lgroup\varchar};{mgroup\string\mgroup\varchar};{sgroup\string\sgroup\varchar};{dgroup\string\dgroup\varchar};{product_type\string\product_type\varchar};{core_department\string\core_department\varchar};{accountin_pattern_gb\string\accountin_pattern_gb\varchar};{material\string\material\varchar};{preferential_product_flg\string\preferential_product_flg\varchar};{set_product_flg\string\set_product_flg\varchar};{set_composition_flg\string\set_composition_flg\varchar};{company_sales_buy_flg\string\company_sales_buy_flg\varchar};{emprate_pms_flg\string\emprate_pms_flg\varchar};{age_limit_cd\string\age_limit_cd\varchar};{store_po_gb\string\store_po_gb\varchar};{web\string\web\varchar};{callcenter\string\callcenter\varchar};{before_renewal_product_no\string\before_renewal_product_no\varchar};{dep\string\dep\varchar};{representative_product_cd\string\representative_product_cd\varchar};{order_per_order_max\string\order_per_order_max\decimal(10,0)};{buttobi_subsc_bundle_yn\string\buttobi_subsc_bundle_yn\varchar};{return_yn\string\return_yn\varchar};{exch_yn\string\exch_yn\varchar};{lot_management_target_product\string\lot_management_target_product\varchar};{reduction_base\string\reduction_base\varchar};{depth\string\depth\decimal(10,0)};{width\string\width\decimal(10,0)};{height\string\height\decimal(10,0)};{trade_cnt\string\trade_cnt\decimal(10,0)};{weight\string\weight\decimal(7,2)};{outerbox_depth\string\outerbox_depth\decimal(10,0)};{outerbox_width\string\outerbox_width\decimal(10,0)};{outerbox_height\string\outerbox_height\decimal(10,0)};{outerbox_weight\string\outerbox_weight\decimal(7,2)};{case_per_include_cnt\string\case_per_include_cnt\decimal(10,0)};{insertion_depth\string\insertion_depth\decimal(10,0)};{insertion_width\string\insertion_width\decimal(10,0)};{insertion_height\string\insertion_height\decimal(10,0)};{palette_stack_cnt_face\string\palette_stack_cnt_face\decimal(10,0)};{palette_stack_cnt_lavel\string\palette_stack_cnt_lavel\decimal(10,0)};{contents\string\contents\varchar};{nekoposu_volume_rate\string\nekoposu_volume_rate\decimal(10,0)};{outside_home_volume_rate\string\outside_home_volume_rate\decimal(10,0)};{color_name\string\color_name\varchar};{color_cd\string\color_cd\varchar};{original_color_cd\string\original_color_cd\varchar};{size_name\string\size_name\varchar};{size_cd\string\size_cd\varchar};{shape_name\string\shape_name\varchar};{shape_cd\string\shape_cd\varchar};{season\string\season\varchar};{unit_cd\string\unit_cd\varchar};{buy_send_reservation_flg\string\buy_send_reservation_flg\varchar};{delivery_notice_undisplay_flg\string\delivery_notice_undisplay_flg\varchar};{airmail_rack_yn\string\airmail_rack_yn\varchar};{mail_delivery_flg\string\mail_delivery_flg\varchar};{outside_home_receive_service_flg\string\outside_home_receive_service_flg\varchar};{out_indicate_warehouse\string\out_indicate_warehouse\varchar};{warehouse_assembly_set_product_flg\string\warehouse_assembly_set_product_flg\varchar};{sagawa_yn_flg\string\sagawa_yn_flg\varchar};{folding_flg\string\folding_flg\varchar};{vender\string\vender\varchar};{use_point_cnt\string\use_point_cnt\decimal(10,0)};{composition_oms_link_flg\string\composition_oms_link_flg\varchar};{mdm_integration_management_cd_nk\string\mdm_integration_management_cd_nk\decimal(19,0)};{insert_date\string\insert_date\varchar};{insert_id\string\insert_id\varchar};{modify_date\string\modify_date\varchar};{modify_id\string\modify_id\varchar}
PRIMARY_KEY=[mdm_integration_management_cd,insert_date,insert_id]
[IF-MDM-PR-010]
IF_ID=IF-MDM-PR-010
SCHEMA_NAME=dwh_raw
TABLE_NAME=sgroup
IF_GROUP=PR-IN-001
JOBNET_ID=DWH-JN-MDM-PR-001
MAPPING_INFO={lgroup\string\lgroup\varchar};{mgroup\string\mgroup\varchar};{sgroup\string\sgroup\varchar};{sgroup_name\string\sgroup_name\varchar};{lgroup_nk\string\lgroup_nk\varchar};{insert_date\string\insert_date\varchar};{insert_id\string\insert_id\varchar};{modify_date\string\modify_date\varchar};{modify_id\string\modify_id\varchar}
PRIMARY_KEY=[lgroup,mgroup,sgroup,insert_date,insert_id]
[IF-MDM-PR-011]
IF_ID=IF-MDM-PR-011
SCHEMA_NAME=dwh_raw
TABLE_NAME=mgroup
IF_GROUP=PR-IN-001
JOBNET_ID=DWH-JN-MDM-PR-001
MAPPING_INFO={lgroup\string\lgroup\varchar};{mgroup\string\mgroup\varchar};{mgroup_name\string\mgroup_name\varchar};{lgroup_nk\string\lgroup_nk\varchar};{insert_date\string\insert_date\varchar};{insert_id\string\insert_id\varchar};{modify_date\string\modify_date\varchar};{modify_id\string\modify_id\varchar}
PRIMARY_KEY=[lgroup,mgroup,insert_date,insert_id]
[IF-MDM-PR-012]
IF_ID=IF-MDM-PR-012
SCHEMA_NAME=dwh_raw
TABLE_NAME=core_department
IF_GROUP=PR-IN-001
JOBNET_ID=DWH-JN-MDM-PR-001
MAPPING_INFO={core_department\string\core_department\varchar};{core_department_name\string\core_department_name\varchar};{core_department_nk\string\core_department_nk\varchar};{insert_date\string\insert_date\varchar};{insert_id\string\insert_id\varchar};{modify_date\string\modify_date\varchar};{modify_id\string\modify_id\varchar}
PRIMARY_KEY=[core_department,insert_date,insert_id]
[IF-MDM-PR-013]
IF_ID=IF-MDM-PR-013
SCHEMA_NAME=dwh_raw
TABLE_NAME=product_type
IF_GROUP=PR-IN-001
JOBNET_ID=DWH-JN-MDM-PR-001
MAPPING_INFO={product_type\string\product_type\varchar};{product_type_name\string\product_type_name\varchar};{product_type_nk\string\product_type_nk\varchar};{insert_date\string\insert_date\varchar};{insert_id\string\insert_id\varchar};{modify_date\string\modify_date\varchar};{modify_id\string\modify_id\varchar}
PRIMARY_KEY=[product_type,insert_date,insert_id]
[IF-MDM-PR-014]
IF_ID=IF-MDM-PR-014
SCHEMA_NAME=dwh_raw
TABLE_NAME=lgroup
IF_GROUP=PR-IN-001
JOBNET_ID=DWH-JN-MDM-PR-001
MAPPING_INFO={lgroup\string\lgroup\varchar};{lgroup_name\string\lgroup_name\varchar};{lgroup_nk\string\lgroup_nk\varchar};{insert_date\string\insert_date\varchar};{insert_id\string\insert_id\varchar};{modify_date\string\modify_date\varchar};{modify_id\string\modify_id\varchar}
PRIMARY_KEY=[lgroup,insert_date,insert_id]
[IF-OMS-PR-001]
IF_ID=IF-OMS-PR-001
SCHEMA_NAME=dwh_raw
TABLE_NAME=category
IF_GROUP=PR-IN-002
JOBNET_ID=DWH-JN-OMS-PR-001
MAPPING_INFO={category_code\string\category_code\varchar};{category_name_pc\string\category_name_pc\varchar};{category_name_sp\string\category_name_sp\varchar};{parent_category_code\string\parent_category_code\varchar};{path\string\path\varchar};{depth\string\depth\decimal(2,0)};{display_order\string\display_order\decimal(8,0)};{commodity_count\string\commodity_count\decimal(12,0)};{last_related_count_datetime\string\last_related_count_datetime\varchar};{public_commodity_count\string\public_commodity_count\decimal(12,0)};{last_public_count_datetime\string\last_public_count_datetime\varchar};{orm_rowid\string\orm_rowid\decimal(38,0)};{created_user\string\created_user\varchar};{created_datetime\string\created_datetime\varchar};{updated_user\string\updated_user\varchar};{updated_datetime\string\updated_datetime\varchar}
PRIMARY_KEY=[category_code,category_name_pc,parent_category_code,path,depth,display_order,orm_rowid,created_user,created_datetime,updated_user,updated_datetime]
[IF-OMS-PR-002]
IF_ID=IF-OMS-PR-002
SCHEMA_NAME=dwh_raw
TABLE_NAME=category_commodity
IF_GROUP=PR-IN-002
JOBNET_ID=DWH-JN-OMS-PR-001
MAPPING_INFO={shop_code\string\shop_code\varchar};{category_code\string\category_code\varchar};{commodity_code\string\commodity_code\varchar};{category_search_path\string\category_search_path\varchar};{search_category_code0\string\search_category_code0\varchar};{search_category_code1\string\search_category_code1\varchar};{search_category_code2\string\search_category_code2\varchar};{search_category_code3\string\search_category_code3\varchar};{search_category_code4\string\search_category_code4\varchar};{search_category_code5\string\search_category_code5\varchar};{search_category_code6\string\search_category_code6\varchar};{orm_rowid\string\orm_rowid\decimal(38,0)};{created_user\string\created_user\varchar};{created_datetime\string\created_datetime\varchar};{updated_user\string\updated_user\varchar};{updated_datetime\string\updated_datetime\varchar}
PRIMARY_KEY=[shop_code,category_code,commodity_code,category_search_path,search_category_code0,search_category_code1,search_category_code2,search_category_code3,search_category_code4,search_category_code5,search_category_code6,orm_rowid,created_user,created_datetime,updated_user,updated_datetime]
[IF-OMS-PR-003]
IF_ID=IF-OMS-PR-003
SCHEMA_NAME=dwh_raw
TABLE_NAME=commodity_header
IF_GROUP=PR-IN-003
JOBNET_ID=DWH-JN-OMS-PR-002
MAPPING_INFO={shop_code\string\shop_code\varchar};{commodity_code\string\commodity_code\varchar};{commodity_name\string\commodity_name\varchar};{commodity_type\string\commodity_type\decimal(1,0)};{represent_sku_code\string\represent_sku_code\varchar};{represent_sku_unit_price\string\represent_sku_unit_price\decimal(8,0)};{stock_status_no\string\stock_status_no\decimal(8,0)};{stock_management_type\string\stock_management_type\decimal(1,0)};{age_limit_code\string\age_limit_code\decimal(8,0)};{commodity_tax_type\string\commodity_tax_type\decimal(1,0)};{tax_group_code\string\tax_group_code\varchar};{short_description\string\short_description\varchar};{commodity_search_words\string\commodity_search_words\varchar};{prior_printing_description\string\prior_printing_description\varchar};{posterior_printing_description\string\posterior_printing_description\varchar};{delivery_description\string\delivery_description\varchar};{sale_start_datetime\string\sale_start_datetime\varchar};{sale_end_datetime\string\sale_end_datetime\varchar};{discount_price_start_datetime\string\discount_price_start_datetime\varchar};{discount_price_end_datetime\string\discount_price_end_datetime\varchar};{reservation_start_datetime\string\reservation_start_datetime\varchar};{reservation_end_datetime\string\reservation_end_datetime\varchar};{prior_printing_start_date\string\prior_printing_start_date\varchar};{prior_printing_end_date\string\prior_printing_end_date\varchar};{posterior_printing_start_date\string\posterior_printing_start_date\varchar};{posterior_printing_end_date\string\posterior_printing_end_date\varchar};{delivery_type_no\string\delivery_type_no\decimal(8,0)};{sales_method_type\string\sales_method_type\decimal(1,0)};{manufacturer_model_no\string\manufacturer_model_no\varchar};{link_url\string\link_url\varchar};{recommend_commodity_rank\string\recommend_commodity_rank\decimal(8,0)};{commodity_popular_rank\string\commodity_popular_rank\decimal(8,0)};{commodity_standard1_name\string\commodity_standard1_name\varchar};{commodity_standard2_name\string\commodity_standard2_name\varchar};{commodity_point_rate\string\commodity_point_rate\decimal(3,0)};{commodity_point_start_datetime\string\commodity_point_start_datetime\varchar};{commodity_point_end_datetime\string\commodity_point_end_datetime\varchar};{sale_flg\string\sale_flg\decimal(1,0)};{noshi_effective_flg\string\noshi_effective_flg\decimal(1,0)};{arrival_goods_flg\string\arrival_goods_flg\decimal(1,0)};{oneshot_order_limit\string\oneshot_order_limit\decimal(8,0)};{standard_image_type\string\standard_image_type\decimal(1,0)};{purchasing_confirm_flg_pc\string\purchasing_confirm_flg_pc\decimal(1,0)};{purchasing_confirm_flg_sp\string\purchasing_confirm_flg_sp\decimal(1,0)};{commodity_kind\string\commodity_kind\varchar};{keihi_hurikae_target_flg\string\keihi_hurikae_target_flg\decimal(1,0)};{charge_user_code\string\charge_user_code\decimal(38,0)};{commodity_remark\string\commodity_remark\varchar};{channel_cc_sale_flg\string\channel_cc_sale_flg\decimal(1,0)};{channel_ec_sale_flg\string\channel_ec_sale_flg\decimal(1,0)};{shipping_charge_target_flg\string\shipping_charge_target_flg\decimal(1,0)};{first_purchase_limit_flg\string\first_purchase_limit_flg\decimal(1,0)};{purchase_hold_flg\string\purchase_hold_flg\decimal(1,0)};{commodity_exclude_flg\string\commodity_exclude_flg\decimal(1,0)};{commodity_subsubcategory_code\string\commodity_subsubcategory_code\varchar};{pack_calc_pattern\string\pack_calc_pattern\decimal(2,0)};{pad_type\string\pad_type\decimal(2,0)};{fall_down_flg\string\fall_down_flg\decimal(1,0)};{height\string\height\decimal(5,0)};{width\string\width\decimal(5,0)};{deepness\string\deepness\decimal(5,0)};{weight\string\weight\decimal(7,0)};{tracking_out_flg\string\tracking_out_flg\decimal(1,0)};{mdm_management_code\string\mdm_management_code\decimal(20,0)};{commodity_segment\string\commodity_segment\varchar};{business_segment\string\business_segment\varchar};{commodity_group\string\commodity_group\varchar};{commodity_series\string\commodity_series\varchar};{core_department\string\core_department\varchar};{accounting_pattern_type\string\accounting_pattern_type\varchar};{return_enabled_flg\string\return_enabled_flg\varchar};{exchange_enabled_flg\string\exchange_enabled_flg\varchar};{exterior_box_weight\string\exterior_box_weight\varchar};{nekoposu_volume_rate\string\nekoposu_volume_rate\decimal(3,0)};{warehouse_assembly_flg\string\warehouse_assembly_flg\varchar};{mail_delivery_flg\string\mail_delivery_flg\varchar};{before_renewal_commodity_code\string\before_renewal_commodity_code\varchar};{preorder_enable_days\string\preorder_enable_days\decimal(2,0)};{main_product_no\string\main_product_no\varchar};{product_no\string\product_no\varchar};{orm_rowid\string\orm_rowid\decimal(38,0)};{created_user\string\created_user\varchar};{created_datetime\string\created_datetime\varchar};{updated_user\string\updated_user\varchar};{updated_datetime\string\updated_datetime\varchar}
PRIMARY_KEY=[shop_code,commodity_code,commodity_name,commodity_type,represent_sku_code,represent_sku_unit_price,stock_management_type,age_limit_code,commodity_tax_type,tax_group_code,sale_start_datetime,sale_end_datetime,delivery_type_no,sales_method_type,sale_flg,commodity_kind,keihi_hurikae_target_flg,channel_cc_sale_flg,channel_ec_sale_flg,shipping_charge_target_flg,first_purchase_limit_flg,purchase_hold_flg,commodity_exclude_flg,pack_calc_pattern,pad_type,fall_down_flg,tracking_out_flg,orm_rowid,created_user,created_datetime,updated_user,updated_datetime]
[IF-OMS-PR-004]
IF_ID=IF-OMS-PR-004
SCHEMA_NAME=dwh_raw
TABLE_NAME=commodity_detail
IF_GROUP=PR-IN-003
JOBNET_ID=DWH-JN-OMS-PR-002
MAPPING_INFO={shop_code\string\shop_code\varchar};{sku_code\string\sku_code\varchar};{commodity_code\string\commodity_code\varchar};{unit_price\string\unit_price\decimal(8,0)};{discount_price\string\discount_price\decimal(8,0)};{reservation_price\string\reservation_price\decimal(8,0)};{jan_code\string\jan_code\varchar};{standard_detail1_name\string\standard_detail1_name\varchar};{standard_detail2_name\string\standard_detail2_name\varchar};{hinban_code\string\hinban_code\varchar};{hinban_kind\string\hinban_kind\varchar};{member_price_applied_flg\string\member_price_applied_flg\decimal(1,0)};{member_price_discount_rate\string\member_price_discount_rate\decimal(3,0)};{member_price\string\member_price\decimal(8,0)};{air_transport_flg\string\air_transport_flg\decimal(1,0)};{commodity_prod_pack_type\string\commodity_prod_pack_type\varchar};{delivery_note_no_disp_flg\string\delivery_note_no_disp_flg\decimal(1,0)};{reduction_point\string\reduction_point\decimal(10,0)};{orm_rowid\string\orm_rowid\decimal(38,0)};{created_user\string\created_user\varchar};{created_datetime\string\created_datetime\varchar};{updated_user\string\updated_user\varchar};{updated_datetime\string\updated_datetime\varchar}
PRIMARY_KEY=[shop_code,sku_code,commodity_code,unit_price,hinban_code,hinban_kind,member_price_applied_flg,member_price_discount_rate,member_price,air_transport_flg,commodity_prod_pack_type,delivery_note_no_disp_flg,orm_rowid,created_user,created_datetime,updated_user,updated_datetime]
[IF-OMS-PR-005]
IF_ID=IF-OMS-PR-005
SCHEMA_NAME=dwh_raw
TABLE_NAME=set_commodity_composition
IF_GROUP=0
JOBNET_ID=DWH-JN-OMS-PR-003
MAPPING_INFO={shop_code\string\shop_code\varchar};{commodity_code\string\commodity_code\varchar};{child_commodity_code\string\child_commodity_code\varchar};{composition_quantity\string\composition_quantity\decimal(2,0)};{composition_order\string\composition_order\decimal(2,0)};{orm_rowid\string\orm_rowid\decimal(38,0)};{created_user\string\created_user\varchar};{created_datetime\string\created_datetime\varchar};{updated_user\string\updated_user\varchar};{updated_datetime\string\updated_datetime\varchar}
PRIMARY_KEY=[shop_code,commodity_code,child_commodity_code,composition_quantity,composition_order,orm_rowid,created_user,created_datetime,updated_user,updated_datetime]
[IF-OMS-PR-006]
IF_ID=IF-OMS-PR-006
SCHEMA_NAME=dwh_raw
TABLE_NAME=regular_sale_base
IF_GROUP=PR-IN-004
JOBNET_ID=DWH-JN-OMS-PR-004
MAPPING_INFO={shop_code\string\shop_code\varchar};{regular_sale_code\string\regular_sale_code\varchar};{sku_code\string\sku_code\varchar};{commodity_code\string\commodity_code\varchar};{regular_cycle_kind_list\string\regular_cycle_kind_list\varchar};{regular_cycle_days_list\string\regular_cycle_days_list\varchar};{regular_cycle_months_list\string\regular_cycle_months_list\varchar};{regular_sale_stop_from\string\regular_sale_stop_from\decimal(5,0)};{regular_sale_stop_to\string\regular_sale_stop_to\decimal(5,0)};{orm_rowid\string\orm_rowid\decimal(38,0)};{created_user\string\created_user\varchar};{created_datetime\string\created_datetime\varchar};{updated_user\string\updated_user\varchar};{updated_datetime\string\updated_datetime\varchar}
PRIMARY_KEY=[shop_code,regular_sale_code,sku_code,commodity_code,orm_rowid,created_user,created_datetime,updated_user,updated_datetime]
[IF-OMS-PR-007]
IF_ID=IF-OMS-PR-007
SCHEMA_NAME=dwh_raw
TABLE_NAME=regular_sale_composition
IF_GROUP=PR-IN-004
JOBNET_ID=DWH-JN-OMS-PR-004
MAPPING_INFO={shop_code\string\shop_code\varchar};{regular_sale_code\string\regular_sale_code\varchar};{regular_sale_composition_no\string\regular_sale_composition_no\varchar};{regular_sale_composition_name\string\regular_sale_composition_name\varchar};{regular_order_count_min_limit\string\regular_order_count_min_limit\decimal(5,0)};{regular_order_count_max_limit\string\regular_order_count_max_limit\decimal(5,0)};{regular_order_count_interval\string\regular_order_count_interval\decimal(5,0)};{retail_price\string\retail_price\decimal(8,0)};{regular_sale_commodity_point\string\regular_sale_commodity_point\decimal(3,0)};{display_order\string\display_order\decimal(8,0)};{orm_rowid\string\orm_rowid\decimal(38,0)};{created_user\string\created_user\varchar};{created_datetime\string\created_datetime\varchar};{updated_user\string\updated_user\varchar};{updated_datetime\string\updated_datetime\varchar}
PRIMARY_KEY=[shop_code,regular_sale_code,regular_sale_composition_no,regular_order_count_min_limit,regular_order_count_interval,retail_price,orm_rowid,created_user,created_datetime,updated_user,updated_datetime]
[IF-OMS-PR-008]
IF_ID=IF-OMS-PR-008
SCHEMA_NAME=dwh_raw
TABLE_NAME=regular_sale_payment
IF_GROUP=PR-IN-004
JOBNET_ID=DWH-JN-OMS-PR-004
MAPPING_INFO={shop_code\string\shop_code\varchar};{regular_sale_code\string\regular_sale_code\varchar};{payment_method_no\string\payment_method_no\decimal(8,0)};{orm_rowid\string\orm_rowid\decimal(38,0)};{created_user\string\created_user\varchar};{created_datetime\string\created_datetime\varchar};{updated_user\string\updated_user\varchar};{updated_datetime\string\updated_datetime\varchar}
PRIMARY_KEY=[shop_code,regular_sale_code,payment_method_no,orm_rowid,created_user,created_datetime,updated_user,updated_datetime]
[IF-OMS-PR-009]
IF_ID=IF-OMS-PR-009
SCHEMA_NAME=dwh_raw
TABLE_NAME=regular_sale_commodity
IF_GROUP=PR-IN-004
JOBNET_ID=DWH-JN-OMS-PR-004
MAPPING_INFO={shop_code\string\shop_code\varchar};{regular_sale_code\string\regular_sale_code\varchar};{regular_sale_composition_no\string\regular_sale_composition_no\varchar};{sku_code\string\sku_code\varchar};{commodity_code\string\commodity_code\varchar};{display_order\string\display_order\decimal(8,0)};{regular_sale_commodity_type\string\regular_sale_commodity_type\varchar};{regular_sale_commodity_point\string\regular_sale_commodity_point\decimal(3,0)};{difference_price\string\difference_price\decimal(8,0)};{orm_rowid\string\orm_rowid\decimal(38,0)};{created_user\string\created_user\varchar};{created_datetime\string\created_datetime\varchar};{updated_user\string\updated_user\varchar};{updated_datetime\string\updated_datetime\varchar}
PRIMARY_KEY=[shop_code,regular_sale_code,regular_sale_composition_no,sku_code,commodity_code,display_order,orm_rowid,created_user,created_datetime,updated_user,updated_datetime]
[IF-OMS-RT-001]
IF_ID=IF-OMS-RT-001
SCHEMA_NAME=dwh_raw
TABLE_NAME=returns_header
IF_GROUP=RT-IN-001
JOBNET_ID=DWH-JN-OMS-RT-001
MAPPING_INFO={henpin_request_no\string\henpin_request_no\varchar};{order_no\string\order_no\varchar};{customer_code\string\customer_code\varchar};{neo_customer_no\string\neo_customer_no\varchar};{henpin_confirm_status\string\henpin_confirm_status\varchar};{henpin_request_datetime\string\henpin_request_datetime\varchar};{henpin_confirm_datetime\string\henpin_confirm_datetime\varchar};{henpin_recieve_user_code\string\henpin_recieve_user_code\decimal(38,0)};{henpin_change_kbn\string\henpin_change_kbn\varchar};{henpin_reason_kbn\string\henpin_reason_kbn\varchar};{henpin_bill_cancel_flg\string\henpin_bill_cancel_flg\decimal(1,0)};{henpin_shipping_flg\string\henpin_shipping_flg\decimal(1,0)};{henpin_souko_kbn\string\henpin_souko_kbn\varchar};{total_shipping_amount\string\total_shipping_amount\decimal(10,0)};{adjustment_amount\string\adjustment_amount\decimal(10,0)};{bill_price\string\bill_price\decimal(10,0)};{bill_price_bf_henpin\string\bill_price_bf_henpin\decimal(10,0)};{appropriation_amount_bf_henpin\string\appropriation_amount_bf_henpin\decimal(10,0)};{deposit_occur_amount\string\deposit_occur_amount\decimal(10,0)};{delivery_note_republish_flg\string\delivery_note_republish_flg\decimal(1,0)};{credit_cancel_flg\string\credit_cancel_flg\decimal(1,0)};{request_af_henpin_confirm_kbn\string\request_af_henpin_confirm_kbn\varchar};{amzn_refund_flg\string\amzn_refund_flg\decimal(1,0)};{amzn_refund_id\string\amzn_refund_id\varchar};{amzn_refund_status\string\amzn_refund_status\varchar};{amzn_refund_initiated_datetime\string\amzn_refund_initiated_datetime\varchar};{amzn_refunded_datetime\string\amzn_refunded_datetime\varchar};{refund_amount\string\refund_amount\decimal(10,0)};{bank_name\string\bank_name\varchar};{bank_branch_name\string\bank_branch_name\varchar};{account_type\string\account_type\varchar};{account_no\string\account_no\varchar};{account_name\string\account_name\varchar};{registered_mail_address\string\registered_mail_address\varchar};{registered_mail_name\string\registered_mail_name\varchar};{registered_remarks\string\registered_remarks\varchar};{appropriation_date\string\appropriation_date\varchar};{appropriation_amount\string\appropriation_amount\decimal(10,0)};{wms_contact_flg\string\wms_contact_flg\decimal(1,0)};{wms_auto_confirm_flg\string\wms_auto_confirm_flg\decimal(1,0)};{sales_recording_date\string\sales_recording_date\varchar};{sales_recording_flg\string\sales_recording_flg\decimal(1,0)};{inquiry_kanri_no\string\inquiry_kanri_no\varchar};{cancel_flg\string\cancel_flg\decimal(1,0)};{change_user_code\string\change_user_code\decimal(38,0)};{before_grant_point_total\string\before_grant_point_total\decimal(10,0)};{before_reduction_point_total\string\before_reduction_point_total\decimal(10,0)};{after_grant_plan_point_prod\string\after_grant_plan_point_prod\decimal(10,0)};{after_grant_plan_point_other\string\after_grant_plan_point_other\decimal(10,0)};{after_grant_plan_point_total\string\after_grant_plan_point_total\decimal(10,0)};{after_grant_point_prod\string\after_grant_point_prod\decimal(10,0)};{after_grant_point_other\string\after_grant_point_other\decimal(10,0)};{after_grant_point_total\string\after_grant_point_total\decimal(10,0)};{after_reduction_plan_point_total\string\after_reduction_plan_point_total\decimal(10,0)};{after_reduction_point_total\string\after_reduction_point_total\decimal(10,0)};{orm_rowid\string\orm_rowid\decimal(38,0)};{created_user\string\created_user\varchar};{created_datetime\string\created_datetime\varchar};{updated_user\string\updated_user\varchar};{updated_datetime\string\updated_datetime\varchar}
PRIMARY_KEY=[henpin_request_no,order_no,customer_code,henpin_confirm_status,henpin_request_datetime,henpin_change_kbn,henpin_reason_kbn,henpin_bill_cancel_flg,henpin_shipping_flg,henpin_souko_kbn,total_shipping_amount,adjustment_amount,bill_price,bill_price_bf_henpin,appropriation_amount_bf_henpin,deposit_occur_amount,delivery_note_republish_flg,credit_cancel_flg,request_af_henpin_confirm_kbn,amzn_refund_flg,wms_contact_flg,wms_auto_confirm_flg,sales_recording_flg,cancel_flg,orm_rowid,created_user,created_datetime,updated_user,updated_datetime]
[IF-OMS-RT-002]
IF_ID=IF-OMS-RT-002
SCHEMA_NAME=dwh_raw
TABLE_NAME=returns_detail
IF_GROUP=RT-IN-001
JOBNET_ID=DWH-JN-OMS-RT-001
MAPPING_INFO={henpin_request_no\string\henpin_request_no\varchar};{shipping_no\string\shipping_no\varchar};{shipping_detail_no\string\shipping_detail_no\decimal(16,0)};{order_no\string\order_no\varchar};{order_detail_no\string\order_detail_no\decimal(16,0)};{shop_code\string\shop_code\varchar};{sku_code\string\sku_code\varchar};{commodity_code\string\commodity_code\varchar};{commodity_name\string\commodity_name\varchar};{commodity_kind\string\commodity_kind\varchar};{baitai_code\string\baitai_code\varchar};{baitai_name\string\baitai_name\varchar};{hinban_code\string\hinban_code\varchar};{benefits_code\string\benefits_code\varchar};{benefits_name\string\benefits_name\varchar};{henpin_qt\string\henpin_qt\decimal(5,0)};{regular_contract_no\string\regular_contract_no\varchar};{unit_price\string\unit_price\decimal(8,0)};{discount_price\string\discount_price\decimal(8,0)};{discount_amount\string\discount_amount\decimal(8,0)};{retail_price\string\retail_price\decimal(8,0)};{retail_tax_group_code\string\retail_tax_group_code\varchar};{retail_tax_no\string\retail_tax_no\decimal(3,0)};{retail_tax_rate\string\retail_tax_rate\decimal(3,0)};{retail_tax\string\retail_tax\decimal(10,0)};{commodity_tax\string\commodity_tax\decimal(10,0)};{commodity_tax_type\string\commodity_tax_type\decimal(1,0)};{purchasing_amount\string\purchasing_amount\decimal(8,0)};{henpin_price\string\henpin_price\decimal(8,0)};{henpin_yoyaku_qt\string\henpin_yoyaku_qt\decimal(5,0)};{change_qt\string\change_qt\decimal(5,0)};{henpin_support_kind\string\henpin_support_kind\varchar};{wms_henpin_rireki_no\string\wms_henpin_rireki_no\varchar};{copy_soko_shiji\string\copy_soko_shiji\varchar};{grant_plan_point_prod_detail\string\grant_plan_point_prod_detail\decimal(10,0)};{reduction_plan_point_prod_detail\string\reduction_plan_point_prod_detail\decimal(10,0)};{campaign_instructions_code\string\campaign_instructions_code\varchar};{campaign_instructions_name\string\campaign_instructions_name\varchar};{orm_rowid\string\orm_rowid\decimal(38,0)};{created_user\string\created_user\varchar};{created_datetime\string\created_datetime\varchar};{updated_user\string\updated_user\varchar};{updated_datetime\string\updated_datetime\varchar}
PRIMARY_KEY=[henpin_request_no,shipping_no,shipping_detail_no,order_no,order_detail_no,shop_code,sku_code,commodity_code,commodity_name,commodity_kind,baitai_code,baitai_name,hinban_code,henpin_qt,unit_price,retail_price,retail_tax_group_code,retail_tax_no,retail_tax_rate,retail_tax,commodity_tax,commodity_tax_type,purchasing_amount,henpin_price,henpin_yoyaku_qt,change_qt,orm_rowid,created_user,created_datetime,updated_user,updated_datetime]
[IF-POS-SL-002]
IF_ID=IF-POS-SL-002
SCHEMA_NAME=dwh_raw
TABLE_NAME=sales_info_alignment_payment
IF_GROUP=SL-IN-001
JOBNET_ID=DWH-JN-POS-SL-002
MAPPING_INFO={shop_cd\string\shop_cd\varchar};{register_num\string\register_num\decimal(11,0)};{business_date\string\business_date\varchar};{receipt_num\string\receipt_num\decimal(11,0)};{line_num\string\line_num\decimal(11,0)};{pay_kind\string\pay_kind\decimal(4,0)};{pay_cd\string\pay_cd\varchar};{credit_linkage_kind\string\credit_linkage_kind\decimal(4,0)};{claim_kind\string\claim_kind\decimal(4,0)};{pay_amount\string\pay_amount\decimal(11,0)};{change_amount\string\change_amount\decimal(11,0)};{surplus_amount\string\surplus_amount\decimal(11,0)};{coupon_num\string\coupon_num\varchar};{ticket_quantity\string\ticket_quantity\decimal(11,0)};{ticket_bar_cd\string\ticket_bar_cd\varchar};{is_worn_cash\string\is_worn_cash\decimal(4,0)};{is_emoney_cash_back\string\is_emoney_cash_back\decimal(4,0)};{numeric_reserve1\string\numeric_reserve1\decimal(11,0)};{numeric_reserve2\string\numeric_reserve2\decimal(11,0)};{numeric_reserve3\string\numeric_reserve3\decimal(11,0)};{numeric_reserve4\string\numeric_reserve4\decimal(11,0)};{numeric_reserve5\string\numeric_reserve5\decimal(11,0)};{numeric_reserve6\string\numeric_reserve6\decimal(11,0)};{numeric_reserve7\string\numeric_reserve7\decimal(11,0)};{numeric_reserve8\string\numeric_reserve8\decimal(11,0)};{numeric_reserve9\string\numeric_reserve9\decimal(11,0)};{numeric_reserve10\string\numeric_reserve10\decimal(11,0)};{string_reserve1\string\string_reserve1\varchar};{string_reserve2\string\string_reserve2\varchar};{string_reserve3\string\string_reserve3\varchar};{string_reserve4\string\string_reserve4\varchar};{string_reserve5\string\string_reserve5\varchar};{string_reserve6\string\string_reserve6\varchar};{string_reserve7\string\string_reserve7\varchar};{string_reserve8\string\string_reserve8\varchar};{string_reserve9\string\string_reserve9\varchar};{string_reserve10\string\string_reserve10\varchar}
PRIMARY_KEY=[shop_cd,register_num,business_date,receipt_num,line_num,pay_kind,pay_amount,change_amount,surplus_amount]
[IF-POS-SL-003]
IF_ID=IF-POS-SL-003
SCHEMA_NAME=dwh_raw
TABLE_NAME=sales_info_alignment_header
IF_GROUP=SL-IN-001
JOBNET_ID=DWH-JN-POS-SL-002
MAPPING_INFO={shop_cd\string\shop_cd\varchar};{register_num\string\register_num\decimal(11,0)};{business_date\string\business_date\varchar};{receipt_num\string\receipt_num\decimal(11,0)};{chit_num\string\chit_num\varchar};{system_datetime\string\system_datetime\varchar};{open_count\string\open_count\decimal(4,0)};{is_void\string\is_void\decimal(4,0)};{check_kind\string\check_kind\decimal(4,0)};{return_kind\string\return_kind\decimal(4,0)};{sub_check_kind\string\sub_check_kind\decimal(4,0)};{sales_group_cd\string\sales_group_cd\decimal(4,0)};{deposit_kind\string\deposit_kind\decimal(4,0)};{operator_cd\string\operator_cd\varchar};{operator_name\string\operator_name\varchar};{sales_man_cd\string\sales_man_cd\varchar};{sales_man_name\string\sales_man_name\varchar};{return_employee_cd\string\return_employee_cd\varchar};{return_employee_name\string\return_employee_name\varchar};{void_employee_cd\string\void_employee_cd\varchar};{void_employee_name\string\void_employee_name\varchar};{staff_sale_employee_cd\string\staff_sale_employee_cd\varchar};{staff_sale_employee_name\string\staff_sale_employee_name\varchar};{customer_cd\string\customer_cd\varchar};{customer_layer_cd\string\customer_layer_cd\varchar};{customer_layer_cd2\string\customer_layer_cd2\varchar};{purchase_motive_cd\string\purchase_motive_cd\varchar};{return_reason_cd\string\return_reason_cd\varchar};{void_reason_cd\string\void_reason_cd\varchar};{net_sales_amount_of_outside_tax\string\net_sales_amount_of_outside_tax\decimal(11,0)};{net_sales_amount_of_inside_tax\string\net_sales_amount_of_inside_tax\decimal(11,0)};{net_sales_amount_of_tax_free\string\net_sales_amount_of_tax_free\decimal(11,0)};{net_sales_outside_tax\string\net_sales_outside_tax\decimal(11,0)};{net_sales_inside_tax\string\net_sales_inside_tax\decimal(11,0)};{net_sales_quantity\string\net_sales_quantity\decimal(11,0)};{outside_sales_amount_of_outside_tax\string\outside_sales_amount_of_outside_tax\decimal(11,0)};{outside_sales_amount_of_inside_tax\string\outside_sales_amount_of_inside_tax\decimal(11,0)};{outside_sales_amount_of_tax_free\string\outside_sales_amount_of_tax_free\decimal(11,0)};{outside_sales_outside_tax\string\outside_sales_outside_tax\decimal(11,0)};{outside_sales_inside_tax\string\outside_sales_inside_tax\decimal(11,0)};{outside_sales_quantity\string\outside_sales_quantity\decimal(11,0)};{total_amount\string\total_amount\decimal(11,0)};{discount_amount\string\discount_amount\decimal(11,0)};{discount_tax_inclusive\string\discount_tax_inclusive\decimal(11,0)};{is_revenue_stamp\string\is_revenue_stamp\decimal(4,0)};{order_line_count\string\order_line_count\decimal(11,0)};{pay_line_count\string\pay_line_count\decimal(4,0)};{is_total_display\string\is_total_display\decimal(4,0)};{is_reduced_tax_rate_trade\string\is_reduced_tax_rate_trade\decimal(4,0)};{is_tax_free\string\is_tax_free\decimal(4,0)};{customers_num\string\customers_num\decimal(11,0)};{deliver_date\string\deliver_date\varchar};{sale_attribute_cd1\string\sale_attribute_cd1\varchar};{sale_attribute_cd2\string\sale_attribute_cd2\varchar};{sale_attribute_cd3\string\sale_attribute_cd3\varchar};{sale_attribute_cd4\string\sale_attribute_cd4\varchar};{campaign_no\string\campaign_no\varchar};{closing_date\string\closing_date\varchar};{return_date\string\return_date\varchar};{order_number\string\order_number\varchar};{employee_meal\string\employee_meal\decimal(4,0)};{employee_code\string\employee_code\varchar};{table_no\string\table_no\varchar};{acceptance_time\string\acceptance_time\varchar};{menu_cook_cmp_time\string\menu_cook_cmp_time\varchar};{menu_offer_cmp_time\string\menu_offer_cmp_time\varchar};{service_charge_amount_outside_tax\string\service_charge_amount_outside_tax\decimal(11,0)};{service_charge_amount_inside_tax\string\service_charge_amount_inside_tax\decimal(11,0)};{service_charge_tax_exclusive\string\service_charge_tax_exclusive\decimal(11,0)};{service_charge_tax_inclusive\string\service_charge_tax_inclusive\decimal(11,0)};{service_charge_amount1\string\service_charge_amount1\decimal(11,0)};{service_charge_amount2\string\service_charge_amount2\decimal(11,0)};{service_charge_minus_amount\string\service_charge_minus_amount\decimal(11,0)};{service_charge_minus_tax_inclusive\string\service_charge_minus_tax_inclusive\decimal(11,0)};{service_charge_target\string\service_charge_target\decimal(4,0)};{service_charge_button\string\service_charge_button\decimal(4,0)};{service_charge1_button\string\service_charge1_button\decimal(4,0)};{service_charge2_button\string\service_charge2_button\decimal(4,0)};{eat_in_amount\string\eat_in_amount\decimal(11,0)};{takeout_amount\string\takeout_amount\decimal(11,0)};{vein_employee_cd\string\vein_employee_cd\varchar};{is_vein_authentication\string\is_vein_authentication\decimal(4,0)};{out_calc_flg\string\out_calc_flg\decimal(4,0)};{sale_goods_flg\string\sale_goods_flg\varchar};{point_linkage_kind\string\point_linkage_kind\decimal(4,0)};{numeric_reserve1\string\numeric_reserve1\decimal(11,0)};{numeric_reserve2\string\numeric_reserve2\decimal(11,0)};{numeric_reserve3\string\numeric_reserve3\decimal(11,0)};{numeric_reserve4\string\numeric_reserve4\decimal(11,0)};{numeric_reserve5\string\numeric_reserve5\decimal(11,0)};{numeric_reserve6\string\numeric_reserve6\decimal(11,0)};{numeric_reserve7\string\numeric_reserve7\decimal(11,0)};{numeric_reserve8\string\numeric_reserve8\decimal(11,0)};{numeric_reserve9\string\numeric_reserve9\decimal(11,0)};{numeric_reserve10\string\numeric_reserve10\decimal(11,0)};{string_reserve1\string\string_reserve1\varchar};{string_reserve2\string\string_reserve2\varchar};{string_reserve3\string\string_reserve3\varchar};{string_reserve4\string\string_reserve4\varchar};{string_reserve5\string\string_reserve5\varchar};{string_reserve6\string\string_reserve6\varchar};{string_reserve7\string\string_reserve7\varchar};{string_reserve8\string\string_reserve8\varchar};{string_reserve9\string\string_reserve9\varchar};{string_reserve10\string\string_reserve10\varchar}
PRIMARY_KEY=[shop_cd,register_num,business_date,receipt_num,chit_num,system_datetime,open_count,is_void,check_kind,return_kind,sub_check_kind,sales_group_cd,operator_cd,operator_name,sales_man_cd,sales_man_name,net_sales_amount_of_outside_tax,net_sales_amount_of_inside_tax,net_sales_amount_of_tax_free,net_sales_outside_tax,net_sales_inside_tax,net_sales_quantity,outside_sales_amount_of_outside_tax,outside_sales_amount_of_inside_tax,outside_sales_amount_of_tax_free,outside_sales_outside_tax,outside_sales_inside_tax,outside_sales_quantity,total_amount,discount_amount,discount_tax_inclusive,is_revenue_stamp,order_line_count,pay_line_count,is_total_display,is_reduced_tax_rate_trade,is_tax_free,customers_num,employee_meal,service_charge_amount_outside_tax,service_charge_amount_inside_tax,service_charge_tax_exclusive,service_charge_tax_inclusive,service_charge_amount1,service_charge_amount2,service_charge_minus_amount,service_charge_minus_tax_inclusive,service_charge_target,service_charge_button,service_charge1_button,service_charge2_button,eat_in_amount,takeout_amount]
[IF-POS-SL-004]
IF_ID=IF-POS-SL-004
SCHEMA_NAME=dwh_raw
TABLE_NAME=sales_info_alignment_detail
IF_GROUP=SL-IN-001
JOBNET_ID=DWH-JN-POS-SL-002
MAPPING_INFO={shop_cd\string\shop_cd\varchar};{register_num\string\register_num\decimal(11,0)};{business_date\string\business_date\varchar};{receipt_num\string\receipt_num\decimal(11,0)};{line_num\string\line_num\decimal(11,0)};{line_kind\string\line_kind\decimal(4,0)};{is_in_store_marking\string\is_in_store_marking\decimal(4,0)};{brand_cd\string\brand_cd\varchar};{dept_cd\string\dept_cd\varchar};{class_cd\string\class_cd\varchar};{sub_class_cd\string\sub_class_cd\varchar};{item_cd\string\item_cd\varchar};{item_name\string\item_name\varchar};{dept_group_cd\string\dept_group_cd\varchar};{parent_item_cd\string\parent_item_cd\varchar};{jan_cd\string\jan_cd\varchar};{item_num\string\item_num\varchar};{color_cd\string\color_cd\varchar};{size_cd\string\size_cd\varchar};{year_cd\string\year_cd\varchar};{season_cd\string\season_cd\varchar};{attribute_cd1\string\attribute_cd1\varchar};{attribute_cd2\string\attribute_cd2\varchar};{attribute_cd3\string\attribute_cd3\varchar};{attribute_cd4\string\attribute_cd4\varchar};{attribute_cd5\string\attribute_cd5\varchar};{scan_bar_cd1\string\scan_bar_cd1\varchar};{scan_bar_cd2\string\scan_bar_cd2\varchar};{discount_cd\string\discount_cd\varchar};{bmplan_cd\string\bmplan_cd\varchar};{coupon_num\string\coupon_num\varchar};{is_include_sales\string\is_include_sales\decimal(4,0)};{item_kind\string\item_kind\decimal(4,0)};{tax_kind\string\tax_kind\decimal(4,0)};{tax_rate\string\tax_rate\decimal(4,1)};{is_reduced_tax_rate\string\is_reduced_tax_rate\decimal(4,0)};{is_proper_item\string\is_proper_item\decimal(4,0)};{is_change_price\string\is_change_price\decimal(4,0)};{master_unit_price\string\master_unit_price\decimal(11,0)};{body_price\string\body_price\decimal(11,0)};{fixed_price\string\fixed_price\decimal(11,0)};{unit_price\string\unit_price\decimal(11,0)};{quantity\string\quantity\decimal(11,0)};{amount\string\amount\decimal(11,0)};{tax_inclusive\string\tax_inclusive\decimal(11,0)};{tax_outside\string\tax_outside\decimal(11,0)};{tax\string\tax\decimal(11,0)};{disc_rate\string\disc_rate\decimal(4,0)};{line_minus_amount\string\line_minus_amount\decimal(11,0)};{line_minus_tax_inclusive\string\line_minus_tax_inclusive\decimal(11,0)};{bmset_minus_amount\string\bmset_minus_amount\decimal(11,0)};{bmset_minus_tax_inclusive\string\bmset_minus_tax_inclusive\decimal(11,0)};{point_minus_amount\string\point_minus_amount\decimal(11,0)};{point_minus_tax_inclusive\string\point_minus_tax_inclusive\decimal(11,0)};{coupon_minus_amount\string\coupon_minus_amount\decimal(11,0)};{coupon_minus_tax_inclusive\string\coupon_minus_tax_inclusive\decimal(11,0)};{sub_total_minus_amount\string\sub_total_minus_amount\decimal(11,0)};{sub_total_minus_tax_inclusive\string\sub_total_minus_tax_inclusive\decimal(11,0)};{before_disc_tax_inclusive\string\before_disc_tax_inclusive\decimal(11,0)};{sales_man_cd\string\sales_man_cd\varchar};{stock_kind\string\stock_kind\decimal(4,0)};{is_inventory_counted\string\is_inventory_counted\decimal(4,0)};{is_promotion_ticket\string\is_promotion_ticket\decimal(4,0)};{promotion_ticket_bar_cd\string\promotion_ticket_bar_cd\varchar};{is_promotion_ticket_allow_combination\string\is_promotion_ticket_allow_combination\decimal(4,0)};{oes_item_flag\string\oes_item_flag\decimal(4,0)};{oes_slip_no\string\oes_slip_no\varchar};{oes_slip_sub_no\string\oes_slip_sub_no\varchar};{grand_classification\string\grand_classification\decimal(4,0)};{menu_classification\string\menu_classification\varchar};{oes_line_num\string\oes_line_num\decimal(11,0)};{oes_quantity\string\oes_quantity\decimal(11,0)};{oes_minus\string\oes_minus\decimal(11,0)};{pos_minus\string\pos_minus\decimal(11,0)};{takeout_flag\string\takeout_flag\decimal(4,0)};{service_charge_minus_amount\string\service_charge_minus_amount\decimal(11,0)};{service_charge_minus_tax_inclusive\string\service_charge_minus_tax_inclusive\decimal(11,0)};{service_charge_flag\string\service_charge_flag\decimal(4,0)};{service_charge1_flag\string\service_charge1_flag\decimal(4,0)};{service_charge2_flag\string\service_charge2_flag\decimal(4,0)};{service_charge1_manually_flag\string\service_charge1_manually_flag\decimal(4,0)};{service_charge2_manually_flag\string\service_charge2_manually_flag\decimal(4,0)};{oes_service_charge1\string\oes_service_charge1\decimal(4,0)};{oes_service_charge2\string\oes_service_charge2\decimal(4,0)};{cooking_directions_time\string\cooking_directions_time\decimal(11,0)};{cooking_complete_time\string\cooking_complete_time\decimal(11,0)};{offer_complete_time\string\offer_complete_time\decimal(11,0)};{acceptance_time\string\acceptance_time\varchar};{menu_cook_cmp_time\string\menu_cook_cmp_time\varchar};{menu_offer_cmp_time\string\menu_offer_cmp_time\varchar};{disc_amount_limit\string\disc_amount_limit\decimal(11,0)};{is_follow_disc\string\is_follow_disc\decimal(4,0)};{is_allow_credit\string\is_allow_credit\decimal(4,0)};{is_employee_acnt_recv\string\is_employee_acnt_recv\decimal(4,0)};{employee_cd\string\employee_cd\varchar};{sub_menu_kind\string\sub_menu_kind\varchar};{grand_menu_code\string\grand_menu_code\varchar};{grand_menu_index\string\grand_menu_index\decimal(11,0)};{select_kind\string\select_kind\varchar};{is_quantity_count\string\is_quantity_count\decimal(4,0)};{is_order_entry\string\is_order_entry\decimal(4,0)};{modifier_kind\string\modifier_kind\decimal(4,0)};{return_target_line_num\string\return_target_line_num\decimal(11,0)};{numeric_reserve1\string\numeric_reserve1\decimal(11,0)};{numeric_reserve2\string\numeric_reserve2\decimal(11,0)};{numeric_reserve3\string\numeric_reserve3\decimal(11,0)};{numeric_reserve4\string\numeric_reserve4\decimal(11,0)};{numeric_reserve5\string\numeric_reserve5\decimal(11,0)};{numeric_reserve6\string\numeric_reserve6\decimal(11,0)};{numeric_reserve7\string\numeric_reserve7\decimal(11,0)};{numeric_reserve8\string\numeric_reserve8\decimal(11,0)};{numeric_reserve9\string\numeric_reserve9\decimal(11,0)};{numeric_reserve10\string\numeric_reserve10\decimal(11,0)};{string_reserve1\string\string_reserve1\varchar};{string_reserve2\string\string_reserve2\varchar};{string_reserve3\string\string_reserve3\varchar};{string_reserve4\string\string_reserve4\varchar};{string_reserve5\string\string_reserve5\varchar};{string_reserve6\string\string_reserve6\varchar};{string_reserve7\string\string_reserve7\varchar};{string_reserve8\string\string_reserve8\varchar};{string_reserve9\string\string_reserve9\varchar};{string_reserve10\string\string_reserve10\varchar}
PRIMARY_KEY=[shop_cd,register_num,business_date,receipt_num,line_num,line_kind,amount,oes_item_flag,grand_classification,service_charge_flag,service_charge1_flag,service_charge2_flag,service_charge1_manually_flag,service_charge2_manually_flag,oes_service_charge1,oes_service_charge2,disc_amount_limit]
[IF-OMS-AC-001]
IF_ID=IF-OMS-AC-001
SCHEMA_NAME=dwh_raw
TABLE_NAME=accounting_data_mail_order
IF_GROUP=0
JOBNET_ID=DWH-JN-OMS-AC-001
MAPPING_INFO={input_no\string\input_no\varchar};{input_sys_type\string\input_sys_type\varchar};{corp_cd\string\corp_cd\varchar};{vote_employee_cd\string\vote_employee_cd\varchar};{vote_dept_cd\string\vote_dept_cd\varchar};{approval_employee_cd\string\approval_employee_cd\varchar};{approval_date\string\approval_date\varchar};{approval_status_type\string\approval_status_type\varchar};{journal_type\string\journal_type\varchar};{slip_date\string\slip_date\varchar};{slip_no\string\slip_no\varchar};{slip_ope_ban_type\string\slip_ope_ban_type\varchar};{journal_reference_type\string\journal_reference_type\varchar};{input_unit_no\string\input_unit_no\varchar};{xml_db_seq_key\string\xml_db_seq_key\varchar};{transfer_link_key\string\transfer_link_key\varchar};{sys_reserve1\string\sys_reserve1\varchar};{fixes_reason_code\string\fixes_reason_code\varchar};{business_code\string\business_code\varchar};{form_code\string\form_code\varchar};{order_top_char\string\order_top_char\varchar};{order_item\string\order_item\varchar};{order_other\string\order_other\varchar};{wh_code\string\wh_code\varchar};{join_segment_code_1\string\join_segment_code_1\varchar};{join_segment_code_2\string\join_segment_code_2\varchar};{join_segment_code_3\string\join_segment_code_3\varchar};{counter_corp_cd\string\counter_corp_cd\varchar};{counter_corp_join_segment_code_1\string\counter_corp_join_segment_code_1\varchar};{counter_corp_join_segment_code_2\string\counter_corp_join_segment_code_2\varchar};{counter_corp_join_segment_code_3\string\counter_corp_join_segment_code_3\varchar};{slip_user_open_date_1\string\slip_user_open_date_1\varchar};{slip_user_open_code_1\string\slip_user_open_code_1\varchar};{slip_user_open_code_2\string\slip_user_open_code_2\varchar};{sys_reserve2\string\sys_reserve2\varchar};{slip_remarks\string\slip_remarks\varchar};{approval_remarks\string\approval_remarks\varchar};{slip_user_open_area\string\slip_user_open_area\varchar};{slip_user_open_area_2\string\slip_user_open_area_2\varchar};{line_num\string\line_num\varchar};{slip_detail_lending_type\string\slip_detail_lending_type\varchar};{account_code\string\account_code\varchar};{accounting_dept_code\string\accounting_dept_code\varchar};{details_type\string\details_type\varchar};{details_code\string\details_code\varchar};{items_type\string\items_type\varchar};{items_code\string\items_code\varchar};{count_ext_code_1\string\count_ext_code_1\varchar};{count_ext_code_1_type\string\count_ext_code_1_type\varchar};{count_ext_code_2\string\count_ext_code_2\varchar};{count_ext_code_2_type\string\count_ext_code_2_type\varchar};{count_ext_code_3\string\count_ext_code_3\varchar};{count_ext_code_3_type\string\count_ext_code_3_type\varchar};{count_ext_code_4\string\count_ext_code_4\varchar};{count_ext_code_4_type\string\count_ext_code_4_type\varchar};{count_ext_code_5\string\count_ext_code_5\varchar};{count_ext_code_5_type\string\count_ext_code_5_type\varchar};{search_ext_code_1\string\search_ext_code_1\varchar};{search_ext_code_1_type\string\search_ext_code_1_type\varchar};{search_ext_code_2\string\search_ext_code_2\varchar};{search_ext_code_2_type\string\search_ext_code_2_type\varchar};{search_ext_code_3\string\search_ext_code_3\varchar};{search_ext_code_3_type\string\search_ext_code_3_type\varchar};{search_ext_code_4\string\search_ext_code_4\varchar};{search_ext_code_4_type\string\search_ext_code_4_type\varchar};{search_ext_code_5\string\search_ext_code_5\varchar};{search_ext_code_5_type\string\search_ext_code_5_type\varchar};{business_partner_code\string\business_partner_code\varchar};{segment_code\string\segment_code\varchar};{cost_burden_center_code\string\cost_burden_center_code\varchar};{bill_cash_code\string\bill_cash_code\varchar};{business_segment_code\string\business_segment_code\varchar};{region_segment_code\string\region_segment_code\varchar};{customer_segment_code\string\customer_segment_code\varchar};{user_open_segment_code_1\string\user_open_segment_code_1\varchar};{user_open_segment_code_2\string\user_open_segment_code_2\varchar};{match_key\string\match_key\varchar};{tran_currency_code\string\tran_currency_code\varchar};{tran_currency_exchange_rate_type\string\tran_currency_exchange_rate_type\varchar};{tran_currency_rate\string\tran_currency_rate\varchar};{view_currency_exchange_rate_type_1\string\view_currency_exchange_rate_type_1\varchar};{view_currency_rate_1\string\view_currency_rate_1\varchar};{view_currency_exchange_rate_type_2\string\view_currency_exchange_rate_type_2\varchar};{view_currency_rate_2\string\view_currency_rate_2\varchar};{view_currency_exchange_rate_type_3\string\view_currency_exchange_rate_type_3\varchar};{view_currency_rate_3\string\view_currency_rate_3\varchar};{funding_code\string\funding_code\varchar};{tax_type_code\string\tax_type_code\varchar};{sys_reserve3\string\sys_reserve3\varchar};{tax_rate_type\string\tax_rate_type\varchar};{function_currency_amout\string\function_currency_amout\decimal(20,0)};{tran_currency_amout\string\tran_currency_amout\decimal(20,0)};{reference_tax\string\reference_tax\decimal(20,0)};{user_open_num_1\string\user_open_num_1\varchar};{tax_type\string\tax_type\varchar};{history_property_code\string\history_property_code\varchar};{counter_account_code\string\counter_account_code\varchar};{sys_reserve4\string\sys_reserve4\varchar};{sys_reserve5\string\sys_reserve5\varchar};{sys_reserve6\string\sys_reserve6\varchar};{sys_reserve7\string\sys_reserve7\varchar};{sys_reserve8\string\sys_reserve8\varchar};{sys_reserve9\string\sys_reserve9\varchar};{sys_reserve10\string\sys_reserve10\varchar};{sys_reserve11\string\sys_reserve11\varchar};{sys_reserve12\string\sys_reserve12\varchar};{sys_reserve13\string\sys_reserve13\varchar};{sys_reserve14\string\sys_reserve14\varchar};{sys_reserve15\string\sys_reserve15\varchar};{sys_reserve16\string\sys_reserve16\varchar};{sys_reserve17\string\sys_reserve17\varchar};{sys_reserve18\string\sys_reserve18\varchar};{sys_reserve19\string\sys_reserve19\varchar};{sys_reserve20\string\sys_reserve20\varchar};{sys_reserve21\string\sys_reserve21\varchar};{sys_reserve22\string\sys_reserve22\varchar};{sys_reserve23\string\sys_reserve23\varchar};{sys_reserve24\string\sys_reserve24\varchar};{sys_reserve25\string\sys_reserve25\varchar};{sys_reserve26\string\sys_reserve26\varchar};{sys_reserve27\string\sys_reserve27\varchar};{sys_reserve28\string\sys_reserve28\varchar};{quantity\string\quantity\decimal(20,0)};{unit_cd\string\unit_cd\varchar};{quantity_sub\string\quantity_sub\decimal(20,0)};{unit_cd_sub\string\unit_cd_sub\varchar};{function_currency_price\string\function_currency_price\decimal(20,0)};{tran_currency_price\string\tran_currency_price\decimal(20,0)};{ext_num_1\string\ext_num_1\varchar};{ext_num_2\string\ext_num_2\varchar};{ext_num_3\string\ext_num_3\varchar};{user_open_date_1\string\user_open_date_1\varchar};{user_open_code_1\string\user_open_code_1\varchar};{user_open_code_2\string\user_open_code_2\varchar};{user_open_code_3\string\user_open_code_3\varchar};{user_open_code_4\string\user_open_code_4\varchar};{user_open_code_5\string\user_open_code_5\varchar};{user_open_code_6\string\user_open_code_6\varchar};{user_open_code_7\string\user_open_code_7\varchar};{user_open_area_1\string\user_open_area_1\varchar};{sys_reserve29\string\sys_reserve29\varchar};{sys_reserve30\string\sys_reserve30\varchar};{user_open_area_2\string\user_open_area_2\varchar};{user_open_area_3\string\user_open_area_3\varchar};{user_open_code_8\string\user_open_code_8\varchar};{user_open_area_5\string\user_open_area_5\varchar};{user_open_area_6\string\user_open_area_6\varchar};{user_open_area_7\string\user_open_area_7\varchar};{user_open_area_8\string\user_open_area_8\varchar};{sys_reserve31\string\sys_reserve31\varchar};{user_open_date_2\string\user_open_date_2\varchar};{text_description_bill_remarks\string\text_description_bill_remarks\varchar};{detail_user_open_area\string\detail_user_open_area\varchar};{detail_user_open_area_2\string\detail_user_open_area_2\varchar};{individual_application_key\string\individual_application_key\varchar};{recovery_payment_dept_code\string\recovery_payment_dept_code\varchar};{contract_no\string\contract_no\varchar};{invoice_no\string\invoice_no\varchar};{recovery_payment_schedule_date\string\recovery_payment_schedule_date\varchar};{bill_cash_closing_date\string\bill_cash_closing_date\varchar};{upd_sub_sys_type\string\upd_sub_sys_type\varchar};{property_control_number\string\property_control_number\varchar};{bill_no\string\bill_no\varchar};{bill_kind_type\string\bill_kind_type\varchar};{bill_type\string\bill_type\varchar};{transition_type\string\transition_type\varchar};{bill_cash_settlement_date\string\bill_cash_settlement_date\varchar};{bill_split_type_sys_reserve\string\bill_split_type_sys_reserve\varchar};{effort_payment_advice_date\string\effort_payment_advice_date\varchar};{cash_schedule_date\string\cash_schedule_date\varchar};{bill_site\string\bill_site\varchar};{sys_reserve32\string\sys_reserve32\varchar};{sys_reserve33\string\sys_reserve33\varchar};{bank_account_holder\string\bank_account_holder\varchar};{payment_place_counter_bank_code\string\payment_place_counter_bank_code\varchar};{payment_place\string\payment_place\varchar};{bill_effort_company_bank_code\string\bill_effort_company_bank_code\varchar};{bill_discount_fee\string\bill_discount_fee\decimal(13,0)};{telegraph_document_transfer_type\string\telegraph_document_transfer_type\varchar};{fee_burden_type\string\fee_burden_type\varchar};{fb_transfer_process_type\string\fb_transfer_process_type\varchar};{company_bank_account_type\string\company_bank_account_type\varchar};{company_bank_account_no\string\company_bank_account_no\varchar};{counter_bank_account_type\string\counter_bank_account_type\varchar};{counter_bank_account_no\string\counter_bank_account_no\varchar};{sys_reserve34\string\sys_reserve34\varchar};{sys_reserve35\string\sys_reserve35\varchar}
PRIMARY_KEY=[input_no,input_sys_type,corp_cd,vote_employee_cd,vote_dept_cd,approval_employee_cd,approval_date,approval_status_type,journal_type,slip_date,slip_ope_ban_type,journal_reference_type,slip_remarks,line_num,slip_detail_lending_type,account_code,accounting_dept_code,details_code,segment_code,user_open_segment_code_1,function_currency_amout,tax_type,text_description_bill_remarks]
[IF-POS-AC-001]
IF_ID=IF-POS-AC-001
SCHEMA_NAME=dwh_raw
TABLE_NAME=accounting_data_store_sales
IF_GROUP=0
JOBNET_ID=DWH-JN-POS-AC-001
MAPPING_INFO={input_no\string\input_no\varchar};{input_sys_type\string\input_sys_type\varchar};{corp_cd\string\corp_cd\varchar};{vote_employee_cd\string\vote_employee_cd\varchar};{vote_dept_cd\string\vote_dept_cd\varchar};{approval_employee_cd\string\approval_employee_cd\varchar};{approval_date\string\approval_date\varchar};{approval_status_type\string\approval_status_type\decimal(4,0)};{journal_type\string\journal_type\decimal(8,0)};{slip_date\string\slip_date\varchar};{slip_no\string\slip_no\varchar};{slip_ope_ban_type\string\slip_ope_ban_type\decimal(4,0)};{journal_reference_type\string\journal_reference_type\decimal(4,0)};{input_unit_no\string\input_unit_no\varchar};{xml_db_seq_key\string\xml_db_seq_key\varchar};{transfer_link_key\string\transfer_link_key\varchar};{sys_reserve1\string\sys_reserve1\varchar};{fixes_reason_code\string\fixes_reason_code\varchar};{business_code\string\business_code\varchar};{form_code\string\form_code\varchar};{order_top_char\string\order_top_char\varchar};{order_item\string\order_item\varchar};{order_other\string\order_other\varchar};{wh_code\string\wh_code\varchar};{join_segment_code_1\string\join_segment_code_1\varchar};{join_segment_code_2\string\join_segment_code_2\varchar};{join_segment_code_3\string\join_segment_code_3\varchar};{counter_corp_cd\string\counter_corp_cd\varchar};{counter_corp_join_segment_code_1\string\counter_corp_join_segment_code_1\varchar};{counter_corp_join_segment_code_2\string\counter_corp_join_segment_code_2\varchar};{counter_corp_join_segment_code_3\string\counter_corp_join_segment_code_3\varchar};{slip_user_open_date_1\string\slip_user_open_date_1\varchar};{slip_user_open_code_1\string\slip_user_open_code_1\varchar};{slip_user_open_code_2\string\slip_user_open_code_2\varchar};{sys_reserve2\string\sys_reserve2\varchar};{slip_remarks\string\slip_remarks\varchar};{approval_remarks\string\approval_remarks\varchar};{slip_user_open_area\string\slip_user_open_area\varchar};{slip_user_open_area_2\string\slip_user_open_area_2\varchar};{line_num\string\line_num\varchar};{slip_detail_lending_type\string\slip_detail_lending_type\decimal(4,0)};{account_code\string\account_code\varchar};{accounting_dept_code\string\accounting_dept_code\varchar};{details_type\string\details_type\varchar};{details_code\string\details_code\varchar};{items_type\string\items_type\varchar};{items_code\string\items_code\varchar};{count_ext_code_1\string\count_ext_code_1\varchar};{count_ext_code_1_type\string\count_ext_code_1_type\varchar};{count_ext_code_2\string\count_ext_code_2\varchar};{count_ext_code_2_type\string\count_ext_code_2_type\varchar};{count_ext_code_3\string\count_ext_code_3\varchar};{count_ext_code_3_type\string\count_ext_code_3_type\varchar};{count_ext_code_4\string\count_ext_code_4\varchar};{count_ext_code_4_type\string\count_ext_code_4_type\varchar};{count_ext_code_5\string\count_ext_code_5\varchar};{count_ext_code_5_type\string\count_ext_code_5_type\varchar};{search_ext_code_1\string\search_ext_code_1\varchar};{search_ext_code_1_type\string\search_ext_code_1_type\varchar};{search_ext_code_2\string\search_ext_code_2\varchar};{search_ext_code_2_type\string\search_ext_code_2_type\varchar};{search_ext_code_3\string\search_ext_code_3\varchar};{search_ext_code_3_type\string\search_ext_code_3_type\varchar};{search_ext_code_4\string\search_ext_code_4\varchar};{search_ext_code_4_type\string\search_ext_code_4_type\varchar};{search_ext_code_5\string\search_ext_code_5\varchar};{search_ext_code_5_type\string\search_ext_code_5_type\varchar};{business_partner_code\string\business_partner_code\varchar};{segment_code\string\segment_code\varchar};{cost_burden_center_code\string\cost_burden_center_code\varchar};{bill_cash_code\string\bill_cash_code\varchar};{business_segment_code\string\business_segment_code\varchar};{region_segment_code\string\region_segment_code\varchar};{customer_segment_code\string\customer_segment_code\varchar};{user_open_segment_code_1\string\user_open_segment_code_1\varchar};{user_open_segment_code_2\string\user_open_segment_code_2\varchar};{match_key\string\match_key\varchar};{tran_currency_code\string\tran_currency_code\varchar};{tran_currency_exchange_rate_type\string\tran_currency_exchange_rate_type\varchar};{tran_currency_rate\string\tran_currency_rate\varchar};{view_currency_exchange_rate_type_1\string\view_currency_exchange_rate_type_1\varchar};{view_currency_rate_1\string\view_currency_rate_1\varchar};{view_currency_exchange_rate_type_2\string\view_currency_exchange_rate_type_2\varchar};{view_currency_rate_2\string\view_currency_rate_2\varchar};{view_currency_exchange_rate_type_3\string\view_currency_exchange_rate_type_3\varchar};{view_currency_rate_3\string\view_currency_rate_3\varchar};{funding_code\string\funding_code\varchar};{tax_type_code\string\tax_type_code\varchar};{sys_reserve3\string\sys_reserve3\varchar};{tax_rate_type\string\tax_rate_type\decimal(4,1)};{function_currency_amout\string\function_currency_amout\decimal(11,0)};{tran_currency_amout\string\tran_currency_amout\decimal(20,0)};{reference_tax\string\reference_tax\decimal(20,0)};{user_open_num_1\string\user_open_num_1\varchar};{tax_type\string\tax_type\decimal(11,0)};{history_property_code\string\history_property_code\varchar};{counter_account_code\string\counter_account_code\varchar};{sys_reserve4\string\sys_reserve4\varchar};{sys_reserve5\string\sys_reserve5\varchar};{sys_reserve6\string\sys_reserve6\varchar};{sys_reserve7\string\sys_reserve7\varchar};{sys_reserve8\string\sys_reserve8\varchar};{sys_reserve9\string\sys_reserve9\varchar};{sys_reserve10\string\sys_reserve10\varchar};{sys_reserve11\string\sys_reserve11\varchar};{sys_reserve12\string\sys_reserve12\varchar};{sys_reserve13\string\sys_reserve13\varchar};{sys_reserve14\string\sys_reserve14\varchar};{sys_reserve15\string\sys_reserve15\varchar};{sys_reserve16\string\sys_reserve16\varchar};{sys_reserve17\string\sys_reserve17\varchar};{sys_reserve18\string\sys_reserve18\varchar};{sys_reserve19\string\sys_reserve19\varchar};{sys_reserve20\string\sys_reserve20\varchar};{sys_reserve21\string\sys_reserve21\varchar};{sys_reserve22\string\sys_reserve22\varchar};{sys_reserve23\string\sys_reserve23\varchar};{sys_reserve24\string\sys_reserve24\varchar};{sys_reserve25\string\sys_reserve25\varchar};{sys_reserve26\string\sys_reserve26\varchar};{sys_reserve27\string\sys_reserve27\varchar};{sys_reserve28\string\sys_reserve28\varchar};{quantity\string\quantity\decimal(11,0)};{unit_cd\string\unit_cd\varchar};{quantity_sub\string\quantity_sub\decimal(20,0)};{unit_cd_sub\string\unit_cd_sub\varchar};{function_currency_price\string\function_currency_price\decimal(20,0)};{tran_currency_price\string\tran_currency_price\decimal(20,0)};{ext_num_1\string\ext_num_1\varchar};{ext_num_2\string\ext_num_2\varchar};{ext_num_3\string\ext_num_3\varchar};{user_open_date_1\string\user_open_date_1\varchar};{user_open_code_1\string\user_open_code_1\varchar};{user_open_code_2\string\user_open_code_2\varchar};{user_open_code_3\string\user_open_code_3\varchar};{user_open_code_4\string\user_open_code_4\varchar};{user_open_code_5\string\user_open_code_5\varchar};{user_open_code_6\string\user_open_code_6\varchar};{user_open_code_7\string\user_open_code_7\varchar};{user_open_area_1\string\user_open_area_1\varchar};{sys_reserve29\string\sys_reserve29\varchar};{sys_reserve30\string\sys_reserve30\varchar};{user_open_area_2\string\user_open_area_2\varchar};{user_open_area_3\string\user_open_area_3\varchar};{user_open_code_8\string\user_open_code_8\varchar};{user_open_area_5\string\user_open_area_5\varchar};{user_open_area_6\string\user_open_area_6\varchar};{user_open_area_7\string\user_open_area_7\varchar};{user_open_area_8\string\user_open_area_8\varchar};{sys_reserve31\string\sys_reserve31\varchar};{user_open_date_2\string\user_open_date_2\varchar};{text_description_bill_remarks\string\text_description_bill_remarks\varchar};{detail_user_open_area\string\detail_user_open_area\varchar};{detail_user_open_area_2\string\detail_user_open_area_2\varchar};{individual_application_key\string\individual_application_key\varchar};{recovery_payment_dept_code\string\recovery_payment_dept_code\varchar};{contract_no\string\contract_no\varchar};{invoice_no\string\invoice_no\varchar};{recovery_payment_schedule_date\string\recovery_payment_schedule_date\varchar};{bill_cash_closing_date\string\bill_cash_closing_date\varchar};{upd_sub_sys_type\string\upd_sub_sys_type\varchar};{property_control_number\string\property_control_number\varchar};{bill_no\string\bill_no\varchar};{bill_kind_type\string\bill_kind_type\varchar};{bill_type\string\bill_type\varchar};{transition_type\string\transition_type\varchar};{bill_cash_settlement_date\string\bill_cash_settlement_date\varchar};{bill_split_type_sys_reserve\string\bill_split_type_sys_reserve\varchar};{effort_payment_advice_date\string\effort_payment_advice_date\varchar};{cash_schedule_date\string\cash_schedule_date\varchar};{bill_site\string\bill_site\varchar};{sys_reserve32\string\sys_reserve32\varchar};{sys_reserve33\string\sys_reserve33\varchar};{bank_account_holder\string\bank_account_holder\varchar};{payment_place_counter_bank_code\string\payment_place_counter_bank_code\varchar};{payment_place\string\payment_place\varchar};{bill_effort_company_bank_code\string\bill_effort_company_bank_code\varchar};{bill_discount_fee\string\bill_discount_fee\decimal(13,0)};{telegraph_document_transfer_type\string\telegraph_document_transfer_type\varchar};{fee_burden_type\string\fee_burden_type\varchar};{fb_transfer_process_type\string\fb_transfer_process_type\varchar};{company_bank_account_type\string\company_bank_account_type\varchar};{company_bank_account_no\string\company_bank_account_no\varchar};{counter_bank_account_type\string\counter_bank_account_type\varchar};{counter_bank_account_no\string\counter_bank_account_no\varchar};{sys_reserve34\string\sys_reserve34\varchar};{sys_reserve35\string\sys_reserve35\varchar}
PRIMARY_KEY=[input_no,input_sys_type,corp_cd,vote_employee_cd,vote_dept_cd,approval_employee_cd,approval_date,approval_status_type,journal_type,slip_date,slip_ope_ban_type,journal_reference_type,slip_remarks,line_num,slip_detail_lending_type,accounting_dept_code,user_open_segment_code_1,function_currency_amout,tax_type,text_description_bill_remarks]
[IF-OMS-SP-004]
IF_ID=IF-OMS-SP-004
SCHEMA_NAME=dwh_raw
TABLE_NAME=out_indicate_header
IF_GROUP=SP-IN-002
JOBNET_ID=DWH-JN-OMS-SP-002
MAPPING_INFO={accept_no\string\accept_no\varchar};{cust_no\string\cust_no\varchar};{record_no\string\record_no\varchar};{cust_name\string\cust_name\varchar};{post_no\string\post_no\varchar};{addr1\string\addr1\varchar};{addr2\string\addr2\varchar};{addr3\string\addr3\varchar};{tel_no\string\tel_no\varchar};{prefecture_code\string\prefecture_code\decimal(2,0)};{cust_flg\string\cust_flg\decimal(1,0)};{order_date\string\order_date\varchar};{pay_kb\string\pay_kb\varchar};{total_price\string\total_price\decimal(10,0)};{delive_cust_name\string\delive_cust_name\varchar};{delive_post_no\string\delive_post_no\varchar};{delive_addr1\string\delive_addr1\varchar};{delive_addr2\string\delive_addr2\varchar};{delive_addr3\string\delive_addr3\varchar};{delive_tel_no\string\delive_tel_no\varchar};{gift_flg\string\gift_flg\varchar};{kibou_ymd\string\kibou_ymd\varchar};{night_flg\string\night_flg\varchar};{cosme_price\string\cosme_price\decimal(10,0)};{health_price\string\health_price\decimal(10,0)};{inner_price\string\inner_price\decimal(10,0)};{update_date\string\update_date\varchar};{chit_print_date\string\chit_print_date\varchar};{yamato_bar_code\string\yamato_bar_code\varchar};{gyosha_flg\string\gyosha_flg\varchar};{status_flg\string\status_flg\varchar};{slip_ono\string\slip_ono\varchar};{order_no\string\order_no\varchar};{clinic_name\string\clinic_name\varchar};{shipment_date\string\shipment_date\varchar};{shipment_plan_date\string\shipment_plan_date\varchar};{pack_cnt\string\pack_cnt\decimal(10,0)};{store_code\string\store_code\varchar};{period_flg\string\period_flg\varchar};{delivery_box_gb\string\delivery_box_gb\varchar};{air_delivery_yn\string\air_delivery_yn\varchar};{tax_amt\string\tax_amt\decimal(10,0)};{conveni_yn\string\conveni_yn\varchar};{pudo_yn\string\pudo_yn\varchar};{inplan_yn\string\inplan_yn\varchar};{over_stock_yn\string\over_stock_yn\varchar};{reserve_order_yn\string\reserve_order_yn\varchar};{gift_rapping_yn\string\gift_rapping_yn\varchar};{kanshi_yn\string\kanshi_yn\varchar};{fusoku_yn\string\fusoku_yn\varchar};{airplane_yn\string\airplane_yn\varchar};{satofuru_yn\string\satofuru_yn\varchar};{tokusha_yn\string\tokusha_yn\varchar};{rakugaki_yn\string\rakugaki_yn\varchar};{multi_sample_yn\string\multi_sample_yn\varchar};{slip_size_code\string\slip_size_code\varchar};{wh_code\string\wh_code\varchar};{agent_cd\string\agent_cd\varchar};{import_yn\string\import_yn\varchar};{import_date\string\import_date\varchar}
PRIMARY_KEY=[accept_no]
[IF-OMS-SP-005]
IF_ID=IF-OMS-SP-005
SCHEMA_NAME=dwh_raw
TABLE_NAME=out_indicate_detail
IF_GROUP=SP-IN-002
JOBNET_ID=DWH-JN-OMS-SP-002
MAPPING_INFO={accept_no\string\accept_no\varchar};{seq\string\seq\decimal(5,0)};{prod_no\string\prod_no\decimal(10,0)};{qty\string\qty\decimal(7,0)};{chit_print_date\string\chit_print_date\varchar};{yamato_bar_code\string\yamato_bar_code\varchar};{gyosha_flg\string\gyosha_flg\varchar};{invoice_branch_number\string\invoice_branch_number\varchar};{bumon_kbn\string\bumon_kbn\varchar};{prod_price\string\prod_price\decimal(6,0)};{order_type\string\order_type\varchar}
PRIMARY_KEY=[accept_no,seq,prod_no]
[IF-WMS-SP-001]
IF_ID=IF-WMS-SP-001
SCHEMA_NAME=dwh_raw
TABLE_NAME=out_achievements_store_sales
IF_GROUP=SP-IN-003
JOBNET_ID=DWH-JN-WMS-SP-001
MAPPING_INFO={accept_no\string\accept_no\varchar};{close_date\string\close_date\varchar};{wh_code\string\wh_code\varchar};{goods_code\string\goods_code\varchar};{out_qty\string\out_qty\decimal(9,0)};{logimane_slip_no\string\logimane_slip_no\varchar}
PRIMARY_KEY=[accept_no,close_date,wh_code,goods_code,out_qty,logimane_slip_no]
[IF-WMS-SP-002]
IF_ID=IF-WMS-SP-002
SCHEMA_NAME=dwh_raw
TABLE_NAME=out_achievements_mail_order
IF_GROUP=SP-IN-003
JOBNET_ID=DWH-JN-WMS-SP-001
MAPPING_INFO={accept_no\string\accept_no\varchar};{close_date\string\close_date\varchar};{wh_code\string\wh_code\varchar};{goods_code\string\goods_code\varchar};{out_qty\string\out_qty\decimal(9,0)};{logimane_slip_no\string\logimane_slip_no\varchar}
PRIMARY_KEY=[accept_no,close_date,wh_code,goods_code,out_qty,logimane_slip_no]
[IF-POS-SH-001]
IF_ID=IF-POS-SH-001
SCHEMA_NAME=dwh_raw
TABLE_NAME=shop_mst_raw
IF_GROUP=0
JOBNET_ID=DWH-JN-POS-SH-001
MAPPING_INFO={shop_cd\string\shop_cd\varchar};{group_cd\string\group_cd\varchar};{area_cd\string\area_cd\varchar};{district_cd\string\district_cd\varchar};{shop_kind\string\shop_kind\varchar};{shop_name_full\string\shop_name_full\varchar};{shop_name_half\string\shop_name_half\varchar};{shop_name_short\string\shop_name_short\varchar};{shop_name_english\string\shop_name_english\varchar};{zip_cd\string\zip_cd\varchar};{prefecture_cd\string\prefecture_cd\varchar};{address1\string\address1\varchar};{address2\string\address2\varchar};{address3\string\address3\varchar};{address4\string\address4\varchar};{tel_num\string\tel_num\varchar};{fax_num\string\fax_num\varchar};{tax_inclusive_round_kind\string\tax_inclusive_round_kind\decimal(4,0)};{tax_exclusive_round_kind\string\tax_exclusive_round_kind\decimal(4,0)};{condition_kind\string\condition_kind\varchar};{condition_detail_kind\string\condition_detail_kind\varchar};{channel_kind\string\channel_kind\varchar};{is_stock_control\string\is_stock_control\decimal(4,0)};{begin_date\string\begin_date\varchar};{end_date\string\end_date\varchar};{shop_floor_space\string\shop_floor_space\decimal(6,2)};{is_abandon_price_change\string\is_abandon_price_change\decimal(4,0)};{is_abandon_stock_transfer\string\is_abandon_stock_transfer\decimal(4,0)};{form_management_kind\string\form_management_kind\varchar};{is_shop_terminal\string\is_shop_terminal\decimal(4,0)};{allow_transfer_group_cd\string\allow_transfer_group_cd\varchar};{main_brand_cd\string\main_brand_cd\varchar};{disc_round_position\string\disc_round_position\decimal(4,0)};{disc_round_kind\string\disc_round_kind\decimal(4,0)};{is_emp_salse_place\string\is_emp_salse_place\decimal(4,0)};{is_move_operation\string\is_move_operation\decimal(4,0)};{is_sales_register\string\is_sales_register\decimal(4,0)};{is_stock_display\string\is_stock_display\decimal(4,0)};{brock_cd\string\brock_cd\varchar};{pricechange_disc_round_position\string\pricechange_disc_round_position\decimal(4,0)};{pricechange_disc_round_kind\string\pricechange_disc_round_kind\decimal(4,0)};{numeric_reserve1\string\numeric_reserve1\decimal(11,0)};{numeric_reserve2\string\numeric_reserve2\decimal(11,0)};{numeric_reserve3\string\numeric_reserve3\decimal(11,0)};{numeric_reserve4\string\numeric_reserve4\decimal(11,0)};{numeric_reserve5\string\numeric_reserve5\decimal(11,0)};{string_reserve1\string\string_reserve1\varchar};{string_reserve2\string\string_reserve2\varchar};{string_reserve3\string\string_reserve3\varchar};{string_reserve4\string\string_reserve4\varchar};{string_reserve5\string\string_reserve5\varchar};{is_deleted\string\is_deleted\decimal(4,0)};{spare_numeric_reserve1\string\spare_numeric_reserve1\decimal(11,0)};{spare_numeric_reserve2\string\spare_numeric_reserve2\decimal(11,0)};{spare_numeric_reserve3\string\spare_numeric_reserve3\decimal(11,0)};{apare_numeric_reserve4\string\apare_numeric_reserve4\decimal(11,0)};{spare_numeric_reserve5\string\spare_numeric_reserve5\decimal(11,0)};{spare_string_reserve1\string\spare_string_reserve1\varchar};{spare_string_reserve2\string\spare_string_reserve2\varchar};{spare_string_reserve3\string\spare_string_reserve3\varchar};{spare_string_reserve4\string\spare_string_reserve4\varchar};{spare_string_reserve5\string\spare_string_reserve5\varchar};{spare_string_reserve6\string\spare_string_reserve6\varchar};{spare_string_reserve7\string\spare_string_reserve7\varchar}
PRIMARY_KEY=[shop_cd,shop_kind,shop_name_full,condition_kind,condition_detail_kind,is_stock_control,is_abandon_price_change,is_abandon_stock_transfer,is_shop_terminal,main_brand_cd,disc_round_position,disc_round_kind,is_emp_salse_place,is_move_operation,is_sales_register,is_stock_display]
[IF-ADD-IS-001]
IF_ID=IF-ADD-IS-001
SCHEMA_NAME=dwh_raw
TABLE_NAME=arrival_schedule_other
IF_GROUP=0
JOBNET_ID=DWH-JN-ADD-IS-001
MAPPING_INFO={stock_arrival_date\string\stock_arrival_date\varchar};{warehouse_cd\string\warehouse_cd\varchar};{po_no\string\po_no\varchar};{warehouse_management_no\string\warehouse_management_no\varchar};{order_count\string\order_count\decimal(7,0)};{agent_cd\string\agent_cd\varchar};{order_registrant_name\string\order_registrant_name\varchar};{comment_code\string\comment_code\varchar}
PRIMARY_KEY=[stock_arrival_date,warehouse_cd,po_no,warehouse_management_no,order_count,agent_cd]
[IF-WMS-IS-001]
IF_ID=IF-WMS-IS-001
SCHEMA_NAME=dwh_raw
TABLE_NAME=arrival_achievements
IF_GROUP=0
JOBNET_ID=DWH-JN-WMS-IS-001
MAPPING_INFO={warehouse_cd\string\warehouse_cd\varchar};{receiving_stock_date\string\receiving_stock_date\varchar};{receiving_stock_former_cd\string\receiving_stock_former_cd\varchar};{receiving_stock_slip_no\string\receiving_stock_slip_no\varchar};{receiving_stock_slip_row_no\string\receiving_stock_slip_row_no\decimal(4,0)};{po_no\string\po_no\varchar};{product_cd\string\product_cd\varchar};{receiving_stock_cnt\string\receiving_stock_cnt\decimal(13,3)}
PRIMARY_KEY=[warehouse_cd,receiving_stock_date,receiving_stock_slip_no,receiving_stock_slip_row_no,po_no,product_cd,receiving_stock_cnt]
[IF-APP-IS-001]
IF_ID=IF-APP-IS-001
SCHEMA_NAME=dwh_raw
TABLE_NAME=arrival_schedule_apparel
IF_GROUP=0
JOBNET_ID=DWH-JN-APP-IS-001
MAPPING_INFO={stock_arrival_date\string\stock_arrival_date\varchar};{warehouse_cd\string\warehouse_cd\varchar};{po_no\string\po_no\varchar};{warehouse_management_no\string\warehouse_management_no\varchar};{order_count\string\order_count\decimal(7,0)};{agent_cd\string\agent_cd\varchar};{order_registrant_name\string\order_registrant_name\varchar};{comment_code\string\comment_code\varchar}
PRIMARY_KEY=[stock_arrival_date,warehouse_cd,po_no,warehouse_management_no,order_count,agent_cd]
[IF-CRM-FA-001]
IF_ID=IF-CRM-FA-001
SCHEMA_NAME=dwh_raw
TABLE_NAME=favorite_product
IF_GROUP=0
JOBNET_ID=DWH-JN-CRM-FA-001
MAPPING_INFO={id\string\id\varchar};{ownerid\string\ownerid\varchar};{isdeleted\string\isdeleted\varchar};{name\string\name\varchar};{createddate\string\createddate\varchar};{createdbyid\string\createdbyid\varchar};{lastmodifieddate\string\lastmodifieddate\varchar};{lastmodifiedbyid\string\lastmodifiedbyid\varchar};{systemmodstamp\string\systemmodstamp\varchar};{accountid__c\string\accountid__c\varchar};{productid__c\string\productid__c\varchar};{productcode__c\string\productcode__c\varchar};{productname__c\string\productname__c\varchar};{isdeleted__c\string\isdeleted__c\varchar}
PRIMARY_KEY=[id,ownerid,createddate,createdbyid,lastmodifieddate,lastmodifiedbyid,systemmodstamp,accountid__c,productid__c]
[IF-WMS-ST-001]
IF_ID=IF-WMS-ST-001
SCHEMA_NAME=dwh_raw
TABLE_NAME=stock_all
IF_GROUP=0
JOBNET_ID=DWH-JN-WMS-ST-001
MAPPING_INFO={center_code\string\center_code\varchar};{stock_kind\string\stock_kind\varchar};{sh_control_number\string\sh_control_number\varchar};{stock_quantity\string\stock_quantity\decimal(7,0)};{allocated_quantity\string\allocated_quantity\decimal(7,0)}
PRIMARY_KEY=[center_code,stock_kind,sh_control_number,stock_quantity,allocated_quantity]
[IF-WMS-ST-002]
IF_ID=IF-WMS-ST-002
SCHEMA_NAME=dwh_raw
TABLE_NAME=stock_change
IF_GROUP=0
JOBNET_ID=DWH-JN-WMS-ST-002
MAPPING_INFO={wms_stock_io_type\string\wms_stock_io_type\varchar};{slip_date\string\slip_date\varchar};{slip_no\string\slip_no\varchar};{sh_control_number\string\sh_control_number\varchar};{quantity\string\quantity\decimal(13,3)};{stock_io_type\string\stock_io_type\decimal(1,0)};{center_code\string\center_code\varchar};{stock_type\string\stock_type\decimal(1,0)};{swh_cmns_target_shop\string\swh_cmns_target_shop\varchar};{center_code_partner\string\center_code_partner\varchar};{stock_type_partner\string\stock_type_partner\varchar};{swh_counter\string\swh_counter\varchar};{receiving_shipping\string\receiving_shipping\varchar};{receiving_shipping_name\string\receiving_shipping_name\varchar};{reason_code_items\string\reason_code_items\varchar};{reason_code_name\string\reason_code_name\varchar};{reason_code_item\string\reason_code_item\varchar}
PRIMARY_KEY=[wms_stock_io_type,slip_date,slip_no,sh_control_number,quantity,stock_io_type,center_code,stock_type]
[IF-CMN-ST-002]
IF_ID=IF-CMN-ST-002
SCHEMA_NAME=dwh_raw
TABLE_NAME=secure_move
IF_GROUP=0
JOBNET_ID=DWH-JN-CMN-ST-002
MAPPING_INFO={corp_cd\string\corp_cd\varchar};{stock_io_type\string\stock_io_type\varchar};{move_date\string\move_date\varchar};{center_code\string\center_code\varchar};{stock_group_code\string\stock_group_code\varchar};{shop_code_swh\string\shop_code_swh\varchar};{sh_control_number\string\sh_control_number\varchar};{move_quantity\string\move_quantity\decimal(7,0)};{move_center_code\string\move_center_code\varchar};{move_stock_group_code\string\move_stock_group_code\varchar};{move_shop_code_swh\string\move_shop_code_swh\varchar};{upd_user_id\string\upd_user_id\varchar};{data_date\string\data_date\varchar}
PRIMARY_KEY=[]
[IF-CDP-ME-001]
IF_ID=IF-CDP-ME-001
SCHEMA_NAME=dwh_raw
TABLE_NAME=member
JOBNET_ID=DWH-JN-CDP-ME-001
UNLOAD_FIELDS=id,isdeleted,masterrecordid,name,lastname,firstname,salutation,type,recordtypeid,parentid,personmailingstreet,personmailingcity,personmailingstate,personmailingstatecode,personmailingpostalcode,personmailingcountry,personmailinglatitude,personmailinglongitude,personmailinggeocodeaccuracy,personmailingaddress,shippingstreet,shippingcity,shippingstate,shippingpostalcode,shippingcountry,shippinglatitude,shippinglongitude,shippinggeocodeaccuracy,shippingaddress,phone,fax,accountnumber,website,photourl,sic,industry,annualrevenue,numberofemployees,ownership,tickersymbol,description,rating,site,ownerid,createddate,createdbyid,lastmodifieddate,lastmodifiedbyid,systemmodstamp,lastactivitydate,lastvieweddate,lastreferenceddate,ispersonaccount,billingstreet,billingcity,billingstate,billingpostalcode,billingcountry,billinglatitude,billinglongitude,billinggeocodeaccuracy,billingaddress,personotherstreet,personothercity,personotherstate,personotherpostalcode,personothercountry,personotherlatitude,personotherlongitude,personothergeocodeaccuracy,personotheraddress,personmobilephone,personotherphone,personassistantphone,personemail,persontitle,persondepartment,personassistantname,personleadsource,personbirthdate,personhasoptedoutofemail,personhasoptedoutoffax,persondonotcall,personlastcurequestdate,personlastcuupdatedate,personemailbouncedreason,personemailbounceddate,personindividualid,personpronouns,persongenderidentity,jigsaw,jigsawcompanyid,accountsource,sicdesc,gender__c,number__c,preferredshipmentservice__c,accountcloseddate__c,accountclosedreason__c,shopcardbarcode__c,personmailingaddress__c,isemployee__c,isoptedinemalmagazine__c,emailmagazineunsubscribeddate__c,emailmagazinesubscribeddate__c,beautycatalogsendtype__c,healthcatalogsendtype__c,apparelcatalogsendtype__c,medicinecatalogsendtype__c,petcatalogsendtype__c,faxpurchaseordersendtype__c,lastnamekana__c,firstnamekana__c,rank__c,source__c,status__c,memo__c,memoforstore__c,isdhccreditcardowner__c,age__c,isoptedindm__c,isoptedincatalog__c,isunmailablepostway__c,isunmailablepost__c,isstoporder__c,isordermonitoring__c,isrequiredcaution__c,guestorderonly__c,customernumber__c,isoptedinsurvey__c,margedaccountid__c,rankexpirydate__c,preferredcontactway__c,lineminiappuserid__c,namekana__c,birthdate__c,emailmagazineoptedouturl__c,personemail__c,optinstoreemailmagazinestatus__c,storeemailmagazineemail__c,storeemailmagazineoptedoutkey__c,storeemailmagazineoptedouturl__c,tonariwaid__c,storeemailmagazinestorecode__c,unmailablereason__c
[IF-CDP-ME-002]
IF_ID=IF-CDP-ME-002
SCHEMA_NAME=dwh_raw
TABLE_NAME=card_info
JOBNET_ID=DWH-JN-CDP-ME-002
UNLOAD_FIELDS=customer_code,credit_card_kanri_no,credit_card_kanri_detail_no,credit_card_no,card_expire_year,card_expire_month,card_holder,card_brand,default_use_flag,change_datetime,change_channel_kbn,card_keep_ng_flg,change_reason_kbn,change_reason,change_user_code,dhc_card_flg,crm_card_id,crm_card_updated_datetime,orm_rowid,created_user,created_datetime,updated_user,updated_datetime
[IF-CDP-CP-001]
IF_ID=IF-CDP-CP-001
SCHEMA_NAME=dwh_raw
TABLE_NAME=campaign_instructions
JOBNET_ID=DWH-JN-CDP-CP-001
UNLOAD_FIELDS=campaign_instructions_code,campaign_instructions_name,campaign_type,delete_flg,campaign_priority,campaign_applied_scope,campaign_use_limit,oneshot_order_limit,campaign_quantity_limit,campaign_start_date,campaign_end_date,present_use_flg,campaign_customer_flg,campaign_combi_limit_flg,permanent_campaign_flg,baitai_code,campaign_description,change_user_code,orm_rowid,created_user,created_datetime,updated_user,updated_datetime
[IF-CDP-CP-002]
IF_ID=IF-CDP-CP-002
SCHEMA_NAME=dwh_raw
TABLE_NAME=campaign_customer
JOBNET_ID=DWH-JN-CDP-CP-001
UNLOAD_FIELDS=campaign_instructions_code,customer_code,joken_type,orm_rowid,created_user,created_datetime,updated_user,updated_datetime
[IF-CDP-CP-003]
IF_ID=IF-CDP-CP-003
SCHEMA_NAME=dwh_raw
TABLE_NAME=campaign_instructions_commodity
JOBNET_ID=DWH-JN-CDP-CP-001
UNLOAD_FIELDS=campaign_instructions_code,shop_code,commodity_code,joken_type,orm_rowid,created_user,created_datetime,updated_user,updated_datetime
[IF-CDP-CP-004]
IF_ID=IF-CDP-CP-004
SCHEMA_NAME=dwh_raw
TABLE_NAME=campaign_promotion
JOBNET_ID=DWH-JN-CDP-CP-001
UNLOAD_FIELDS=campaign_instructions_code,promotion_no,promotion_type,shop_code,commodity_code,commodity_name,present_qt,discount_rate,discount_amount,discount_retail_price,shipping_charge,orm_rowid,created_user,created_datetime,updated_user,updated_datetime
[IF-CDP-OR-001]
IF_ID=IF-CDP-OR-001
SCHEMA_NAME=dwh_raw
TABLE_NAME=order_header
JOBNET_ID=DWH-JN-CDP-OR-001
UNLOAD_FIELDS=order_no,shop_code,order_datetime,customer_code,neo_customer_no,guest_flg,last_name,first_name,last_name_kana,first_name_kana,email,birth_date,sex,postal_code,prefecture_code,address1,address2,address3,address4,corporation_post_name,phone_number,advance_later_flg,payment_method_no,payment_method_type,payment_method_name,ext_payment_method_type,payment_commission,payment_commission_tax_gr_code,payment_commission_tax_no,payment_commission_tax_rate,payment_commission_tax,payment_commission_tax_type,coupon_management_code,coupon_code,coupon_name,coupon_type,coupon_use_purchase_price,coupon_discount_type,coupon_discount_price,coupon_discount_rate,coupon_used_amount,coupon_start_datetime,coupon_end_datetime,coupon_kbn,goods_group,commodity_category_code,commodity_series,coupon_commodity_code_display,baitai_name,used_point,total_amount,ec_promotion_id,ec_promotion_name,ec_promotion_discount_price,ec_campaign_id,ec_campaign_name,payment_date,payment_limit_date,payment_status,ext_payment_status,customer_group_code,data_transport_status,order_status,ext_order_status,tax_reference_date,cancel_date,client_group,caution,message,payment_order_id,cvs_code,payment_receipt_no,payment_receipt_url,receipt_no,customer_no,confirm_no,career_key,order_create_error_code,order_display_status,order_kind_kbn,marketing_channel,original_order_no,external_order_no,order_recieve_datetime,order_update_datetime,order_update_reason_kbn,cancel_reason_kbn,uncollectible_date,order_total_price,account_receivable_balance,appropriate_amount,bill_address_kbn,receipt_flg,receipt_to,receipt_detail,bill_price,bill_no,bill_print_count,authority_result_kbn,authority_no,card_password,authority_approval_no,authority_date,authority_price,authority_cancel_approval_no,authority_cancel_date,credit_payment_no,credit_payment_date,credit_payment_price,credit_cancel_payment_no,credit_cancel_payment_date,credit_result_kbn,card_brand,credit_card_kanri_no,credit_card_kanri_detail_no,credit_card_no,credit_card_meigi,credit_card_valid_year,credit_card_valid_month,credit_card_pay_count,payment_bar_code,amzn_charge_permission_id,amzn_charge_id,amzn_charge_status,amzn_authorization_datetime,amzn_capture_initiated_datetime,amzn_captured_datetime,amzn_canceled_datetime,order_user_code,order_user,change_user_code,change_user,demand_kbn,demand1_ref_date,demand1_date,demand1_limit_date,demand1_amount,demand1_bar_code,demand2_ref_date,demand2_date,demand2_limit_date,demand2_amount,demand2_bar_code,demand3_ref_date,demand3_date,demand3_limit_date,demand3_amount,demand3_bar_code,kashidaore_date,demand_exclude_reason_kbn,demand_exclude_start_date,demand_exclude_end_date,bill_sei_kj,bill_mei_kj,bill_sei_kn,bill_mei_kn,bill_tel_no,bill_zipcd,bill_addr1,bill_addr2,bill_addr3,bill_addr4,bill_corporation_post_name,nohinsyo_uketsuke_tanto,grant_plan_point_prod,grant_plan_point_other,grant_plan_point_total,grant_point_prod,grant_point_other,grant_point_total,reduction_plan_point_total,reduction_point_total,subtotal_before_campaign,subtotal_after_campaign,total_before_campaign,orm_rowid,created_user,created_datetime,updated_user,updated_datetime
[IF-CDP-OR-002]
IF_ID=IF-CDP-OR-002
SCHEMA_NAME=dwh_raw
TABLE_NAME=order_detail
JOBNET_ID=DWH-JN-CDP-OR-001
UNLOAD_FIELDS=order_no,order_detail_no,shop_code,sku_code,commodity_code,commodity_name,commodity_kind,baitai_code,baitai_name,hinban_code,standard_detail1_name,standard_detail2_name,purchasing_amount,unit_price,retail_price,retail_tax,commodity_tax_group_code,commodity_tax_no,commodity_tax_rate,commodity_tax,commodity_tax_type,campaign_code,campaign_name,campaign_instructions_code,campaign_instructions_name,campaign_discount_rate,campaign_discount_price,present_campaign_instructions_code,present_order_detail_no,age_limit_code,age_limit_name,age,age_limit_confirm_type,applied_point_rate,benefits_code,benefits_name,benefits_commodity_code,stock_management_type,stock_allocated_kbn,allocated_warehouse_code,allocated_quantity,arrival_reserved_quantity,cancel_quantity,henpin_qt,coupon_management_code,coupon_code,coupon_name,coupon_discount_rate,coupon_discount_price,ec_promotion_id,ec_promotion_name,ec_promotion_discount_price,ec_campaign_id,ec_campaign_name,adjustment_price,keihi_hurikae_target_flg,member_price_applied_flg,shipping_charge_target_flg,regular_contract_no,regular_contract_detail_no,regular_kaiji,regular_check_memo,total_commodity_buy_count,total_commodity_regular_kaiji,regular_total_commodity_regular_kaiji,commodity_category_code,total_category_buy_count,total_categoryregular_kaiji,regular_total_categoryregular_kaiji,commodity_subcategory_code,total_subcategory_buy_count,total_subcategoryregular_kaiji,regular_total_subcategoryregular_kaiji,commodity_subsubcategory_code,total_subsubcategory_buy_count,total_subsubcategoryregular_kaiji,regular_total_subsubcategoryregular_kaiji,grant_plan_point_prod_detail,reduction_plan_point_prod_detail,orm_rowid,created_user,created_datetime,updated_user,updated_datetime
[IF-CDP-OR-003]
IF_ID=IF-CDP-OR-003
SCHEMA_NAME=dwh_raw
TABLE_NAME=regular_sale_cont_header
JOBNET_ID=DWH-JN-CDP-OR-002
UNLOAD_FIELDS=regular_contract_no,shop_code,regular_sale_cont_datetime,customer_code,neo_customer_no,payment_method_no,address_no,regular_sale_cont_status,next_delivery_request_date,external_order_no,order_user_code,regular_update_datetime,change_user_code,regular_update_reason_kbn,otodoke_hope_time_kbn,marketing_channel,delivery_type_no,shipping_method_flg,ext_payment_method_type,card_brand,credit_card_kanri_no,credit_card_kanri_detail_no,credit_card_no,credit_card_meigi,credit_card_valid_year,credit_card_valid_month,credit_card_pay_count,amzn_charge_permission_id,bill_address_kbn,bill_print_otodoke_id,o_name_disp_kbn,delivery_note_flg,include_flg,receipt_flg,receipt_to,receipt_detail,first_shipping_date,lastest_shipping_date,first_delivery_date,lastest_delivery_date,regular_stop_date,regular_stop_reason_kbn,regular_hold_date,regular_hold_clear_date,regular_kaiji,shipped_regular_count,delivery_memo,regular_hold_reason_kbn,niyose_flg,orm_rowid,created_user,created_datetime,updated_user,updated_datetime
[IF-CDP-OR-004]
IF_ID=IF-CDP-OR-004
SCHEMA_NAME=dwh_raw
TABLE_NAME=regular_sale_cont_detail
JOBNET_ID=DWH-JN-CDP-OR-002
UNLOAD_FIELDS=regular_contract_no,regular_contract_detail_no,shop_code,sku_code,commodity_code,contract_amount,commodity_name,commodity_subcategory_code,commodity_subcategory_code_name,baitai_code,regular_cycle_delivery_kbn,regular_cycle_kijun_date,regular_kind,regular_cycle_day_int,regular_cycle_day,regular_cycle_mon_interval,regular_cycle_mon_interval_day,regular_cycle_week_num,regular_cycle_week_kbn,regular_cycle_week_mon,regular_cycle_week_tue,regular_cycle_week_wed,regular_cycle_week_thu,regular_cycle_week_fri,regular_cycle_week_sat,regular_cycle_week_sun,regular_cycle_week_hol,cycle_disp_name,next_shipping_plan_date,next_shipping_date,next_delivery_plan_date,next_delivery_date,lastest_delivery_date,regular_kaiji,shipped_regular_count,regular_sale_stop_from,regular_sale_stop_to,hasso_souko_cd,shipping_area,regular_check_memo,regular_memo_hold_flg,souko_shiji,next_regular_sale_stop_status,regular_stop_reason_kbn,orm_rowid,created_user,created_datetime,updated_user,updated_datetime
[IF-CDP-OR-005]
IF_ID=IF-CDP-OR-005
SCHEMA_NAME=dwh_raw
TABLE_NAME=regular_sale_cont_composition
JOBNET_ID=DWH-JN-CDP-OR-002
UNLOAD_FIELDS=regular_contract_no,regular_contract_detail_no,composition_no,shop_code,parent_commodity_code,parent_sku_code,child_commodity_code,child_sku_code,commodity_name,composition_quantity,regular_sale_composition_no,regular_sale_composition_name,regular_sale_commodity_type,regular_order_count_min_limit,regular_order_count_max_limit,regular_order_count_interval,orm_rowid,created_user,created_datetime,updated_user,updated_datetime
[IF-CDP-PR-001]
IF_ID=IF-CDP-PR-001
SCHEMA_NAME=dwh_raw
TABLE_NAME=period_price_linkage
JOBNET_ID=DWH-JN-CDP-PR-001
UNLOAD_FIELDS=mdm_integration_management_cd,tax_exc,tax_inc,tax,tax_rate,apply_start_date,mdm_integration_management_cd_nk,insert_date,insert_id,modify_date,modify_id
[IF-CDP-PR-002]
IF_ID=IF-CDP-PR-002
SCHEMA_NAME=dwh_raw
TABLE_NAME=dgroup
JOBNET_ID=DWH-JN-CDP-PR-001
UNLOAD_FIELDS=lgroup,mgroup,sgroup,dgroup,dgroup_name,lgroup_nk,insert_date,insert_id,modify_date,modify_id
[IF-CDP-PR-003]
IF_ID=IF-CDP-PR-003
SCHEMA_NAME=dwh_raw
TABLE_NAME=product_series
JOBNET_ID=DWH-JN-CDP-PR-001
UNLOAD_FIELDS=product_series,product_series_name,product_series_nk,insert_date,insert_id,modify_date,modify_id
[IF-CDP-PR-004]
IF_ID=IF-CDP-PR-004
SCHEMA_NAME=dwh_raw
TABLE_NAME=product_segment
JOBNET_ID=DWH-JN-CDP-PR-001
UNLOAD_FIELDS=product_segment,product_segment_name,product_segment_nk,insert_date,insert_id,modify_date,modify_id
[IF-CDP-PR-005]
IF_ID=IF-CDP-PR-005
SCHEMA_NAME=dwh_raw
TABLE_NAME=product_cat
JOBNET_ID=DWH-JN-CDP-PR-001
UNLOAD_FIELDS=product_cat,product_cat_name,product_cat_nk,insert_date,insert_id,modify_date,modify_id
[IF-CDP-PR-006]
IF_ID=IF-CDP-PR-006
SCHEMA_NAME=dwh_raw
TABLE_NAME=product_linkage
JOBNET_ID=DWH-JN-CDP-PR-001
UNLOAD_FIELDS=mdm_integration_management_cd,product_picture_id,product_no,mail_order_product_cd,store_sales_product_cd,warehouse_management_cd,jan,jan_issue_flg,main_product_no,core_product_name,web_product_name,product_name,registration_name,law_cat_cd,product_segment,business_segment,product_cat,product_series,sale_start_date,period_set_sales_channel_1,sales_channel_1_sale_start_date,sales_channel_1_sale_end_date,period_set_sales_channel_2,sales_channel_2_sale_start_date,sales_channel_2_sale_end_date,period_set_sales_channel_3,sales_channel_3_sale_start_date,sales_channel_3_sale_end_date,sale_status,lgroup,mgroup,sgroup,dgroup,product_type,core_department,accountin_pattern_gb,material,preferential_product_flg,set_product_flg,set_composition_flg,company_sales_buy_flg,emprate_pms_flg,age_limit_cd,store_po_gb,web,callcenter,before_renewal_product_no,dep,representative_product_cd,order_per_order_max,buttobi_subsc_bundle_yn,return_yn,exch_yn,lot_management_target_product,reduction_base,depth,width,height,trade_cnt,weight,outerbox_depth,outerbox_width,outerbox_height,outerbox_weight,case_per_include_cnt,insertion_depth,insertion_width,insertion_height,palette_stack_cnt_face,palette_stack_cnt_lavel,contents,nekoposu_volume_rate,outside_home_volume_rate,color_name,color_cd,original_color_cd,size_name,size_cd,shape_name,shape_cd,season,unit_cd,buy_send_reservation_flg,delivery_notice_undisplay_flg,airmail_rack_yn,mail_delivery_flg,outside_home_receive_service_flg,out_indicate_warehouse,warehouse_assembly_set_product_flg,sagawa_yn_flg,folding_flg,vender,use_point_cnt,composition_oms_link_flg,mdm_integration_management_cd_nk,insert_date,insert_id,modify_date,modify_id
[IF-CDP-PR-007]
IF_ID=IF-CDP-PR-007
SCHEMA_NAME=dwh_raw
TABLE_NAME=sgroup
JOBNET_ID=DWH-JN-CDP-PR-001
UNLOAD_FIELDS=lgroup,mgroup,sgroup,sgroup_name,lgroup_nk,insert_date,insert_id,modify_date,modify_id
[IF-CDP-PR-008]
IF_ID=IF-CDP-PR-008
SCHEMA_NAME=dwh_raw
TABLE_NAME=mgroup
JOBNET_ID=DWH-JN-CDP-PR-001
UNLOAD_FIELDS=lgroup,mgroup,mgroup_name,lgroup_nk,insert_date,insert_id,modify_date,modify_id
[IF-CDP-PR-009]
IF_ID=IF-CDP-PR-009
SCHEMA_NAME=dwh_raw
TABLE_NAME=product_type
JOBNET_ID=DWH-JN-CDP-PR-001
UNLOAD_FIELDS=product_type,product_type_name,product_type_nk,insert_date,insert_id,modify_date,modify_id
[IF-CDP-PR-010]
IF_ID=IF-CDP-PR-010
SCHEMA_NAME=dwh_raw
TABLE_NAME=lgroup
JOBNET_ID=DWH-JN-CDP-PR-001
UNLOAD_FIELDS=lgroup,lgroup_name,lgroup_nk,insert_date,insert_id,modify_date,modify_id
[IF-CDP-PR-011]
IF_ID=IF-CDP-PR-011
SCHEMA_NAME=dwh_raw
TABLE_NAME=commodity_header
JOBNET_ID=DWH-JN-CDP-PR-011
UNLOAD_FIELDS=shop_code,commodity_code,commodity_name,commodity_type,represent_sku_code,represent_sku_unit_price,stock_status_no,stock_management_type,age_limit_code,commodity_tax_type,tax_group_code,short_description,commodity_search_words,prior_printing_description,posterior_printing_description,delivery_description,sale_start_datetime,sale_end_datetime,discount_price_start_datetime,discount_price_end_datetime,reservation_start_datetime,reservation_end_datetime,prior_printing_start_date,prior_printing_end_date,posterior_printing_start_date,posterior_printing_end_date,delivery_type_no,sales_method_type,manufacturer_model_no,link_url,recommend_commodity_rank,commodity_popular_rank,commodity_standard1_name,commodity_standard2_name,commodity_point_rate,commodity_point_start_datetime,commodity_point_end_datetime,sale_flg,noshi_effective_flg,arrival_goods_flg,oneshot_order_limit,standard_image_type,purchasing_confirm_flg_pc,purchasing_confirm_flg_sp,commodity_kind,keihi_hurikae_target_flg,charge_user_code,commodity_remark,channel_cc_sale_flg,channel_ec_sale_flg,shipping_charge_target_flg,first_purchase_limit_flg,purchase_hold_flg,commodity_exclude_flg,commodity_subsubcategory_code,pack_calc_pattern,pad_type,fall_down_flg,height,width,deepness,weight,tracking_out_flg,mdm_management_code,commodity_segment,business_segment,commodity_group,commodity_series,core_department,accounting_pattern_type,return_enabled_flg,exchange_enabled_flg,exterior_box_weight,nekoposu_volume_rate,warehouse_assembly_flg,mail_delivery_flg,before_renewal_commodity_code,preorder_enable_days,main_product_no,product_no,orm_rowid,created_user,created_datetime,updated_user,updated_datetime
[IF-CDP-PR-012]
IF_ID=IF-CDP-PR-012
SCHEMA_NAME=dwh_raw
TABLE_NAME=commodity_detail
JOBNET_ID=DWH-JN-CDP-PR-011
UNLOAD_FIELDS=shop_code,sku_code,commodity_code,unit_price,discount_price,reservation_price,jan_code,standard_detail1_name,standard_detail2_name,hinban_code,hinban_kind,member_price_applied_flg,member_price_discount_rate,member_price,air_transport_flg,commodity_prod_pack_type,delivery_note_no_disp_flg,reduction_point,orm_rowid,created_user,created_datetime,updated_user,updated_datetime
[IF-CDP-PR-013]
IF_ID=IF-CDP-PR-013
SCHEMA_NAME=dwh_raw
TABLE_NAME=set_commodity_composition
JOBNET_ID=DWH-JN-CDP-PR-012
UNLOAD_FIELDS=shop_code,commodity_code,child_commodity_code,composition_quantity,composition_order,orm_rowid,created_user,created_datetime,updated_user,updated_datetime
[IF-CDP-PR-014]
IF_ID=IF-CDP-PR-014
SCHEMA_NAME=dwh_raw
TABLE_NAME=regular_sale_base
JOBNET_ID=DWH-JN-CDP-PR-013
UNLOAD_FIELDS=shop_code,regular_sale_code,sku_code,commodity_code,regular_cycle_kind_list,regular_cycle_days_list,regular_cycle_months_list,regular_sale_stop_from,regular_sale_stop_to,orm_rowid,created_user,created_datetime,updated_user,updated_datetime
[IF-CDP-PR-015]
IF_ID=IF-CDP-PR-015
SCHEMA_NAME=dwh_raw
TABLE_NAME=regular_sale_composition
JOBNET_ID=DWH-JN-CDP-PR-013
UNLOAD_FIELDS=shop_code,regular_sale_code,regular_sale_composition_no,regular_sale_composition_name,regular_order_count_min_limit,regular_order_count_max_limit,regular_order_count_interval,retail_price,regular_sale_commodity_point,display_order,orm_rowid,created_user,created_datetime,updated_user,updated_datetime
[IF-CDP-PR-016]
IF_ID=IF-CDP-PR-016
SCHEMA_NAME=dwh_raw
TABLE_NAME=regular_sale_payment
JOBNET_ID=DWH-JN-CDP-PR-013
UNLOAD_FIELDS=shop_code,regular_sale_code,payment_method_no,orm_rowid,created_user,created_datetime,updated_user,updated_datetime
[IF-CDP-PR-017]
IF_ID=IF-CDP-PR-017
SCHEMA_NAME=dwh_raw
TABLE_NAME=regular_sale_commodity
JOBNET_ID=DWH-JN-CDP-PR-013
UNLOAD_FIELDS=shop_code,regular_sale_code,regular_sale_composition_no,sku_code,commodity_code,display_order,regular_sale_commodity_type,regular_sale_commodity_point,difference_price,orm_rowid,created_user,created_datetime,updated_user,updated_datetime
[IF-CDP-SL-001]
IF_ID=IF-CDP-SL-001
SCHEMA_NAME=dwh_raw
TABLE_NAME=sales_info_alignment_header
JOBNET_ID=DWH-JN-CDP-SL-001
UNLOAD_FIELDS=shop_cd,register_num,business_date,receipt_num,chit_num,system_datetime,open_count,is_void,check_kind,return_kind,sub_check_kind,sales_group_cd,deposit_kind,operator_cd,operator_name,sales_man_cd,sales_man_name,return_employee_cd,return_employee_name,void_employee_cd,void_employee_name,staff_sale_employee_cd,staff_sale_employee_name,customer_cd,customer_layer_cd,customer_layer_cd2,purchase_motive_cd,return_reason_cd,void_reason_cd,net_sales_amount_of_outside_tax,net_sales_amount_of_inside_tax,net_sales_amount_of_tax_free,net_sales_outside_tax,net_sales_inside_tax,net_sales_quantity,outside_sales_amount_of_outside_tax,outside_sales_amount_of_inside_tax,outside_sales_amount_of_tax_free,outside_sales_outside_tax,outside_sales_inside_tax,outside_sales_quantity,total_amount,discount_amount,discount_tax_inclusive,is_revenue_stamp,order_line_count,pay_line_count,is_total_display,is_reduced_tax_rate_trade,is_tax_free,customers_num,deliver_date,sale_attribute_cd1,sale_attribute_cd2,sale_attribute_cd3,sale_attribute_cd4,campaign_no,closing_date,return_date,order_number,employee_meal,employee_code,table_no,acceptance_time,menu_cook_cmp_time,menu_offer_cmp_time,service_charge_amount_outside_tax,service_charge_amount_inside_tax,service_charge_tax_exclusive,service_charge_tax_inclusive,service_charge_amount1,service_charge_amount2,service_charge_minus_amount,service_charge_minus_tax_inclusive,service_charge_target,service_charge_button,service_charge1_button,service_charge2_button,eat_in_amount,takeout_amount,vein_employee_cd,is_vein_authentication,out_calc_flg,sale_goods_flg,point_linkage_kind,numeric_reserve1,numeric_reserve2,numeric_reserve3,numeric_reserve4,numeric_reserve5,numeric_reserve6,numeric_reserve7,numeric_reserve8,numeric_reserve9,numeric_reserve10,string_reserve1,string_reserve2,string_reserve3,string_reserve4,string_reserve5,string_reserve6,string_reserve7,string_reserve8,string_reserve9,string_reserve10
[IF-CDP-SL-002]
IF_ID=IF-CDP-SL-002
SCHEMA_NAME=dwh_raw
TABLE_NAME=sales_info_alignment_detail
JOBNET_ID=DWH-JN-CDP-SL-001
UNLOAD_FIELDS=shop_cd,register_num,business_date,receipt_num,line_num,line_kind,is_in_store_marking,brand_cd,dept_cd,class_cd,sub_class_cd,item_cd,item_name,dept_group_cd,parent_item_cd,jan_cd,item_num,color_cd,size_cd,year_cd,season_cd,attribute_cd1,attribute_cd2,attribute_cd3,attribute_cd4,attribute_cd5,scan_bar_cd1,scan_bar_cd2,discount_cd,bmplan_cd,coupon_num,is_include_sales,item_kind,tax_kind,tax_rate,is_reduced_tax_rate,is_proper_item,is_change_price,master_unit_price,body_price,fixed_price,unit_price,quantity,amount,tax_inclusive,tax_outside,tax,disc_rate,line_minus_amount,line_minus_tax_inclusive,bmset_minus_amount,bmset_minus_tax_inclusive,point_minus_amount,point_minus_tax_inclusive,coupon_minus_amount,coupon_minus_tax_inclusive,sub_total_minus_amount,sub_total_minus_tax_inclusive,before_disc_tax_inclusive,sales_man_cd,stock_kind,is_inventory_counted,is_promotion_ticket,promotion_ticket_bar_cd,is_promotion_ticket_allow_combination,oes_item_flag,oes_slip_no,oes_slip_sub_no,grand_classification,menu_classification,oes_line_num,oes_quantity,oes_minus,pos_minus,takeout_flag,service_charge_minus_amount,service_charge_minus_tax_inclusive,service_charge_flag,service_charge1_flag,service_charge2_flag,service_charge1_manually_flag,service_charge2_manually_flag,oes_service_charge1,oes_service_charge2,cooking_directions_time,cooking_complete_time,offer_complete_time,acceptance_time,menu_cook_cmp_time,menu_offer_cmp_time,disc_amount_limit,is_follow_disc,is_allow_credit,is_employee_acnt_recv,employee_cd,sub_menu_kind,grand_menu_code,grand_menu_index,select_kind,is_quantity_count,is_order_entry,modifier_kind,return_target_line_num,numeric_reserve1,numeric_reserve2,numeric_reserve3,numeric_reserve4,numeric_reserve5,numeric_reserve6,numeric_reserve7,numeric_reserve8,numeric_reserve9,numeric_reserve10,string_reserve1,string_reserve2,string_reserve3,string_reserve4,string_reserve5,string_reserve6,string_reserve7,string_reserve8,string_reserve9,string_reserve10
[IF-CDP-SH-001]
IF_ID=IF-CDP-SH-001
SCHEMA_NAME=dwh_raw
TABLE_NAME=shop_mst_raw
JOBNET_ID=DWH-JN-CDP-SH-001
UNLOAD_FIELDS=shop_cd,group_cd,area_cd,district_cd,shop_kind,shop_name_full,shop_name_half,shop_name_short,shop_name_english,zip_cd,prefecture_cd,address1,address2,address3,address4,tel_num,fax_num,tax_inclusive_round_kind,tax_exclusive_round_kind,condition_kind,condition_detail_kind,channel_kind,is_stock_control,begin_date,end_date,shop_floor_space,is_abandon_price_change,is_abandon_stock_transfer,form_management_kind,is_shop_terminal,allow_transfer_group_cd,main_brand_cd,disc_round_position,disc_round_kind,is_emp_salse_place,is_move_operation,is_sales_register,is_stock_display,brock_cd,pricechange_disc_round_position,pricechange_disc_round_kind,numeric_reserve1,numeric_reserve2,numeric_reserve3,numeric_reserve4,numeric_reserve5,string_reserve1,string_reserve2,string_reserve3,string_reserve4,string_reserve5,is_deleted,spare_numeric_reserve1,spare_numeric_reserve2,spare_numeric_reserve3,apare_numeric_reserve4,spare_numeric_reserve5,spare_string_reserve1,spare_string_reserve2,spare_string_reserve3,spare_string_reserve4,spare_string_reserve5,spare_string_reserve6,spare_string_reserve7
[IF-CDP-FA-001]
IF_ID=IF-CDP-FA-001
SCHEMA_NAME=dwh_raw
TABLE_NAME=favorite_product
JOBNET_ID=DWH-JN-CDP-FA-001
UNLOAD_FIELDS=id,ownerid,isdeleted,name,createddate,createdbyid,lastmodifieddate,lastmodifiedbyid,systemmodstamp,accountid__c,productid__c,productcode__c,productname__c,isdeleted__c
[IF-CDP-CO-001]
IF_ID=IF-CDP-CO-001
SCHEMA_NAME=dwh_raw
TABLE_NAME=coupon
JOBNET_ID=DWH-JN-CDP-CO-001
UNLOAD_FIELDS=coupon_management_code,coupon_code,coupon_name,coupon_invalid_flag,coupon_type,coupon_issue_type,coupon_use_limit,coupon_use_purchase_price,coupon_discount_type,coupon_discount_rate,coupon_discount_price,coupon_start_datetime,coupon_end_datetime,coupon_limit_display_period,coupon_limit_display,coupon_description,coupon_message,coupon_kbn,coupon_post_in_charge,coupon_commodity_flag,marketing_channel_list,goods_group,commodity_category_code,commodity_series,orm_rowid,created_user,created_datetime,updated_user,updated_datetime
[IF-CDP-CO-002]
IF_ID=IF-CDP-CO-002
SCHEMA_NAME=dwh_raw
TABLE_NAME=coupon_customer
JOBNET_ID=DWH-JN-CDP-CO-001
UNLOAD_FIELDS=coupon_management_code,customer_code,neo_customer_no,coupon_issue_status,coupon_used_count,coupon_used_date,baitai_code,orm_rowid,created_user,created_datetime,updated_user,updated_datetime
[IF-CDP-CO-003]
IF_ID=IF-CDP-CO-003
SCHEMA_NAME=dwh_raw
TABLE_NAME=coupon_commodity
JOBNET_ID=DWH-JN-CDP-CO-001
UNLOAD_FIELDS=coupon_management_code,shop_code,commodity_code,orm_rowid,created_user,created_datetime,updated_user,updated_datetime
[DWH-JN-DWH-SH-001]
JOBNET_ID=DWH-JN-DWH-SH-001
SOURCE_SCHEMA=dwh_raw
SOURCE_TABLE=shop_mst_raw
TARGET_SCHEMA=dwh_storage
TARGET_TABLE=shop_mst_storage
[DWH-JN-DWH-SH-002]
JOBNET_ID=DWH-JN-DWH-SH-002
SOURCE_SCHEMA=dwh_storage
SOURCE_TABLE=shop_mst_storage
TARGET_SCHEMA=dwh_analysis
TARGET_TABLE=shop_mst_analysis