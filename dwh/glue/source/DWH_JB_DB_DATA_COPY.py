import sys
import time
import os
import traceback
sys.path.append(os.path.dirname(__file__))
import redshift_connector
from awsglue.utils import getResolvedOptions
from common import common
from glue_logger import GlueLogger

# Glue ジョブのパラメータ取得
args = getResolvedOptions(sys.argv, ['JOBNET_ID'])

# 共通部品の初期化
common = common()
# Glueジョブのパラメータ「JOBNET_ID」を取得
job_config_key = args['JOBNET_ID']
# ジョブネットIDを取得
jobnet_id = common.get_config_value(job_config_key, "JOBNET_ID")
# データのソーススキーマとテーブル名を取得
source_schema = common.get_config_value(job_config_key, "SOURCE_SCHEMA")
source_table = common.get_config_value(job_config_key, "SOURCE_TABLE")
# データのターゲットスキーマとテーブル名を取得
target_schema = common.get_config_value(job_config_key, "TARGET_SCHEMA")
target_table = common.get_config_value(job_config_key, "TARGET_TABLE")

# ログの初期化
logger = GlueLogger(jobnet_id)


# Redshiftコネクション(Glueを利用)
secret_name = common.get_paramter_store_value("SECRET_NAME")
logger.info("I_DWH_JB_DB_DATA_COPY_001", msg_values=(jobnet_id))

try:
    # Redshift 接続情報を取得
    try:
        secrets = common.get_secret(secret_name)
        redshift_host = secrets["host"]
        redshift_user = secrets["username"]
        redshift_password = secrets["password"]
        redshift_db = secrets["database"]
    except Exception as e:
        logger.error("E_DWH_JB_DB_DATA_COPY_004", msg_values=(jobnet_id))
        raise
    
    # Redshift へ接続
    try:
        conn = redshift_connector.connect(
            host=redshift_host,
            database=redshift_db,
            user=redshift_user,
            password=redshift_password,
        )
        conn.autocommit = False
    except Exception as e:
        logger.error("E_DWH_JB_DB_DATA_COPY_005", msg_values=(jobnet_id))
        raise

    try:
        with conn.cursor() as cursor:
            cursor.execute("BEGIN;")  # トランザクションを開始
            cursor.execute(f"TRUNCATE TABLE {target_schema}.{target_table};")
            cursor.execute(f"""
                INSERT INTO {target_schema}.{target_table}
                SELECT * FROM {source_schema}.{source_table};
            """)
            conn.commit()  # トランザクションをコミット
        logger.info("I_DWH_JB_DB_DATA_COPY_002", msg_values=(jobnet_id))
    except Exception as e:
        conn.rollback()  # トランザクションをロールバック
        logger.error("E_DWH_JB_DB_DATA_COPY_001", msg_values=(jobnet_id))
        raise
    finally:
        if conn:
            conn.close()
    logger.info("I_DWH_JB_DB_DATA_COPY_003", msg_values=(jobnet_id))

except Exception as e:
    stack_trace = traceback.format_exc() # 例外のスタックトレースを取得
    logger.error("E_DWH_JB_DB_DATA_COPY_002", msg_values=(jobnet_id, stack_trace)) # 終了時の異常ログ

    sys.stdout.flush()
    sys.stderr.flush()
    time.sleep(2)  # ログ確実に記録できるように
    logger.error("E_DWH_JB_DB_DATA_COPY_003", msg_values=(jobnet_id))
    raise # 異常終了
