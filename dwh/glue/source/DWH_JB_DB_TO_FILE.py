import sys
import os

sys.path.append(os.path.dirname(__file__))
from awsglue.utils import getResolvedOptions
from pyspark.context import SparkContext
from awsglue.context import GlueContext
from awsglue.job import Job
import datetime as dt
import boto3
import json
import redshift_connector
from common import common
from glue_logger import GlueLogger


# 入力パラメータを取得
args = getResolvedOptions(sys.argv, ["JOB_NAME", "IF_IDS"])


sc = SparkContext()
glueContext = GlueContext(sc)
spark = glueContext.spark_session
job = Job(glueContext)
job.init(args["JOB_NAME"], args)

# バックアップファイルパスリストを作成
backup_path_list = []
# 出力パスに保持するリストを作成
output_path_list = []
# S3クライアントを作成
s3_client = boto3.client("s3")

# 共通部品の初期化
common = common()

# インタフェースIDのリストを作成
if_ids = json.loads(args["IF_IDS"])

# 環境変数取得
# 出力ファイル配置用S3バケット
s3_bucket = common.get_paramter_store_value("S3_BUCKET_NAME_PREFIX")
# RedshiftのSecrets Managerシークレット名
secret_name = common.get_paramter_store_value("SECRET_NAME")
# 出力するファイルのS3プレフィックス
s3_csv_output_prefix = common.get_paramter_store_value("S3_CSV_OUTPUT_PATH")
# バックアップファイルのS3プレフィックス
s3_csv_backup_prefix = common.get_paramter_store_value("S3_CSV_BACKUP_PATH")
# アンロード用ロール
iam_role = common.get_paramter_store_value("DWH_UNLOAD_IAM_ROLE")

# インタフェースIDのリストを作成
if_ids = eval(json.loads(args["IF_IDS"]))

# インターフェースIDのリストが空かチェック
if not if_ids:
    msg_format = "[%s][%s][%s]%s"  # [日時][ログレベル][機能名]エラーメッセージ内容
    error_log = msg_format % (
        dt.datetime.now().strftime("%Y/%m/%d %H:%M:%S"),
        "ERROR",
        "DWH_JB_DB_TO_FILE",
        "入力パラメータ用インタフェースID取得できない、処理異常終了。",
    )
    print(error_log)
    raise ValueError(f"入力パラメータ用インタフェースID取得できない、処理異常終了。")

for if_id in if_ids:
    job_config_key = if_id
    jobnet_id = ""
    try:
        # インターフェースIDを取得
        interface_id = common.get_config_value(job_config_key, "IF_ID")
        # スキーマ名を取得
        schema_name = common.get_config_value(job_config_key, "SCHEMA_NAME")
        # テーブル名を取得
        table_name = common.get_config_value(job_config_key, "TABLE_NAME")
        # テアンロード項目リストを取得
        unload_fields = common.get_config_value(job_config_key, "UNLOAD_FIELDS")
        # ジョブネットIDを取得
        jobnet_id = common.get_config_value(job_config_key, "JOBNET_ID")

        # 開始処理
        logger = GlueLogger(jobnet_id)
        logger.info("I_JOB_DWH_DB_TO_FILE_001", msg_values=(if_id))

        # Redshift接続情報を取得
        secrets = common.get_secret(secret_name)
        redshift_host = secrets["host"]
        redshift_user = secrets["username"]
        redshift_password = secrets["password"]
        redshift_db = secrets["database"]

        # Redshiftに接続
        try:
            conn = redshift_connector.connect(
                host=redshift_host,
                database=redshift_db,
                user=redshift_user,
                password=redshift_password,
            )
        except Exception as e:
            # UNLOAD実行エラー
            logger.error("E_JOB_DWH_DB_TO_FILE_005", msg_values=(if_id))
            raise e

        # 実行日付を取得
        t_delta = dt.timedelta(hours=9)  # 9時間
        JST = dt.timezone(t_delta, "JST")  # UTCから9時間差の「JST」タイムゾーン
        current_time = dt.datetime.now(JST)
        current_time_str = current_time.strftime("%Y%m%d%H%M%S")
        sys_date = current_time.strftime("%Y%m%d000000")
        if "--SYS_DATE" in sys.argv:
            input_sys_date = sys.argv[sys.argv.index("--SYS_DATE") + 1]
            try:
                # 入力された日付文字列が正しい形式（14桁）かチェック
                if len(input_sys_date) != 14:
                    raise ValueError(
                        f"日付フォーマットが不正です: {input_sys_date}。正しい形式: YYYYMMDDHHMMSS"
                    )
                # 入力された日付文字列が正しい形式かチェック
                dt.datetime.strptime(input_sys_date, "%Y%m%d%H%M%S")
                sys_date = input_sys_date
            except Exception as e:
                logger.error(
                    "E_JOB_DWH_DB_TO_FILE_006", msg_values=(if_id, input_sys_date)
                )
                raise e

        # 出力のパスを設定
        tmp_path = f"tmp_{current_time_str}"
        s3_path = f"s3://{s3_bucket}/{s3_csv_output_prefix}{if_id}/{tmp_path}/"
        output_file_prefix = f"{s3_csv_output_prefix}{if_id}/{tmp_path}/"
        # Redshift UNLOAD SQLクエリを作成
        sql_unload = f"""
        UNLOAD ('SELECT {unload_fields} FROM {schema_name}.{table_name} 
                WHERE dwh_updated_datetime >= TO_TIMESTAMP(''{sys_date}'', ''YYYYMMDDHH24MISS'')')
        TO '{s3_path}'
        IAM_ROLE '{iam_role}'
        FORMAT AS CSV
        DELIMITER ','
        PARALLEL OFF
        ALLOWOVERWRITE
        HEADER ;
        """

        # 出力パスに保持するリストに出力パスを追加
        output_path_list.append(output_file_prefix)

        # クエリを実行するためにカーソルを作成
        cur = conn.cursor()

        # UNLOAD実行
        try:
            cur.execute(sql_unload)
        except Exception as e:
            # UNLOAD実行エラー
            logger.error("E_JOB_DWH_DB_TO_FILE_001", msg_values=(if_id))
            raise e
        finally:
            # クエリの実行が完了したらカーソルと接続を閉じる
            if cur:
                cur.close()
            if conn:
                conn.close()

        # UNLOAD実行成功
        logger.info("I_JOB_DWH_DB_TO_FILE_002", msg_values=(if_id))

        ### ファイルマージ###
        try:
            # マージ対象ファイルリストを取得する
            response = s3_client.list_objects_v2(
                Bucket=s3_bucket, Prefix=output_file_prefix
            )
            files = [obj["Key"] for obj in response["Contents"]]

            # ローカルマージファイル名
            output_file = f"{if_id}_{current_time_str}.csv"
            header_written = False

            # ファイルをマージ
            with open(output_file, "w", encoding="utf-8") as outfile:
                for key in files:
                    obj = s3_client.get_object(Bucket=s3_bucket, Key=key)
                    # ファイル全体を読み込み、UTF-8 にデコード
                    file_content = obj["Body"].read().decode("utf-8")
                    # 行ごとに分割
                    lines = file_content.splitlines()
                    if not lines:
                        continue
                    if not header_written:
                        # 最初のファイルの場合はヘッダーも含めて全行を書き込む
                        outfile.write("\n".join(lines) + "\n")
                        header_written = True
                    else:
                        # 以降のファイルは最初の行（ヘッダー）をスキップして書き込む
                        outfile.write("\n".join(lines[1:]) + "\n")

            # アップロード先のキーを設定
            destination_key = f"{output_file_prefix}{output_file}"

            # ファイルを S3 にアップロード
            try:
                s3_client.upload_file(output_file, s3_bucket, destination_key)
                os.remove(output_file)
                for key in files:
                    s3_client.delete_object(Bucket=s3_bucket, Key=key)
                backup_path = f"{if_id}/{tmp_path}/{if_id}_{current_time_str}.csv"
                backup_path_list.append(backup_path)
            except Exception as e:
                if os.path.exists(output_file):
                    os.remove(output_file)
                raise e
        except Exception as e:
            # ファイルマージエラー
            logger.error("E_JOB_DWH_DB_TO_FILE_002", msg_values=(if_id))
            raise e

        # ァイルをマージ実行成功
        logger.info("I_JOB_DWH_DB_TO_FILE_003", msg_values=(if_id))
    except Exception as e:
        if not jobnet_id:
            msg_format = (
                "[%s][%s][%s]%s"  # [日時][ログレベル][機能名]エラーメッセージ内容
            )
            error_log = msg_format % (
                dt.datetime.now().strftime("%Y/%m/%d %H:%M:%S"),
                "ERROR",
                "DWH_JB_DB_TO_FILE",
                f"コンフィグファイル読み込み失敗。{e}",
            )
            print(error_log)
            raise
        else:
            # エラー場合
            for out_path in output_path_list:
                # 削除ファイルリストを取得
                response = s3_client.list_objects_v2(Bucket=s3_bucket, Prefix=out_path)

                if "Contents" in response:
                    files = [obj["Key"] for obj in response["Contents"]]
                    for key in files:
                        s3_client.delete_object(
                            Bucket=s3_bucket, Key=key
                        )  # ファイルを削除
            logger.error("E_JOB_DWH_DB_TO_FILE_003", msg_values=(if_id, e))
            logger.error("E_JOB_DWH_DB_TO_FILE_004", msg_values=(if_id))
            raise

    logger.info("I_JOB_DWH_DB_TO_FILE_004", msg_values=(if_id))


s3 = boto3.resource("s3")
for backup_path in backup_path_list:
    backup_path_parts = backup_path.split('/')
    backup_path_if = f"{backup_path_parts[0]}/{backup_path_parts[-1]}"
    copy_source = {"Bucket": s3_bucket, "Key": f"{s3_csv_output_prefix}{backup_path}"}
    s3.meta.client.copy(
        copy_source, Bucket=s3_bucket, Key=f"{s3_csv_output_prefix}{backup_path_if}"
    )
    s3.meta.client.copy(
        copy_source, Bucket=s3_bucket, Key=f"{s3_csv_backup_prefix}{backup_path_if}"
    )
    s3.meta.client.delete_object(
        Bucket=s3_bucket, Key=f"{s3_csv_output_prefix}{backup_path}"
    )

job.commit()
