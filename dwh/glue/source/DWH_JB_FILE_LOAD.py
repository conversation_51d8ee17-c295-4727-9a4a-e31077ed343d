from re import escape
import sys
import os
sys.path.append(os.path.dirname(__file__))
import boto3
import json
import datetime as dt
import redshift_connector
import re

from awsglue.transforms import *
from awsglue.utils import getResolvedOptions
from pyspark.context import SparkContext
from awsglue.context import GlueContext
from awsglue.dynamicframe import DynamicFrame
from awsglue.job import Job
from pyspark.sql.types import StructType, StringType, DecimalType
from pyspark.sql.functions import when, col, concat_ws, lit, udf
from pyspark.sql.functions import current_timestamp, from_utc_timestamp
from datetime import datetime,timedelta
from common import common
from glue_logger import GlueLogger


# 入力パラメータを取得
args = getResolvedOptions(sys.argv, ['JOB_NAME', 'if_ids', 'if_files'])  

sc = SparkContext()
glueContext = GlueContext(sc)
spark = glueContext.spark_session
job = Job(glueContext)
job.init(args["JOB_NAME"], args)

# 共通部品の初期化
common = common()

#Redshiftコネクション(Glue利用)
redshift_connection_glue = common.get_paramter_store_value("REDSHIFT_CONNECTION_GLUE")
#Redshiftの一時格納パス
redshift_tempdir = common.get_paramter_store_value("REDSHIFT_TEMPDIR")
#RedshiftのSecrets Managerシークレット名
secret_name = common.get_paramter_store_value("SECRET_NAME")

#IFファイル配置用S3バケット(prefix)
s3_bucket_name_prefix = common.get_paramter_store_value("S3_BUCKET_NAME_PREFIX")
#バックアップ元ファイルのS3プレフィックス
s3_bucket_name_suffix_copy_from = common.get_paramter_store_value("S3_BUCKET_NAME_SUFFIX_COPY_FROM")
#バックアップ先ファイルのS3プレフィックス
s3_bucket_name_suffix_copy_to = common.get_paramter_store_value("S3_BUCKET_NAME_SUFFIX_COPY_TO")

#区切り用変数
delimiter_value = ","
escape_value = "\""
quote_value = "\""

# 最大VARCHARの長さ
max_varchar_length = 65535

# Redshift接続情報を取得
secrets = common.get_secret(secret_name)
redshift_host = secrets["host"]
redshift_user = secrets["username"]
redshift_password = secrets["password"]
redshift_db = secrets["database"]

#データ加工用項目設定
dwh_created_user = "DWH"
dwh_updated_datetime = "DWH"

# インタフェースIDとファイルパスのリストを作成
if_ids = json.loads(args['if_ids'])
if_files = json.loads(args['if_files'])
ids_files = [{"if_ids": if_ids[i], "if_files": if_files[i]} for i in range(len(if_ids))]

#データ取得&加工&チェック共通処理
def data_check(if_id, if_group, file_path, mapping_info, primary_key):
    ###ヘッダチェック###
    try:
        #受信ファイルのヘッダー部を取得する
        options = {
            "header": True,
            "delimiter": delimiter_value,
            "quote": quote_value,
            "escape": escape_value,
            "maxRows": 1
        }
        df = spark.read.format("csv").load(file_path, **options)
        csv_headers = df.columns
    except:
        #データ抽出エラー
        if if_group:
            logger.error("E_DWH_JB_FILE_LOAD_004",msg_values=(if_group, if_id, file_path))
        else:
            logger.error("E_DWH_JB_FILE_LOAD_001",msg_values=(if_id, file_path))
        raise ValueError("データ抽出エラー")

    #必須チェック対象を取得
    primary_key_info = re.findall(r"\w+", primary_key)

    #マッピング対象を初期化
    mapping_dict = {}
    mapping_info_arr = mapping_info.split(";")
    for i in mapping_info_arr:
        # マッピング対象作成
        fields_arr = i.strip('{}').split('\\')
        mapping_dict[fields_arr[0]] = '\\'.join(fields_arr)

    #受信ファイルとマッピング対象の項目数不一致の場合、異常終了
    if len(csv_headers) != len(mapping_dict):
        if if_group:
            logger.error("E_DWH_JB_FILE_LOAD_005",msg_values=(if_group, if_id, "入力ヘッダ不正(CSVヘッダ数不正)"))
        else:
            logger.error("E_DWH_JB_FILE_LOAD_002",msg_values=(if_id, "入力ヘッダ不正(CSVヘッダ数不正)"))
        raise ValueError("入力ヘッダ不正(CSVヘッダ数不正)")
    
    #受信ファイルとマッピング対象の項目順不一致の場合、異常終了
    expected_headers = list(mapping_dict.keys())
    if csv_headers != expected_headers:
        if if_group:
            logger.error("E_DWH_JB_FILE_LOAD_005",msg_values=(if_group, if_id, "入力ヘッダ不正(CSVヘッダ順不正)"))
        else:
            logger.error("E_DWH_JB_FILE_LOAD_002",msg_values=(if_id, "入力ヘッダ不正(CSVヘッダ順不正)"))
        raise ValueError("入力ヘッダ不正(CSVヘッダ順不正)")

    #取得したヘッダー順で取込用マッピング情報を作成する
    sorted_mappings = []
    for header in csv_headers:
        lower_case_header = header.lower()
        mapping = mapping_dict[lower_case_header]
        sorted_mappings.append(mapping)

    #取込用マッピング情報を文字列形式に変換する
    sorted_mapping_info = ""
    for mapping in sorted_mappings:
        if sorted_mapping_info:
            sorted_mapping_info += ";"
        sorted_mapping_info += "{" + mapping + "}"

    #チェック対象(IFファイル)を初期化
    structTpyeSchema = StructType()

    #取込用マッピング情報よりチェック対象(L1)を作成
    sorted_mapping_info_arr = sorted_mapping_info.split(";")
    for i in sorted_mapping_info_arr:
        #チェック対象(IFファイル)の作成
        if ((i.strip('{}').split('\\')[3]).startswith("decimal")):
            #整数部
            precision_int = (i.strip('{}').split('\\')[3]).strip('decimal()').split(',')[0]
            #小数部
            scale_int = (i.strip('{}').split('\\')[3]).strip('decimal()').split(',')[1]
            structTpyeSchema.add(i.strip('{}').split('\\')[0], DecimalType(int(precision_int),int(scale_int)), True)
        else:
            structTpyeSchema.add(i.strip('{}').split('\\')[0], StringType(), True)

    structTpyeSchema.add("_corrupt_record", StringType(), True)

    ###データチェック###
    try:
        #データ抽出
        # ソースからデータ抽出(チェック用)
        options = {
            "header": True,
            "enforceSchema":False,
            "delimiter": delimiter_value,
            "quote": quote_value,
            "escape": escape_value,
            "ignoreLeadingWhiteSpace": True,
            "ignoreTrailingWhiteSpace": True,
            "multiLine": True,
            "nullValue": "",
            "schema": structTpyeSchema,
            "columnNameOfCorruptRecord": "_corrupt_record"
        }
        data_frame = spark.read.format("csv").load(file_path, **options)
    except:
        #データ抽出エラー
        if if_group:
            logger.error("E_DWH_JB_FILE_LOAD_004",msg_values=(if_group, if_id, file_path))
        else:
            logger.error("E_DWH_JB_FILE_LOAD_001",msg_values=(if_id, file_path))      
        raise ValueError("データ抽出エラー")
            
    # 65535バイト数チェック     
    for column_name, column_type in data_frame.dtypes:
        if column_type == "string":
            data_frame = data_frame.withColumn(
                column_name,
                udf(lambda value: (
                    None if value is None else (
                        value if len(value.encode("utf-8")) <= max_varchar_length else 
                        value.encode("utf-8")[:max_varchar_length].decode("utf-8", errors="ignore")
                    )
                ), StringType())(col(column_name))
            )

    #必須チェック
    data_frame_primary = data_frame
    for pk_key in primary_key_info:
        data_frame_primary = data_frame_primary.withColumn(f"{pk_key}_check", when(col(pk_key).isNull() | (col(pk_key) == ""), lit("必須項目NULL")).otherwise(lit(None)))
    check_columns = [col(f"{pk}_check") for pk in primary_key_info]
    data_frame_primary = data_frame_primary.withColumn("_corrupt_record", when(concat_ws("|", *check_columns) != "", concat_ws("|", *check_columns)).otherwise(None))
    #不正データ集計
    corrupt_records_count_primary = data_frame_primary.filter(data_frame_primary["_corrupt_record"].isNotNull()).count()
    if corrupt_records_count_primary > 0:
        #不正データログ出力
        if if_group:
            logger.error("E_DWH_JB_FILE_LOAD_005",msg_values=(if_group, if_id, f"入力データ不正. 不正データ件数：{corrupt_records_count_primary}件. 不正データサンプル抽出(最大10件)"))
        else:
            logger.error("E_DWH_JB_FILE_LOAD_002",msg_values=(if_id, f"入力データ不正. 不正データ件数：{corrupt_records_count_primary}件. 不正データサンプル抽出(最大10件)"))
        data_frame.filter(data_frame._corrupt_record.isNotNull()).show(10,truncate=False)
        raise ValueError("入力データ不正")

    #項目妥当性チェック
    data_frame.cache()
    #不正データ集計
    corrupt_records_count = data_frame.filter(data_frame["_corrupt_record"].isNotNull()).count()
    if corrupt_records_count > 0:
        #不正データログ出力
        if if_group:
            logger.error("E_DWH_JB_FILE_LOAD_005",msg_values=(if_group, if_id, f"入力データ不正. 不正データ件数：{corrupt_records_count}件. 不正データサンプル抽出(最大10件)"))
        else:
            logger.error("E_DWH_JB_FILE_LOAD_002",msg_values=(if_id, f"入力データ不正. 不正データ件数：{corrupt_records_count}件. 不正データサンプル抽出(最大10件)"))
        data_frame.filter(data_frame._corrupt_record.isNotNull()).show(10,truncate=False)
        raise ValueError("入力データ不正")

    ###データ加工###
    data_frame = data_frame.withColumn("dwh_created_user", lit(dwh_created_user))
    data_frame = data_frame.withColumn("dwh_created_datetime", from_utc_timestamp(current_timestamp(), "Asia/Tokyo"))
    data_frame = data_frame.withColumn("dwh_updated_user", lit(dwh_updated_datetime))
    data_frame = data_frame.withColumn("dwh_updated_datetime", from_utc_timestamp(current_timestamp(), "Asia/Tokyo"))

    return data_frame

#受信ファイルのバックアップ&削除共通処理
def file_backup_and_delete(if_id, file_path):
        
    s3_client = boto3.client('s3')
    key = file_path.replace("s3://" + s3_bucket_name_prefix + "/" + s3_bucket_name_suffix_copy_from, "")

    #IF受信ファイルバックアップ処理
    try:
        s3_client.copy_object(
        Bucket=s3_bucket_name_prefix,
        CopySource={'Bucket': s3_bucket_name_prefix, 'Key': s3_bucket_name_suffix_copy_from + key},
        Key=s3_bucket_name_suffix_copy_to + key)
    except Exception as e:
        logger.error("E_DWH_JB_FILE_LOAD_012",msg_values=(if_id))
        raise IOError("IF受信ファイルバックアップ処理が異常終了")
    logger.info("I_DWH_JB_FILE_LOAD_008",msg_values=(if_id))

    #IF受信ファイル削除処理
    try:
        s3_client.delete_object(Bucket=s3_bucket_name_prefix, Key=s3_bucket_name_suffix_copy_from + key)
    except Exception as e:
        logger.error("E_DWH_JB_FILE_LOAD_013",msg_values=(if_id))
        raise IOError("IF受信ファイル削除処理が異常終了")
    logger.info("I_DWH_JB_FILE_LOAD_009",msg_values=(if_id))

#処理開始

#ファイル存在チェックからパラメータ取得できない場合
if len(ids_files) == 0:
    raise Exception("「ファイル存在チェック」からパラメータを取得できません。")
#単一IDの場合
elif len(ids_files) == 1:

    #コンフィグファイル用検査キーを取得
    job_config_key = ids_files[0]['if_ids']
 
    try:

        #インタフェースIDを取得
        if_id = common.get_config_value(job_config_key, "IF_ID")
        #ジョブネットIDを取得
        jobnet_id = common.get_config_value(job_config_key, "JOBNET_ID")
        #連携単位を設定
        if_group = ""
        #スキーマ名を取得
        schema_name = common.get_config_value(job_config_key, "SCHEMA_NAME")
        #テーブル名を取得
        table_name = common.get_config_value(job_config_key, "TABLE_NAME")  
        #マッピング内容を取得
        mapping_info = common.get_config_value(job_config_key, "MAPPING_INFO")
        #必須項目を取得
        primary_key = common.get_config_value(job_config_key, "PRIMARY_KEY")

        #ファイルパス取得
        file_path = ids_files[0]['if_files']

        #開始処理
        logger = GlueLogger(jobnet_id)
        logger.info("I_DWH_JB_FILE_LOAD_001",msg_values=(if_id))     

        #データ取得&加工&チェック共通処理
        data_frame = data_check(if_id, if_group, file_path, mapping_info, primary_key)

        # DataFrameからDynamicFrameに変換
        data_frame_2 = data_frame.drop("_corrupt_record")
        AmazonS3_CSV = DynamicFrame.fromDF(data_frame_2, glueContext, 'AmazonS3_CSV')

        #登録前SQL作成
        preactions_sql = "TRUNCATE TABLE " + schema_name + "." + table_name + "_work" + "; "
        #登録後SQL作成
        postactions_sql = common.get_sql(job_config_key, "postaction")
        #登録テーブル設定
        dbtable = schema_name + "." + table_name + "_work"

        #データロード
        try:
            # Redshiftにロード
            AmazonRedshift_TABLE = glueContext.write_dynamic_frame.from_options(
                frame=AmazonS3_CSV,
                connection_type="redshift",
                connection_options={
                    "connectionName": redshift_connection_glue,
                    "redshiftTmpDir": redshift_tempdir,
                    "useConnectionProperties": "true",
                    "dbtable": dbtable,
                    "preactions": preactions_sql,
                    "postactions": postactions_sql
                },
                transformation_ctx="AmazonRedshift_TABLE",
            )
        except:
            #Redshiftへのデータロードが異常終了の場合、エラーログを出力して、異常終了する。
            logger.error("E_DWH_JB_FILE_LOAD_003",msg_values=(if_id))
            raise RuntimeError("Redshiftにデータロードが異常終了")

        #Redshiftへのデータロードが正常終了の場合、ログを出力して、正常終了する。
        logger.info("I_DWH_JB_FILE_LOAD_002",msg_values=(if_id))

        #受信ファイルのバックアップ&削除共通処理
        file_backup_and_delete(if_id, file_path)

    except Exception as e:
        #例外処理
        logger.error("E_DWH_JB_FILE_LOAD_008",msg_values=(if_id, str(e)))
        logger.error("E_DWH_JB_FILE_LOAD_009",msg_values=(if_id))
        raise

    job.commit()
    #終了ログ
    logger.info("I_DWH_JB_FILE_LOAD_003",msg_values=(if_id))

#複数IF
else:

    for id_file in ids_files:
        #コンフィグファイル用検査キーを取得
        job_config_key = id_file['if_ids']
 
        try:
            #インタフェースIDを取得
            if_id = common.get_config_value(job_config_key, "IF_ID")
            #ジョブネットIDを取得
            jobnet_id = common.get_config_value(job_config_key, "JOBNET_ID")
            #連携単位を取得
            if_group = common.get_config_value(job_config_key, "IF_GROUP")
            #スキーマ名を取得
            schema_name = common.get_config_value(job_config_key, "SCHEMA_NAME")
            #テーブル名を取得
            table_name = common.get_config_value(job_config_key, "TABLE_NAME")
            #マッピング内容を取得
            mapping_info = common.get_config_value(job_config_key, "MAPPING_INFO")
            #必須項目を取得
            primary_key = common.get_config_value(job_config_key, "PRIMARY_KEY")

            #ファイルパス取得
            file_path = id_file['if_files']
            
            #開始処理
            logger = GlueLogger(jobnet_id)
            logger.info("I_DWH_JB_FILE_LOAD_004",msg_values=(if_group, if_id))

            #データ取得&加工&チェック共通処理
            data_frame = data_check(if_id, if_group, file_path, mapping_info, primary_key)

            # DataFrameからDynamicFrameに変換
            data_frame_2 = data_frame.drop("_corrupt_record")
            AmazonS3_CSV = DynamicFrame.fromDF(data_frame_2, glueContext, 'AmazonS3_CSV')

            #登録前SQL文作成
            preactions_sql = "TRUNCATE TABLE " + schema_name + "." + table_name + "_work" + "; "
            #登録後SQL作成
            postactions_sql = ""
            #登録テーブル設定
            dbtable = schema_name + "." + table_name + "_work"

            #データロード(仮)
            try:
                # Redshiftにロード(仮テーブル)
                AmazonRedshift_TABLE = glueContext.write_dynamic_frame.from_options(
                    frame=AmazonS3_CSV,
                    connection_type="redshift",
                    connection_options={
                        "connectionName": redshift_connection_glue,
                        "redshiftTmpDir": redshift_tempdir,
                        "useConnectionProperties": "true",
                        "dbtable": dbtable,
                        "preactions": preactions_sql,
                        "postactions": postactions_sql
                    },
                    transformation_ctx="AmazonRedshift_TABLE",
                )
            except:
                #Redshiftへのデータロードが異常終了の場合、エラーログを出力して、異常終了する。
                logger.error("E_DWH_JB_FILE_LOAD_006",msg_values=(if_group, if_id))
                raise RuntimeError("Redshiftにデータロードが異常終了")
            #Redshiftへのデータロードが正常終了の場合、ログを出力して、正常終了する。
            logger.info("I_DWH_JB_FILE_LOAD_005",msg_values=(if_group, if_id))

        except Exception as e:
            #例外処理
            logger.error("E_DWH_JB_FILE_LOAD_010",msg_values=(if_group, if_id, str(e)))
            logger.error("E_DWH_JB_FILE_LOAD_011",msg_values=(if_group, if_id))
            raise

    #Redshiftにロード(本テーブル)
    try:

        if_ids_str = ""
        if_group = common.get_config_value(ids_files[0]['if_ids'], "IF_GROUP")
        for id_file in ids_files:
            if_ids_str += id_file['if_ids'] + ", "

        #コネクション開始
        conn = redshift_connector.connect(
            host=redshift_host,
            database=redshift_db,
            user=redshift_user,
            password=redshift_password
        )
        #自動コミット
        #conn.autocommit = True
        cursor = conn.cursor()

        try:
            #SQL実行
            cursor.execute("BEGIN;")
            for id_file in ids_files:
                cursor.execute(common.get_sql(id_file['if_ids'], "postaction"))
            cursor.execute("COMMIT;")
            cursor.execute("END;")
        except Exception as e:
            logger.error("E_DWH_JB_FILE_LOAD_007",msg_values=(if_group, if_ids_str, f"エラー内容: {str(e)}"))
            raise e
        finally:
            cursor.close()
            conn.close()

        logger.info("I_DWH_JB_FILE_LOAD_006",msg_values=(if_group, if_ids_str))
    except Exception as e:
        #例外処理
        logger.error("E_DWH_JB_FILE_LOAD_010",msg_values=(if_group, if_ids_str, str(e)))
        logger.error("E_DWH_JB_FILE_LOAD_011",msg_values=(if_group, if_ids_str))
        raise
        
    for id_file in ids_files:

        try:
            #インタフェースIDを取得
            if_id = id_file['if_ids']
            #ファイルパス取得
            file_path = id_file['if_files']

            #受信ファイルのバックアップ&削除共通処理
            file_backup_and_delete(if_id, file_path)

        except Exception as e:
            #例外処理
            logger.error("E_DWH_JB_FILE_LOAD_010",msg_values=(if_group, if_id, str(e)))
            logger.error("E_DWH_JB_FILE_LOAD_011",msg_values=(if_group, if_id))
            raise e

    job.commit()
    #終了ログ
    for id_file in ids_files:
        logger.info("I_DWH_JB_FILE_LOAD_007",msg_values=(common.get_config_value(id_file['if_ids'], "IF_GROUP"), id_file['if_ids']))
