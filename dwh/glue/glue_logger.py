#!/usr/bin/env python
# -*- coding: utf-8 -*-

import boto3
import configparser
import datetime
import os
import logging
from typing import Optional, Tuple, Any
from configparser import ConfigParser


class GlueLogger:
    """Glueジョブ用ログ出力クラス"""

    # ログレベル定数（pythonの標準ロギングレベルに合わせる）
    ERROR = logging.ERROR  # 40
    WARN = logging.WARNING  # 30
    INFO = logging.INFO  # 20
    DEBUG = logging.DEBUG  # 10
    TRACE = logging.DEBUG - 5  # 5 （独自定義）

    def __init__(self, jobnet_id: str):
        """
        初期化処理
        Args:
            jobnet_id: ジョブネットID
        """
        self.jobnet_id = jobnet_id

        # 基本ログフォーマット
        self.msg_format = "[%s][%s][%s][%s]%s"  # [日時][ログレベル][ジョブネットID][メッセージID]メッセージ内容
        self.static_msg_format = "[%s][%s][%s]%s"  # メッセージIDなしフォーマット

        # ログレベルの設定
        log_level = os.environ.get("LOG_LEVEL", "INFO")
        self.log_level = getattr(logging, log_level.upper(), logging.INFO)

        # ログメッセージの読み込み
        try:
            #AWS サービスのS3_Clientインスタンス作成処理
            self.__s3_client = self.create_aws_client("s3")
            #AWS サービスのSSM_Clientインスタンス作成処理
            self.__ssm_client = self.create_aws_client("ssm")
            #パラメータストアの取得
            self.s3BucketNameGlue = self.__ssm_client.get_parameter(Name='S3_BUCKET_NAME_GLUE')['Parameter']['Value']
            #ログconfigファイルの取得
            self.msg_file = configparser.ConfigParser()
            msg_file_obj = self.__s3_client.get_object(Bucket=self.s3BucketNameGlue, Key='scripts/dwh/config/log_message.config')
            self.msg_file.read_string(msg_file_obj["Body"].read().decode("utf-8"))
            
            #response = self.__s3_client.get_object(Bucket=self.s3BucketNameGlue, Key='scripts/dwh/config/log_message.config')
            #config_data = response["Body"].read().decode("utf-8")
            #config = configparser.ConfigParser()
            #config.read_string(config_data)
            #self.msg_file = config
            
        except Exception as e:
            print(f"Warning: Failed to load log message config: {e}")
            self.msg_file = configparser.ConfigParser()

    def create_aws_client(self, service_name):
        """
        Method:
            AWS サービスのClientインスタンス作成処理
        Args:
            service_name: サービス名
        Return:
            AWS サービスのClientインスタンス
        """
        return boto3.client(service_name)

    def get_log_time(self) -> str:
        """
        ログ出力時間を取得
        Returns:
            str: フォーマット済み日時文字列
        """
        return datetime.datetime.now().strftime("%Y/%m/%d %H:%M:%S")

    def _format_message(self, level: str, msg_id: Optional[str], message: str) -> str:
        """
        ログメッセージをフォーマット
        Args:
            level: ログレベル文字列
            msg_id: メッセージID（省略可）
            message: メッセージ内容
        Returns:
            str: フォーマット済みメッセージ
        """
        if msg_id:
            return self.msg_format % (
                self.get_log_time(),
                level,
                self.jobnet_id,
                msg_id,
                message,
            )
        else:
            return self.static_msg_format % (
                self.get_log_time(),
                level,
                self.jobnet_id,
                message,
            )

    def _get_message_content(
        self, section: str, msg_id: str, values: Optional[Tuple[Any, ...]] = None
    ) -> str:
        """
        メッセージ内容を取得
        Args:
            section: メッセージセクション
            msg_id: メッセージID
            values: メッセージパラメータ（省略可）
        Returns:
            str: メッセージ内容
        """
        try:
            message = self.msg_file.get(section, msg_id)
            if values:
                message = message % values
            return message
        except Exception as e:
            print("Message not found for {msg_id}: {str(e)}")
            raise e

    def loggerByMsgId(self, msg_id: str, msg_values: Optional[Tuple[Any, ...]] = None):
        """
        メッセージIDによるログ出力
        Args:
            msg_id: メッセージID
            msg_values: メッセージパラメータ（省略可）
        """
        if not msg_id:
            return

        if msg_id[0] == "T" and self.log_level <= self.TRACE:
            self.trace(msg_id)
        elif msg_id[0] == "D" and self.log_level <= self.DEBUG:
            self.debug(msg_id)
        elif msg_id[0] == "I" and self.log_level <= self.INFO:
            self.info(msg_id, msg_values)
        elif msg_id[0] == "W" and self.log_level <= self.WARN:
            self.warning(msg_id, msg_values)
        elif msg_id[0] == "E" and self.log_level <= self.ERROR:
            self.error(msg_id, msg_values)

    def trace(self, msg: str):
        """
        TRACEログを出力
        Args:
            msg: メッセージ内容
        """
        if self.log_level <= self.TRACE:
            print(self._format_message("TRACE", None, msg))

    def debug(self, msg: str):
        """
        DEBUGログを出力
        Args:
            msg: メッセージ内容
        """
        if self.log_level <= self.DEBUG:
            print(self._format_message("DEBUG", None, msg))

    def info(self, msg_id: str, msg_values: Optional[Tuple[Any, ...]] = None):
        """
        INFOログを出力
        Args:
            msg_id: メッセージID
            msg_values: メッセージパラメータ（省略可）
        """
        if self.log_level <= self.INFO:
            message = self._get_message_content("INFO", msg_id, msg_values)
            print(self._format_message("INFO", msg_id, message))

    def warning(self, msg_id: str, msg_values: Optional[Tuple[Any, ...]] = None):
        """
        WARNログを出力
        Args:
            msg_id: メッセージID
            msg_values: メッセージパラメータ（省略可）
        """
        if self.log_level <= self.WARN:
            message = self._get_message_content("WARN", msg_id, msg_values)
            print(self._format_message("WARN", msg_id, message))

    def error(self, msg_id: str, msg_values: Optional[Tuple[Any, ...]] = None):
        """
        ERRORログを出力
        Args:
            msg_id: メッセージID
            msg_values: メッセージパラメータ（省略可）
        """
        if self.log_level <= self.ERROR:
            message = self._get_message_content("ERROR", msg_id, msg_values)
            print(self._format_message("ERROR", msg_id, message))

    def error_common(self, msg: str):
        """
        共通ERRORログを出力（メッセージIDなし）
        Args:
            msg: メッセージ内容
        """
        if self.log_level <= self.ERROR:
            print(self._format_message("ERROR", None, msg))

    def exception(self, exception: Exception):
        """
        例外ログを出力
        Args:
            exception: 例外オブジェクト
        """
        if self.log_level <= self.ERROR and exception.args:
            print(self._format_message("ERROR", None, str(exception)))

    def isDebug(self) -> bool:
        """
        デバッグモードか判定
        Returns:
            bool: デバッグモードの場合True
        """
        return self.log_level <= self.DEBUG
