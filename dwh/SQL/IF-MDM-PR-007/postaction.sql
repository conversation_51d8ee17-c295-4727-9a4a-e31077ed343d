MERGE INTO dwh_raw.product_segment USING dwh_raw.product_segment_work
ON product_segment.product_segment = product_segment_work.product_segment
WHEN MATCHED THEN UPDATE SET
product_segment_name = product_segment_work.product_segment_name,
product_segment_nk = product_segment_work.product_segment_nk,
insert_date = CAST(NULLIF(product_segment_work.insert_date, '') AS TIMESTAMP),
insert_id = product_segment_work.insert_id,
modify_date = CAST(NULLIF(product_segment_work.modify_date, '') AS TIMESTAMP),
modify_id = product_segment_work.modify_id,
dwh_updated_user = product_segment_work.dwh_updated_user,
dwh_updated_datetime = product_segment_work.dwh_updated_datetime
WHEN NOT MATCHED THEN INSERT VALUES (
product_segment_work.product_segment,
product_segment_work.product_segment_name,
product_segment_work.product_segment_nk,
CAST(NULLIF(product_segment_work.insert_date, '') AS TIMESTAMP),
product_segment_work.insert_id,
CAST(NULLIF(product_segment_work.modify_date, '') AS TIMESTAMP),
product_segment_work.modify_id,
product_segment_work.dwh_created_user,
product_segment_work.dwh_created_datetime,
product_segment_work.dwh_updated_user,
product_segment_work.dwh_updated_datetime
);