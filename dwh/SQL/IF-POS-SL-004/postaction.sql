MERGE INTO dwh_raw.sales_info_alignment_detail USING dwh_raw.sales_info_alignment_detail_work
ON sales_info_alignment_detail.shop_cd = sales_info_alignment_detail_work.shop_cd AND
 sales_info_alignment_detail.register_num = sales_info_alignment_detail_work.register_num AND
 sales_info_alignment_detail.business_date = sales_info_alignment_detail_work.business_date AND
 sales_info_alignment_detail.receipt_num = sales_info_alignment_detail_work.receipt_num AND
 sales_info_alignment_detail.line_num = sales_info_alignment_detail_work.line_num
WHEN MATCHED THEN UPDATE SET
line_kind = sales_info_alignment_detail_work.line_kind,
is_in_store_marking = sales_info_alignment_detail_work.is_in_store_marking,
brand_cd = sales_info_alignment_detail_work.brand_cd,
dept_cd = sales_info_alignment_detail_work.dept_cd,
class_cd = sales_info_alignment_detail_work.class_cd,
sub_class_cd = sales_info_alignment_detail_work.sub_class_cd,
item_cd = sales_info_alignment_detail_work.item_cd,
item_name = sales_info_alignment_detail_work.item_name,
dept_group_cd = sales_info_alignment_detail_work.dept_group_cd,
parent_item_cd = sales_info_alignment_detail_work.parent_item_cd,
jan_cd = sales_info_alignment_detail_work.jan_cd,
item_num = sales_info_alignment_detail_work.item_num,
color_cd = sales_info_alignment_detail_work.color_cd,
size_cd = sales_info_alignment_detail_work.size_cd,
year_cd = sales_info_alignment_detail_work.year_cd,
season_cd = sales_info_alignment_detail_work.season_cd,
attribute_cd1 = sales_info_alignment_detail_work.attribute_cd1,
attribute_cd2 = sales_info_alignment_detail_work.attribute_cd2,
attribute_cd3 = sales_info_alignment_detail_work.attribute_cd3,
attribute_cd4 = sales_info_alignment_detail_work.attribute_cd4,
attribute_cd5 = sales_info_alignment_detail_work.attribute_cd5,
scan_bar_cd1 = sales_info_alignment_detail_work.scan_bar_cd1,
scan_bar_cd2 = sales_info_alignment_detail_work.scan_bar_cd2,
discount_cd = sales_info_alignment_detail_work.discount_cd,
bmplan_cd = sales_info_alignment_detail_work.bmplan_cd,
coupon_num = sales_info_alignment_detail_work.coupon_num,
is_include_sales = sales_info_alignment_detail_work.is_include_sales,
item_kind = sales_info_alignment_detail_work.item_kind,
tax_kind = sales_info_alignment_detail_work.tax_kind,
tax_rate = sales_info_alignment_detail_work.tax_rate,
is_reduced_tax_rate = sales_info_alignment_detail_work.is_reduced_tax_rate,
is_proper_item = sales_info_alignment_detail_work.is_proper_item,
is_change_price = sales_info_alignment_detail_work.is_change_price,
master_unit_price = sales_info_alignment_detail_work.master_unit_price,
body_price = sales_info_alignment_detail_work.body_price,
fixed_price = sales_info_alignment_detail_work.fixed_price,
unit_price = sales_info_alignment_detail_work.unit_price,
quantity = sales_info_alignment_detail_work.quantity,
amount = sales_info_alignment_detail_work.amount,
tax_inclusive = sales_info_alignment_detail_work.tax_inclusive,
tax_outside = sales_info_alignment_detail_work.tax_outside,
tax = sales_info_alignment_detail_work.tax,
disc_rate = sales_info_alignment_detail_work.disc_rate,
line_minus_amount = sales_info_alignment_detail_work.line_minus_amount,
line_minus_tax_inclusive = sales_info_alignment_detail_work.line_minus_tax_inclusive,
bmset_minus_amount = sales_info_alignment_detail_work.bmset_minus_amount,
bmset_minus_tax_inclusive = sales_info_alignment_detail_work.bmset_minus_tax_inclusive,
point_minus_amount = sales_info_alignment_detail_work.point_minus_amount,
point_minus_tax_inclusive = sales_info_alignment_detail_work.point_minus_tax_inclusive,
coupon_minus_amount = sales_info_alignment_detail_work.coupon_minus_amount,
coupon_minus_tax_inclusive = sales_info_alignment_detail_work.coupon_minus_tax_inclusive,
sub_total_minus_amount = sales_info_alignment_detail_work.sub_total_minus_amount,
sub_total_minus_tax_inclusive = sales_info_alignment_detail_work.sub_total_minus_tax_inclusive,
before_disc_tax_inclusive = sales_info_alignment_detail_work.before_disc_tax_inclusive,
sales_man_cd = sales_info_alignment_detail_work.sales_man_cd,
stock_kind = sales_info_alignment_detail_work.stock_kind,
is_inventory_counted = sales_info_alignment_detail_work.is_inventory_counted,
is_promotion_ticket = sales_info_alignment_detail_work.is_promotion_ticket,
promotion_ticket_bar_cd = sales_info_alignment_detail_work.promotion_ticket_bar_cd,
is_promotion_ticket_allow_combination = sales_info_alignment_detail_work.is_promotion_ticket_allow_combination,
oes_item_flag = sales_info_alignment_detail_work.oes_item_flag,
oes_slip_no = sales_info_alignment_detail_work.oes_slip_no,
oes_slip_sub_no = sales_info_alignment_detail_work.oes_slip_sub_no,
grand_classification = sales_info_alignment_detail_work.grand_classification,
menu_classification = sales_info_alignment_detail_work.menu_classification,
oes_line_num = sales_info_alignment_detail_work.oes_line_num,
oes_quantity = sales_info_alignment_detail_work.oes_quantity,
oes_minus = sales_info_alignment_detail_work.oes_minus,
pos_minus = sales_info_alignment_detail_work.pos_minus,
takeout_flag = sales_info_alignment_detail_work.takeout_flag,
service_charge_minus_amount = sales_info_alignment_detail_work.service_charge_minus_amount,
service_charge_minus_tax_inclusive = sales_info_alignment_detail_work.service_charge_minus_tax_inclusive,
service_charge_flag = sales_info_alignment_detail_work.service_charge_flag,
service_charge1_flag = sales_info_alignment_detail_work.service_charge1_flag,
service_charge2_flag = sales_info_alignment_detail_work.service_charge2_flag,
service_charge1_manually_flag = sales_info_alignment_detail_work.service_charge1_manually_flag,
service_charge2_manually_flag = sales_info_alignment_detail_work.service_charge2_manually_flag,
oes_service_charge1 = sales_info_alignment_detail_work.oes_service_charge1,
oes_service_charge2 = sales_info_alignment_detail_work.oes_service_charge2,
cooking_directions_time = sales_info_alignment_detail_work.cooking_directions_time,
cooking_complete_time = sales_info_alignment_detail_work.cooking_complete_time,
offer_complete_time = sales_info_alignment_detail_work.offer_complete_time,
acceptance_time = CAST(NULLIF(sales_info_alignment_detail_work.acceptance_time, '') AS TIMESTAMP),
menu_cook_cmp_time = CAST(NULLIF(sales_info_alignment_detail_work.menu_cook_cmp_time, '') AS TIMESTAMP),
menu_offer_cmp_time = CAST(NULLIF(sales_info_alignment_detail_work.menu_offer_cmp_time, '') AS TIMESTAMP),
disc_amount_limit = sales_info_alignment_detail_work.disc_amount_limit,
is_follow_disc = sales_info_alignment_detail_work.is_follow_disc,
is_allow_credit = sales_info_alignment_detail_work.is_allow_credit,
is_employee_acnt_recv = sales_info_alignment_detail_work.is_employee_acnt_recv,
employee_cd = sales_info_alignment_detail_work.employee_cd,
sub_menu_kind = sales_info_alignment_detail_work.sub_menu_kind,
grand_menu_code = sales_info_alignment_detail_work.grand_menu_code,
grand_menu_index = sales_info_alignment_detail_work.grand_menu_index,
select_kind = sales_info_alignment_detail_work.select_kind,
is_quantity_count = sales_info_alignment_detail_work.is_quantity_count,
is_order_entry = sales_info_alignment_detail_work.is_order_entry,
modifier_kind = sales_info_alignment_detail_work.modifier_kind,
return_target_line_num = sales_info_alignment_detail_work.return_target_line_num,
numeric_reserve1 = sales_info_alignment_detail_work.numeric_reserve1,
numeric_reserve2 = sales_info_alignment_detail_work.numeric_reserve2,
numeric_reserve3 = sales_info_alignment_detail_work.numeric_reserve3,
numeric_reserve4 = sales_info_alignment_detail_work.numeric_reserve4,
numeric_reserve5 = sales_info_alignment_detail_work.numeric_reserve5,
numeric_reserve6 = sales_info_alignment_detail_work.numeric_reserve6,
numeric_reserve7 = sales_info_alignment_detail_work.numeric_reserve7,
numeric_reserve8 = sales_info_alignment_detail_work.numeric_reserve8,
numeric_reserve9 = sales_info_alignment_detail_work.numeric_reserve9,
numeric_reserve10 = sales_info_alignment_detail_work.numeric_reserve10,
string_reserve1 = sales_info_alignment_detail_work.string_reserve1,
string_reserve2 = sales_info_alignment_detail_work.string_reserve2,
string_reserve3 = sales_info_alignment_detail_work.string_reserve3,
string_reserve4 = sales_info_alignment_detail_work.string_reserve4,
string_reserve5 = sales_info_alignment_detail_work.string_reserve5,
string_reserve6 = sales_info_alignment_detail_work.string_reserve6,
string_reserve7 = sales_info_alignment_detail_work.string_reserve7,
string_reserve8 = sales_info_alignment_detail_work.string_reserve8,
string_reserve9 = sales_info_alignment_detail_work.string_reserve9,
string_reserve10 = sales_info_alignment_detail_work.string_reserve10,
dwh_updated_user = sales_info_alignment_detail_work.dwh_updated_user,
dwh_updated_datetime = sales_info_alignment_detail_work.dwh_updated_datetime
WHEN NOT MATCHED THEN INSERT VALUES (
sales_info_alignment_detail_work.shop_cd,
sales_info_alignment_detail_work.register_num,
CAST(NULLIF(sales_info_alignment_detail_work.business_date, '') AS DATE),
sales_info_alignment_detail_work.receipt_num,
sales_info_alignment_detail_work.line_num,
sales_info_alignment_detail_work.line_kind,
sales_info_alignment_detail_work.is_in_store_marking,
sales_info_alignment_detail_work.brand_cd,
sales_info_alignment_detail_work.dept_cd,
sales_info_alignment_detail_work.class_cd,
sales_info_alignment_detail_work.sub_class_cd,
sales_info_alignment_detail_work.item_cd,
sales_info_alignment_detail_work.item_name,
sales_info_alignment_detail_work.dept_group_cd,
sales_info_alignment_detail_work.parent_item_cd,
sales_info_alignment_detail_work.jan_cd,
sales_info_alignment_detail_work.item_num,
sales_info_alignment_detail_work.color_cd,
sales_info_alignment_detail_work.size_cd,
sales_info_alignment_detail_work.year_cd,
sales_info_alignment_detail_work.season_cd,
sales_info_alignment_detail_work.attribute_cd1,
sales_info_alignment_detail_work.attribute_cd2,
sales_info_alignment_detail_work.attribute_cd3,
sales_info_alignment_detail_work.attribute_cd4,
sales_info_alignment_detail_work.attribute_cd5,
sales_info_alignment_detail_work.scan_bar_cd1,
sales_info_alignment_detail_work.scan_bar_cd2,
sales_info_alignment_detail_work.discount_cd,
sales_info_alignment_detail_work.bmplan_cd,
sales_info_alignment_detail_work.coupon_num,
sales_info_alignment_detail_work.is_include_sales,
sales_info_alignment_detail_work.item_kind,
sales_info_alignment_detail_work.tax_kind,
sales_info_alignment_detail_work.tax_rate,
sales_info_alignment_detail_work.is_reduced_tax_rate,
sales_info_alignment_detail_work.is_proper_item,
sales_info_alignment_detail_work.is_change_price,
sales_info_alignment_detail_work.master_unit_price,
sales_info_alignment_detail_work.body_price,
sales_info_alignment_detail_work.fixed_price,
sales_info_alignment_detail_work.unit_price,
sales_info_alignment_detail_work.quantity,
sales_info_alignment_detail_work.amount,
sales_info_alignment_detail_work.tax_inclusive,
sales_info_alignment_detail_work.tax_outside,
sales_info_alignment_detail_work.tax,
sales_info_alignment_detail_work.disc_rate,
sales_info_alignment_detail_work.line_minus_amount,
sales_info_alignment_detail_work.line_minus_tax_inclusive,
sales_info_alignment_detail_work.bmset_minus_amount,
sales_info_alignment_detail_work.bmset_minus_tax_inclusive,
sales_info_alignment_detail_work.point_minus_amount,
sales_info_alignment_detail_work.point_minus_tax_inclusive,
sales_info_alignment_detail_work.coupon_minus_amount,
sales_info_alignment_detail_work.coupon_minus_tax_inclusive,
sales_info_alignment_detail_work.sub_total_minus_amount,
sales_info_alignment_detail_work.sub_total_minus_tax_inclusive,
sales_info_alignment_detail_work.before_disc_tax_inclusive,
sales_info_alignment_detail_work.sales_man_cd,
sales_info_alignment_detail_work.stock_kind,
sales_info_alignment_detail_work.is_inventory_counted,
sales_info_alignment_detail_work.is_promotion_ticket,
sales_info_alignment_detail_work.promotion_ticket_bar_cd,
sales_info_alignment_detail_work.is_promotion_ticket_allow_combination,
sales_info_alignment_detail_work.oes_item_flag,
sales_info_alignment_detail_work.oes_slip_no,
sales_info_alignment_detail_work.oes_slip_sub_no,
sales_info_alignment_detail_work.grand_classification,
sales_info_alignment_detail_work.menu_classification,
sales_info_alignment_detail_work.oes_line_num,
sales_info_alignment_detail_work.oes_quantity,
sales_info_alignment_detail_work.oes_minus,
sales_info_alignment_detail_work.pos_minus,
sales_info_alignment_detail_work.takeout_flag,
sales_info_alignment_detail_work.service_charge_minus_amount,
sales_info_alignment_detail_work.service_charge_minus_tax_inclusive,
sales_info_alignment_detail_work.service_charge_flag,
sales_info_alignment_detail_work.service_charge1_flag,
sales_info_alignment_detail_work.service_charge2_flag,
sales_info_alignment_detail_work.service_charge1_manually_flag,
sales_info_alignment_detail_work.service_charge2_manually_flag,
sales_info_alignment_detail_work.oes_service_charge1,
sales_info_alignment_detail_work.oes_service_charge2,
sales_info_alignment_detail_work.cooking_directions_time,
sales_info_alignment_detail_work.cooking_complete_time,
sales_info_alignment_detail_work.offer_complete_time,
CAST(NULLIF(sales_info_alignment_detail_work.acceptance_time, '') AS TIMESTAMP),
CAST(NULLIF(sales_info_alignment_detail_work.menu_cook_cmp_time, '') AS TIMESTAMP),
CAST(NULLIF(sales_info_alignment_detail_work.menu_offer_cmp_time, '') AS TIMESTAMP),
sales_info_alignment_detail_work.disc_amount_limit,
sales_info_alignment_detail_work.is_follow_disc,
sales_info_alignment_detail_work.is_allow_credit,
sales_info_alignment_detail_work.is_employee_acnt_recv,
sales_info_alignment_detail_work.employee_cd,
sales_info_alignment_detail_work.sub_menu_kind,
sales_info_alignment_detail_work.grand_menu_code,
sales_info_alignment_detail_work.grand_menu_index,
sales_info_alignment_detail_work.select_kind,
sales_info_alignment_detail_work.is_quantity_count,
sales_info_alignment_detail_work.is_order_entry,
sales_info_alignment_detail_work.modifier_kind,
sales_info_alignment_detail_work.return_target_line_num,
sales_info_alignment_detail_work.numeric_reserve1,
sales_info_alignment_detail_work.numeric_reserve2,
sales_info_alignment_detail_work.numeric_reserve3,
sales_info_alignment_detail_work.numeric_reserve4,
sales_info_alignment_detail_work.numeric_reserve5,
sales_info_alignment_detail_work.numeric_reserve6,
sales_info_alignment_detail_work.numeric_reserve7,
sales_info_alignment_detail_work.numeric_reserve8,
sales_info_alignment_detail_work.numeric_reserve9,
sales_info_alignment_detail_work.numeric_reserve10,
sales_info_alignment_detail_work.string_reserve1,
sales_info_alignment_detail_work.string_reserve2,
sales_info_alignment_detail_work.string_reserve3,
sales_info_alignment_detail_work.string_reserve4,
sales_info_alignment_detail_work.string_reserve5,
sales_info_alignment_detail_work.string_reserve6,
sales_info_alignment_detail_work.string_reserve7,
sales_info_alignment_detail_work.string_reserve8,
sales_info_alignment_detail_work.string_reserve9,
sales_info_alignment_detail_work.string_reserve10,
sales_info_alignment_detail_work.dwh_created_user,
sales_info_alignment_detail_work.dwh_created_datetime,
sales_info_alignment_detail_work.dwh_updated_user,
sales_info_alignment_detail_work.dwh_updated_datetime
);