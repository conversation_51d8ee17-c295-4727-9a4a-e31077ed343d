MERGE INTO dwh_raw.out_achievements_mail_order USING dwh_raw.out_achievements_mail_order_work
ON out_achievements_mail_order.accept_no = out_achievements_mail_order_work.accept_no AND
 out_achievements_mail_order.close_date = out_achievements_mail_order_work.close_date AND
 out_achievements_mail_order.wh_code = out_achievements_mail_order_work.wh_code AND
 out_achievements_mail_order.goods_code = out_achievements_mail_order_work.goods_code
WHEN MATCHED THEN UPDATE SET
out_qty = out_achievements_mail_order_work.out_qty,
logimane_slip_no = out_achievements_mail_order_work.logimane_slip_no,
dwh_updated_user = out_achievements_mail_order_work.dwh_updated_user,
dwh_updated_datetime = out_achievements_mail_order_work.dwh_updated_datetime
WHEN NOT MATCHED THEN INSERT VALUES (
out_achievements_mail_order_work.accept_no,
CAST(NULLIF(out_achievements_mail_order_work.close_date, '') AS DATE),
out_achievements_mail_order_work.wh_code,
out_achievements_mail_order_work.goods_code,
out_achievements_mail_order_work.out_qty,
out_achievements_mail_order_work.logimane_slip_no,
out_achievements_mail_order_work.dwh_created_user,
out_achievements_mail_order_work.dwh_created_datetime,
out_achievements_mail_order_work.dwh_updated_user,
out_achievements_mail_order_work.dwh_updated_datetime
);