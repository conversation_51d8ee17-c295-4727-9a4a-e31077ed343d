MERGE INTO dwh_raw.campaign_order USING dwh_raw.campaign_order_work
ON campaign_order.campaign_instructions_code = campaign_order_work.campaign_instructions_code AND
 campaign_order.campaign_group_no = campaign_order_work.campaign_group_no AND
 campaign_order.joken_type = campaign_order_work.joken_type AND
 campaign_order.campaign_joken_no = campaign_order_work.campaign_joken_no
WHEN MATCHED THEN UPDATE SET
joken_kind1 = campaign_order_work.joken_kind1,
joken_kind2 = campaign_order_work.joken_kind2,
joken = campaign_order_work.joken,
joken_min = campaign_order_work.joken_min,
joken_max = campaign_order_work.joken_max,
regular_kaiji = campaign_order_work.regular_kaiji,
joken_month_num = campaign_order_work.joken_month_num,
commodity_name = campaign_order_work.commodity_name,
orm_rowid = campaign_order_work.orm_rowid,
created_user = campaign_order_work.created_user,
created_datetime = CAST(NULLIF(campaign_order_work.created_datetime, '') AS TIMESTAMP),
updated_user = campaign_order_work.updated_user,
updated_datetime = CAST(NULLIF(campaign_order_work.updated_datetime, '') AS TIMESTAMP),
dwh_updated_user = campaign_order_work.dwh_updated_user,
dwh_updated_datetime = campaign_order_work.dwh_updated_datetime
WHEN NOT MATCHED THEN INSERT VALUES (
campaign_order_work.campaign_instructions_code,
campaign_order_work.campaign_group_no,
campaign_order_work.joken_type,
campaign_order_work.campaign_joken_no,
campaign_order_work.joken_kind1,
campaign_order_work.joken_kind2,
campaign_order_work.joken,
campaign_order_work.joken_min,
campaign_order_work.joken_max,
campaign_order_work.regular_kaiji,
campaign_order_work.joken_month_num,
campaign_order_work.commodity_name,
campaign_order_work.orm_rowid,
campaign_order_work.created_user,
CAST(NULLIF(campaign_order_work.created_datetime, '') AS TIMESTAMP),
campaign_order_work.updated_user,
CAST(NULLIF(campaign_order_work.updated_datetime, '') AS TIMESTAMP),
campaign_order_work.dwh_created_user,
campaign_order_work.dwh_created_datetime,
campaign_order_work.dwh_updated_user,
campaign_order_work.dwh_updated_datetime
);