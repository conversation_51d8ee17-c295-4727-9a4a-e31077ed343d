MERGE INTO dwh_raw.campaign_order_group USING dwh_raw.campaign_order_group_work
ON campaign_order_group.campaign_instructions_code = campaign_order_group_work.campaign_instructions_code AND
 campaign_order_group.campaign_group_no = campaign_order_group_work.campaign_group_no
WHEN MATCHED THEN UPDATE SET
campaign_joken_disp = campaign_order_group_work.campaign_joken_disp,
exclude_joken_disp = campaign_order_group_work.exclude_joken_disp,
orm_rowid = campaign_order_group_work.orm_rowid,
created_user = campaign_order_group_work.created_user,
created_datetime = CAST(NULLIF(campaign_order_group_work.created_datetime, '') AS TIMESTAMP),
updated_user = campaign_order_group_work.updated_user,
updated_datetime = CAST(NULLIF(campaign_order_group_work.updated_datetime, '') AS TIMESTAMP),
dwh_updated_user = campaign_order_group_work.dwh_updated_user,
dwh_updated_datetime = campaign_order_group_work.dwh_updated_datetime
WHEN NOT MATCHED THEN INSERT VALUES (
campaign_order_group_work.campaign_instructions_code,
campaign_order_group_work.campaign_group_no,
campaign_order_group_work.campaign_joken_disp,
campaign_order_group_work.exclude_joken_disp,
campaign_order_group_work.orm_rowid,
campaign_order_group_work.created_user,
CAST(NULLIF(campaign_order_group_work.created_datetime, '') AS TIMESTAMP),
campaign_order_group_work.updated_user,
CAST(NULLIF(campaign_order_group_work.updated_datetime, '') AS TIMESTAMP),
campaign_order_group_work.dwh_created_user,
campaign_order_group_work.dwh_created_datetime,
campaign_order_group_work.dwh_updated_user,
campaign_order_group_work.dwh_updated_datetime
);