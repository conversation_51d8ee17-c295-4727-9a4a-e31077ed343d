MERGE INTO dwh_raw.dgroup USING dwh_raw.dgroup_work
ON dgroup.lgroup = dgroup_work.lgroup AND
 dgroup.mgroup = dgroup_work.mgroup AND
 dgroup.sgroup = dgroup_work.sgroup AND
 dgroup.dgroup = dgroup_work.dgroup
WHEN MATCHED THEN UPDATE SET
dgroup_name = dgroup_work.dgroup_name,
lgroup_nk = dgroup_work.lgroup_nk,
insert_date = CAST(NULLIF(dgroup_work.insert_date, '') AS TIMESTAMP),
insert_id = dgroup_work.insert_id,
modify_date = CAST(NULLIF(dgroup_work.modify_date, '') AS TIMESTAMP),
modify_id = dgroup_work.modify_id,
dwh_updated_user = dgroup_work.dwh_updated_user,
dwh_updated_datetime = dgroup_work.dwh_updated_datetime
WHEN NOT MATCHED THEN INSERT VALUES (
dgroup_work.lgroup,
dgroup_work.mgroup,
dgroup_work.sgroup,
dgroup_work.dgroup,
dgroup_work.dgroup_name,
dgroup_work.lgroup_nk,
CAST(NULLIF(dgroup_work.insert_date, '') AS TIMESTAMP),
dgroup_work.insert_id,
CAST(NULLIF(dgroup_work.modify_date, '') AS TIMESTAMP),
dgroup_work.modify_id,
dgroup_work.dwh_created_user,
dgroup_work.dwh_created_datetime,
dgroup_work.dwh_updated_user,
dgroup_work.dwh_updated_datetime
);