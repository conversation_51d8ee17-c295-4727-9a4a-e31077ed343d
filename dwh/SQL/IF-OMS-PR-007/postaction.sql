MERGE INTO dwh_raw.regular_sale_composition USING dwh_raw.regular_sale_composition_work
ON regular_sale_composition.shop_code = regular_sale_composition_work.shop_code AND
 regular_sale_composition.regular_sale_code = regular_sale_composition_work.regular_sale_code AND
 regular_sale_composition.regular_sale_composition_no = regular_sale_composition_work.regular_sale_composition_no
WHEN MATCHED THEN UPDATE SET
regular_sale_composition_name = regular_sale_composition_work.regular_sale_composition_name,
regular_order_count_min_limit = regular_sale_composition_work.regular_order_count_min_limit,
regular_order_count_max_limit = regular_sale_composition_work.regular_order_count_max_limit,
regular_order_count_interval = regular_sale_composition_work.regular_order_count_interval,
retail_price = regular_sale_composition_work.retail_price,
regular_sale_commodity_point = regular_sale_composition_work.regular_sale_commodity_point,
display_order = regular_sale_composition_work.display_order,
orm_rowid = regular_sale_composition_work.orm_rowid,
created_user = regular_sale_composition_work.created_user,
created_datetime = CAST(NULLIF(regular_sale_composition_work.created_datetime, '') AS TIMESTAMP),
updated_user = regular_sale_composition_work.updated_user,
updated_datetime = CAST(NULLIF(regular_sale_composition_work.updated_datetime, '') AS TIMESTAMP),
dwh_updated_user = regular_sale_composition_work.dwh_updated_user,
dwh_updated_datetime = regular_sale_composition_work.dwh_updated_datetime
WHEN NOT MATCHED THEN INSERT VALUES (
regular_sale_composition_work.shop_code,
regular_sale_composition_work.regular_sale_code,
regular_sale_composition_work.regular_sale_composition_no,
regular_sale_composition_work.regular_sale_composition_name,
regular_sale_composition_work.regular_order_count_min_limit,
regular_sale_composition_work.regular_order_count_max_limit,
regular_sale_composition_work.regular_order_count_interval,
regular_sale_composition_work.retail_price,
regular_sale_composition_work.regular_sale_commodity_point,
regular_sale_composition_work.display_order,
regular_sale_composition_work.orm_rowid,
regular_sale_composition_work.created_user,
CAST(NULLIF(regular_sale_composition_work.created_datetime, '') AS TIMESTAMP),
regular_sale_composition_work.updated_user,
CAST(NULLIF(regular_sale_composition_work.updated_datetime, '') AS TIMESTAMP),
regular_sale_composition_work.dwh_created_user,
regular_sale_composition_work.dwh_created_datetime,
regular_sale_composition_work.dwh_updated_user,
regular_sale_composition_work.dwh_updated_datetime
);