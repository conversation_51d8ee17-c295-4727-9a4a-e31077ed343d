MERGE INTO dwh_raw.campaign_promotion USING dwh_raw.campaign_promotion_work
ON campaign_promotion.campaign_instructions_code = campaign_promotion_work.campaign_instructions_code AND
 campaign_promotion.promotion_no = campaign_promotion_work.promotion_no
WHEN MATCHED THEN UPDATE SET
promotion_type = campaign_promotion_work.promotion_type,
shop_code = campaign_promotion_work.shop_code,
commodity_code = campaign_promotion_work.commodity_code,
commodity_name = campaign_promotion_work.commodity_name,
present_qt = campaign_promotion_work.present_qt,
discount_rate = campaign_promotion_work.discount_rate,
discount_amount = campaign_promotion_work.discount_amount,
discount_retail_price = campaign_promotion_work.discount_retail_price,
shipping_charge = campaign_promotion_work.shipping_charge,
orm_rowid = campaign_promotion_work.orm_rowid,
created_user = campaign_promotion_work.created_user,
created_datetime = CAST(NULLIF(campaign_promotion_work.created_datetime, '') AS TIMESTAMP),
updated_user = campaign_promotion_work.updated_user,
updated_datetime = CAST(NULLIF(campaign_promotion_work.updated_datetime, '') AS TIMESTAMP),
dwh_updated_user = campaign_promotion_work.dwh_updated_user,
dwh_updated_datetime = campaign_promotion_work.dwh_updated_datetime
WHEN NOT MATCHED THEN INSERT VALUES (
campaign_promotion_work.campaign_instructions_code,
campaign_promotion_work.promotion_no,
campaign_promotion_work.promotion_type,
campaign_promotion_work.shop_code,
campaign_promotion_work.commodity_code,
campaign_promotion_work.commodity_name,
campaign_promotion_work.present_qt,
campaign_promotion_work.discount_rate,
campaign_promotion_work.discount_amount,
campaign_promotion_work.discount_retail_price,
campaign_promotion_work.shipping_charge,
campaign_promotion_work.orm_rowid,
campaign_promotion_work.created_user,
CAST(NULLIF(campaign_promotion_work.created_datetime, '') AS TIMESTAMP),
campaign_promotion_work.updated_user,
CAST(NULLIF(campaign_promotion_work.updated_datetime, '') AS TIMESTAMP),
campaign_promotion_work.dwh_created_user,
campaign_promotion_work.dwh_created_datetime,
campaign_promotion_work.dwh_updated_user,
campaign_promotion_work.dwh_updated_datetime
);