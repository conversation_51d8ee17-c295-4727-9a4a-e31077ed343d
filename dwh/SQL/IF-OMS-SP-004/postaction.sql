MERGE INTO dwh_raw.out_indicate_header USING dwh_raw.out_indicate_header_work
ON out_indicate_header.accept_no = out_indicate_header_work.accept_no
WHEN MATCHED THEN UPDATE SET
cust_no = out_indicate_header_work.cust_no,
record_no = out_indicate_header_work.record_no,
cust_name = out_indicate_header_work.cust_name,
post_no = out_indicate_header_work.post_no,
addr1 = out_indicate_header_work.addr1,
addr2 = out_indicate_header_work.addr2,
addr3 = out_indicate_header_work.addr3,
tel_no = out_indicate_header_work.tel_no,
prefecture_code = out_indicate_header_work.prefecture_code,
cust_flg = out_indicate_header_work.cust_flg,
order_date = CAST(NULLIF(out_indicate_header_work.order_date, '') AS DATE),
pay_kb = out_indicate_header_work.pay_kb,
total_price = out_indicate_header_work.total_price,
delive_cust_name = out_indicate_header_work.delive_cust_name,
delive_post_no = out_indicate_header_work.delive_post_no,
delive_addr1 = out_indicate_header_work.delive_addr1,
delive_addr2 = out_indicate_header_work.delive_addr2,
delive_addr3 = out_indicate_header_work.delive_addr3,
delive_tel_no = out_indicate_header_work.delive_tel_no,
gift_flg = out_indicate_header_work.gift_flg,
kibou_ymd = CAST(NULLIF(out_indicate_header_work.kibou_ymd, '') AS DATE),
night_flg = out_indicate_header_work.night_flg,
cosme_price = out_indicate_header_work.cosme_price,
health_price = out_indicate_header_work.health_price,
inner_price = out_indicate_header_work.inner_price,
update_date = CAST(NULLIF(out_indicate_header_work.update_date, '') AS DATE),
chit_print_date = CAST(NULLIF(out_indicate_header_work.chit_print_date, '') AS DATE),
yamato_bar_code = out_indicate_header_work.yamato_bar_code,
gyosha_flg = out_indicate_header_work.gyosha_flg,
status_flg = out_indicate_header_work.status_flg,
slip_ono = out_indicate_header_work.slip_ono,
order_no = out_indicate_header_work.order_no,
clinic_name = out_indicate_header_work.clinic_name,
shipment_date = CAST(NULLIF(out_indicate_header_work.shipment_date, '') AS DATE),
shipment_plan_date = CAST(NULLIF(out_indicate_header_work.shipment_plan_date, '') AS DATE),
pack_cnt = out_indicate_header_work.pack_cnt,
store_code = out_indicate_header_work.store_code,
period_flg = out_indicate_header_work.period_flg,
delivery_box_gb = out_indicate_header_work.delivery_box_gb,
air_delivery_yn = out_indicate_header_work.air_delivery_yn,
tax_amt = out_indicate_header_work.tax_amt,
conveni_yn = out_indicate_header_work.conveni_yn,
pudo_yn = out_indicate_header_work.pudo_yn,
inplan_yn = out_indicate_header_work.inplan_yn,
over_stock_yn = out_indicate_header_work.over_stock_yn,
reserve_order_yn = out_indicate_header_work.reserve_order_yn,
gift_rapping_yn = out_indicate_header_work.gift_rapping_yn,
kanshi_yn = out_indicate_header_work.kanshi_yn,
fusoku_yn = out_indicate_header_work.fusoku_yn,
airplane_yn = out_indicate_header_work.airplane_yn,
satofuru_yn = out_indicate_header_work.satofuru_yn,
tokusha_yn = out_indicate_header_work.tokusha_yn,
rakugaki_yn = out_indicate_header_work.rakugaki_yn,
multi_sample_yn = out_indicate_header_work.multi_sample_yn,
slip_size_code = out_indicate_header_work.slip_size_code,
wh_code = out_indicate_header_work.wh_code,
agent_cd = out_indicate_header_work.agent_cd,
import_yn = out_indicate_header_work.import_yn,
import_date = CAST(NULLIF(out_indicate_header_work.import_date, '') AS DATE),
dwh_updated_user = out_indicate_header_work.dwh_updated_user,
dwh_updated_datetime = out_indicate_header_work.dwh_updated_datetime
WHEN NOT MATCHED THEN INSERT VALUES (
out_indicate_header_work.accept_no,
out_indicate_header_work.cust_no,
out_indicate_header_work.record_no,
out_indicate_header_work.cust_name,
out_indicate_header_work.post_no,
out_indicate_header_work.addr1,
out_indicate_header_work.addr2,
out_indicate_header_work.addr3,
out_indicate_header_work.tel_no,
out_indicate_header_work.prefecture_code,
out_indicate_header_work.cust_flg,
CAST(NULLIF(out_indicate_header_work.order_date, '') AS DATE),
out_indicate_header_work.pay_kb,
out_indicate_header_work.total_price,
out_indicate_header_work.delive_cust_name,
out_indicate_header_work.delive_post_no,
out_indicate_header_work.delive_addr1,
out_indicate_header_work.delive_addr2,
out_indicate_header_work.delive_addr3,
out_indicate_header_work.delive_tel_no,
out_indicate_header_work.gift_flg,
CAST(NULLIF(out_indicate_header_work.kibou_ymd, '') AS DATE),
out_indicate_header_work.night_flg,
out_indicate_header_work.cosme_price,
out_indicate_header_work.health_price,
out_indicate_header_work.inner_price,
CAST(NULLIF(out_indicate_header_work.update_date, '') AS DATE),
CAST(NULLIF(out_indicate_header_work.chit_print_date, '') AS DATE),
out_indicate_header_work.yamato_bar_code,
out_indicate_header_work.gyosha_flg,
out_indicate_header_work.status_flg,
out_indicate_header_work.slip_ono,
out_indicate_header_work.order_no,
out_indicate_header_work.clinic_name,
CAST(NULLIF(out_indicate_header_work.shipment_date, '') AS DATE),
CAST(NULLIF(out_indicate_header_work.shipment_plan_date, '') AS DATE),
out_indicate_header_work.pack_cnt,
out_indicate_header_work.store_code,
out_indicate_header_work.period_flg,
out_indicate_header_work.delivery_box_gb,
out_indicate_header_work.air_delivery_yn,
out_indicate_header_work.tax_amt,
out_indicate_header_work.conveni_yn,
out_indicate_header_work.pudo_yn,
out_indicate_header_work.inplan_yn,
out_indicate_header_work.over_stock_yn,
out_indicate_header_work.reserve_order_yn,
out_indicate_header_work.gift_rapping_yn,
out_indicate_header_work.kanshi_yn,
out_indicate_header_work.fusoku_yn,
out_indicate_header_work.airplane_yn,
out_indicate_header_work.satofuru_yn,
out_indicate_header_work.tokusha_yn,
out_indicate_header_work.rakugaki_yn,
out_indicate_header_work.multi_sample_yn,
out_indicate_header_work.slip_size_code,
out_indicate_header_work.wh_code,
out_indicate_header_work.agent_cd,
out_indicate_header_work.import_yn,
CAST(NULLIF(out_indicate_header_work.import_date, '') AS DATE),
out_indicate_header_work.dwh_created_user,
out_indicate_header_work.dwh_created_datetime,
out_indicate_header_work.dwh_updated_user,
out_indicate_header_work.dwh_updated_datetime
);