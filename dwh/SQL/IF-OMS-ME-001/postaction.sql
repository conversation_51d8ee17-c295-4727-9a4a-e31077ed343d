MERGE INTO dwh_raw.card_info USING dwh_raw.card_info_work
ON card_info.customer_code = card_info_work.customer_code AND
 card_info.credit_card_kanri_no = card_info_work.credit_card_kanri_no AND
 card_info.credit_card_kanri_detail_no = card_info_work.credit_card_kanri_detail_no
WHEN MATCHED THEN UPDATE SET
credit_card_no = card_info_work.credit_card_no,
card_expire_year = card_info_work.card_expire_year,
card_expire_month = card_info_work.card_expire_month,
card_holder = card_info_work.card_holder,
card_brand = card_info_work.card_brand,
default_use_flag = card_info_work.default_use_flag,
change_datetime = CAST(NULLIF(card_info_work.change_datetime, '') AS TIMESTAMP),
change_channel_kbn = card_info_work.change_channel_kbn,
card_keep_ng_flg = card_info_work.card_keep_ng_flg,
change_reason_kbn = card_info_work.change_reason_kbn,
change_reason = card_info_work.change_reason,
change_user_code = card_info_work.change_user_code,
dhc_card_flg = card_info_work.dhc_card_flg,
crm_card_id = card_info_work.crm_card_id,
crm_card_updated_datetime = CAST(NULLIF(card_info_work.crm_card_updated_datetime, '') AS TIMESTAMP),
orm_rowid = card_info_work.orm_rowid,
created_user = card_info_work.created_user,
created_datetime = CAST(NULLIF(card_info_work.created_datetime, '') AS TIMESTAMP),
updated_user = card_info_work.updated_user,
updated_datetime = CAST(NULLIF(card_info_work.updated_datetime, '') AS TIMESTAMP),
dwh_updated_user = card_info_work.dwh_updated_user,
dwh_updated_datetime = card_info_work.dwh_updated_datetime
WHEN NOT MATCHED THEN INSERT VALUES (
card_info_work.customer_code,
card_info_work.credit_card_kanri_no,
card_info_work.credit_card_kanri_detail_no,
card_info_work.credit_card_no,
card_info_work.card_expire_year,
card_info_work.card_expire_month,
card_info_work.card_holder,
card_info_work.card_brand,
card_info_work.default_use_flag,
CAST(NULLIF(card_info_work.change_datetime, '') AS TIMESTAMP),
card_info_work.change_channel_kbn,
card_info_work.card_keep_ng_flg,
card_info_work.change_reason_kbn,
card_info_work.change_reason,
card_info_work.change_user_code,
card_info_work.dhc_card_flg,
card_info_work.crm_card_id,
CAST(NULLIF(card_info_work.crm_card_updated_datetime, '') AS TIMESTAMP),
card_info_work.orm_rowid,
card_info_work.created_user,
CAST(NULLIF(card_info_work.created_datetime, '') AS TIMESTAMP),
card_info_work.updated_user,
CAST(NULLIF(card_info_work.updated_datetime, '') AS TIMESTAMP),
card_info_work.dwh_created_user,
card_info_work.dwh_created_datetime,
card_info_work.dwh_updated_user,
card_info_work.dwh_updated_datetime
);