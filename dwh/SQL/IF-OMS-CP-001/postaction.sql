MERGE INTO dwh_raw.campaign_instructions USING dwh_raw.campaign_instructions_work
ON campaign_instructions.campaign_instructions_code = campaign_instructions_work.campaign_instructions_code
WHEN MATCHED THEN UPDATE SET
campaign_instructions_name = campaign_instructions_work.campaign_instructions_name,
campaign_type = campaign_instructions_work.campaign_type,
delete_flg = campaign_instructions_work.delete_flg,
campaign_priority = campaign_instructions_work.campaign_priority,
campaign_applied_scope = campaign_instructions_work.campaign_applied_scope,
campaign_use_limit = campaign_instructions_work.campaign_use_limit,
oneshot_order_limit = campaign_instructions_work.oneshot_order_limit,
campaign_quantity_limit = campaign_instructions_work.campaign_quantity_limit,
campaign_start_date = CAST(NULLIF(campaign_instructions_work.campaign_start_date, '') AS DATE),
campaign_end_date = CAST(NULLIF(campaign_instructions_work.campaign_end_date, '') AS DATE),
present_use_flg = campaign_instructions_work.present_use_flg,
campaign_customer_flg = campaign_instructions_work.campaign_customer_flg,
campaign_combi_limit_flg = campaign_instructions_work.campaign_combi_limit_flg,
permanent_campaign_flg = campaign_instructions_work.permanent_campaign_flg,
baitai_code = campaign_instructions_work.baitai_code,
campaign_description = campaign_instructions_work.campaign_description,
change_user_code = campaign_instructions_work.change_user_code,
orm_rowid = campaign_instructions_work.orm_rowid,
created_user = campaign_instructions_work.created_user,
created_datetime = CAST(NULLIF(campaign_instructions_work.created_datetime, '') AS TIMESTAMP),
updated_user = campaign_instructions_work.updated_user,
updated_datetime = CAST(NULLIF(campaign_instructions_work.updated_datetime, '') AS TIMESTAMP),
dwh_updated_user = campaign_instructions_work.dwh_updated_user,
dwh_updated_datetime = campaign_instructions_work.dwh_updated_datetime
WHEN NOT MATCHED THEN INSERT VALUES (
campaign_instructions_work.campaign_instructions_code,
campaign_instructions_work.campaign_instructions_name,
campaign_instructions_work.campaign_type,
campaign_instructions_work.delete_flg,
campaign_instructions_work.campaign_priority,
campaign_instructions_work.campaign_applied_scope,
campaign_instructions_work.campaign_use_limit,
campaign_instructions_work.oneshot_order_limit,
campaign_instructions_work.campaign_quantity_limit,
CAST(NULLIF(campaign_instructions_work.campaign_start_date, '') AS DATE),
CAST(NULLIF(campaign_instructions_work.campaign_end_date, '') AS DATE),
campaign_instructions_work.present_use_flg,
campaign_instructions_work.campaign_customer_flg,
campaign_instructions_work.campaign_combi_limit_flg,
campaign_instructions_work.permanent_campaign_flg,
campaign_instructions_work.baitai_code,
campaign_instructions_work.campaign_description,
campaign_instructions_work.change_user_code,
campaign_instructions_work.orm_rowid,
campaign_instructions_work.created_user,
CAST(NULLIF(campaign_instructions_work.created_datetime, '') AS TIMESTAMP),
campaign_instructions_work.updated_user,
CAST(NULLIF(campaign_instructions_work.updated_datetime, '') AS TIMESTAMP),
campaign_instructions_work.dwh_created_user,
campaign_instructions_work.dwh_created_datetime,
campaign_instructions_work.dwh_updated_user,
campaign_instructions_work.dwh_updated_datetime
);