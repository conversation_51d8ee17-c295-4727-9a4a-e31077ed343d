MERGE INTO dwh_raw.stock_change USING dwh_raw.stock_change_work
ON stock_change.slip_no = stock_change_work.slip_no AND
 stock_change.sh_control_number = stock_change_work.sh_control_number
WHEN MATCHED THEN UPDATE SET
wms_stock_io_type = stock_change_work.wms_stock_io_type,
slip_date = CAST(NULLIF(stock_change_work.slip_date, '') AS DATE),
quantity = stock_change_work.quantity,
stock_io_type = stock_change_work.stock_io_type,
center_code = stock_change_work.center_code,
stock_type = stock_change_work.stock_type,
swh_cmns_target_shop = stock_change_work.swh_cmns_target_shop,
center_code_partner = stock_change_work.center_code_partner,
stock_type_partner = stock_change_work.stock_type_partner,
swh_counter = stock_change_work.swh_counter,
receiving_shipping = stock_change_work.receiving_shipping,
receiving_shipping_name = stock_change_work.receiving_shipping_name,
reason_code_items = stock_change_work.reason_code_items,
reason_code_name = stock_change_work.reason_code_name,
reason_code_item = stock_change_work.reason_code_item,
dwh_updated_user = stock_change_work.dwh_updated_user,
dwh_updated_datetime = stock_change_work.dwh_updated_datetime
WHEN NOT MATCHED THEN INSERT VALUES (
stock_change_work.wms_stock_io_type,
CAST(NULLIF(stock_change_work.slip_date, '') AS DATE),
stock_change_work.slip_no,
stock_change_work.sh_control_number,
stock_change_work.quantity,
stock_change_work.stock_io_type,
stock_change_work.center_code,
stock_change_work.stock_type,
stock_change_work.swh_cmns_target_shop,
stock_change_work.center_code_partner,
stock_change_work.stock_type_partner,
stock_change_work.swh_counter,
stock_change_work.receiving_shipping,
stock_change_work.receiving_shipping_name,
stock_change_work.reason_code_items,
stock_change_work.reason_code_name,
stock_change_work.reason_code_item,
stock_change_work.dwh_created_user,
stock_change_work.dwh_created_datetime,
stock_change_work.dwh_updated_user,
stock_change_work.dwh_updated_datetime
);