MERGE INTO dwh_raw.order_campaign USING dwh_raw.order_campaign_work
ON order_campaign.order_no = order_campaign_work.order_no AND
 order_campaign.campaign_instructions_code = order_campaign_work.campaign_instructions_code
WHEN MATCHED THEN UPDATE SET
campaign_instructions_name = order_campaign_work.campaign_instructions_name,
campaign_description = order_campaign_work.campaign_description,
campaign_end_date = CAST(NULLIF(order_campaign_work.campaign_end_date, '') AS TIMESTAMP),
orm_rowid = order_campaign_work.orm_rowid,
created_user = order_campaign_work.created_user,
created_datetime = CAST(NULLIF(order_campaign_work.created_datetime, '') AS TIMESTAMP),
updated_user = order_campaign_work.updated_user,
updated_datetime = CAST(NULLIF(order_campaign_work.updated_datetime, '') AS TIMESTAMP),
dwh_updated_user = order_campaign_work.dwh_updated_user,
dwh_updated_datetime = order_campaign_work.dwh_updated_datetime
WHEN NOT MATCHED THEN INSERT VALUES (
order_campaign_work.order_no,
order_campaign_work.campaign_instructions_code,
order_campaign_work.campaign_instructions_name,
order_campaign_work.campaign_description,
CAST(NULLIF(order_campaign_work.campaign_end_date, '') AS TIMESTAMP),
order_campaign_work.orm_rowid,
order_campaign_work.created_user,
CAST(NULLIF(order_campaign_work.created_datetime, '') AS TIMESTAMP),
order_campaign_work.updated_user,
CAST(NULLIF(order_campaign_work.updated_datetime, '') AS TIMESTAMP),
order_campaign_work.dwh_created_user,
order_campaign_work.dwh_created_datetime,
order_campaign_work.dwh_updated_user,
order_campaign_work.dwh_updated_datetime
);