MERGE INTO dwh_raw.stock_month USING dwh_raw.stock_month_work
ON stock_month.business_year_month = stock_month_work.business_year_month AND
 stock_month.shop_cd = stock_month_work.shop_cd AND
 stock_month.item_cd = stock_month_work.item_cd
WHEN MATCHED THEN UPDATE SET
brand_cd = stock_month_work.brand_cd,
proper_item_kind = stock_month_work.proper_item_kind,
retail_price = stock_month_work.retail_price,
seling_price = stock_month_work.seling_price,
good_stock_count = stock_month_work.good_stock_count,
defective_stock_count = stock_month_work.defective_stock_count,
pending_stock_count = stock_month_work.pending_stock_count,
reserving_stock_count = stock_month_work.reserving_stock_count,
take_out_stock_count = stock_month_work.take_out_stock_count,
shipment_stock_count = stock_month_work.shipment_stock_count,
reservation_stock_count = stock_month_work.reservation_stock_count,
reserve_count1 = stock_month_work.reserve_count1,
reserve_count2 = stock_month_work.reserve_count2,
reserve_count3 = stock_month_work.reserve_count3,
reserve_count4 = stock_month_work.reserve_count4,
reserve_count5 = stock_month_work.reserve_count5,
ins_biz_date = CAST(NULLIF(stock_month_work.ins_biz_date, '') AS DATE),
upd_biz_date = CAST(NULLIF(stock_month_work.upd_biz_date, '') AS DATE),
ins_date = CAST(NULLIF(stock_month_work.ins_date, '') AS TIMESTAMP),
upd_date = CAST(NULLIF(stock_month_work.upd_date, '') AS TIMESTAMP),
ins_user_id = stock_month_work.ins_user_id,
upd_user_id = stock_month_work.upd_user_id,
ins_pgm_id = stock_month_work.ins_pgm_id,
upd_pgm_id = stock_month_work.upd_pgm_id,
dwh_updated_user = stock_month_work.dwh_updated_user,
dwh_updated_datetime = stock_month_work.dwh_updated_datetime
WHEN NOT MATCHED THEN INSERT VALUES (
stock_month_work.business_year_month,
stock_month_work.shop_cd,
stock_month_work.item_cd,
stock_month_work.brand_cd,
stock_month_work.proper_item_kind,
stock_month_work.retail_price,
stock_month_work.seling_price,
stock_month_work.good_stock_count,
stock_month_work.defective_stock_count,
stock_month_work.pending_stock_count,
stock_month_work.reserving_stock_count,
stock_month_work.take_out_stock_count,
stock_month_work.shipment_stock_count,
stock_month_work.reservation_stock_count,
stock_month_work.reserve_count1,
stock_month_work.reserve_count2,
stock_month_work.reserve_count3,
stock_month_work.reserve_count4,
stock_month_work.reserve_count5,
CAST(NULLIF(stock_month_work.ins_biz_date, '') AS DATE),
CAST(NULLIF(stock_month_work.upd_biz_date, '') AS DATE),
CAST(NULLIF(stock_month_work.ins_date, '') AS TIMESTAMP),
CAST(NULLIF(stock_month_work.upd_date, '') AS TIMESTAMP),
stock_month_work.ins_user_id,
stock_month_work.upd_user_id,
stock_month_work.ins_pgm_id,
stock_month_work.upd_pgm_id,
stock_month_work.dwh_created_user,
stock_month_work.dwh_created_datetime,
stock_month_work.dwh_updated_user,
stock_month_work.dwh_updated_datetime
);