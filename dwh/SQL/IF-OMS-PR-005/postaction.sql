MERGE INTO dwh_raw.set_commodity_composition USING dwh_raw.set_commodity_composition_work
ON set_commodity_composition.shop_code = set_commodity_composition_work.shop_code AND
 set_commodity_composition.commodity_code = set_commodity_composition_work.commodity_code AND
 set_commodity_composition.child_commodity_code = set_commodity_composition_work.child_commodity_code AND
 set_commodity_composition.composition_order = set_commodity_composition_work.composition_order
WHEN MATCHED THEN UPDATE SET
composition_quantity = set_commodity_composition_work.composition_quantity,
orm_rowid = set_commodity_composition_work.orm_rowid,
created_user = set_commodity_composition_work.created_user,
created_datetime = CAST(NULLIF(set_commodity_composition_work.created_datetime, '') AS TIMESTAMP),
updated_user = set_commodity_composition_work.updated_user,
updated_datetime = CAST(NULLIF(set_commodity_composition_work.updated_datetime, '') AS TIMESTAMP),
dwh_updated_user = set_commodity_composition_work.dwh_updated_user,
dwh_updated_datetime = set_commodity_composition_work.dwh_updated_datetime
WHEN NOT MATCHED THEN INSERT VALUES (
set_commodity_composition_work.shop_code,
set_commodity_composition_work.commodity_code,
set_commodity_composition_work.child_commodity_code,
set_commodity_composition_work.composition_quantity,
set_commodity_composition_work.composition_order,
set_commodity_composition_work.orm_rowid,
set_commodity_composition_work.created_user,
CAST(NULLIF(set_commodity_composition_work.created_datetime, '') AS TIMESTAMP),
set_commodity_composition_work.updated_user,
CAST(NULLIF(set_commodity_composition_work.updated_datetime, '') AS TIMESTAMP),
set_commodity_composition_work.dwh_created_user,
set_commodity_composition_work.dwh_created_datetime,
set_commodity_composition_work.dwh_updated_user,
set_commodity_composition_work.dwh_updated_datetime
);