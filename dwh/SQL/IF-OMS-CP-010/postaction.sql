MERGE INTO dwh_raw.order_campaign_use_amount USING dwh_raw.order_campaign_use_amount_work
ON order_campaign_use_amount.order_no = order_campaign_use_amount_work.order_no AND
 order_campaign_use_amount.tax_group_code = order_campaign_use_amount_work.tax_group_code AND
 order_campaign_use_amount.tax_no = order_campaign_use_amount_work.tax_no AND
 order_campaign_use_amount.use_code_type = order_campaign_use_amount_work.use_code_type AND
 order_campaign_use_amount.use_code = order_campaign_use_amount_work.use_code
WHEN MATCHED THEN UPDATE SET
use_amount = order_campaign_use_amount_work.use_amount,
tax_rate = order_campaign_use_amount_work.tax_rate,
orm_rowid = order_campaign_use_amount_work.orm_rowid,
created_user = order_campaign_use_amount_work.created_user,
created_datetime = CAST(NULLIF(order_campaign_use_amount_work.created_datetime, '') AS TIMESTAMP),
updated_user = order_campaign_use_amount_work.updated_user,
updated_datetime = CAST(NULLIF(order_campaign_use_amount_work.updated_datetime, '') AS TIMESTAMP),
dwh_updated_user = order_campaign_use_amount_work.dwh_updated_user,
dwh_updated_datetime = order_campaign_use_amount_work.dwh_updated_datetime
WHEN NOT MATCHED THEN INSERT VALUES (
order_campaign_use_amount_work.order_no,
order_campaign_use_amount_work.tax_group_code,
order_campaign_use_amount_work.tax_no,
order_campaign_use_amount_work.use_code_type,
order_campaign_use_amount_work.use_code,
order_campaign_use_amount_work.use_amount,
order_campaign_use_amount_work.tax_rate,
order_campaign_use_amount_work.orm_rowid,
order_campaign_use_amount_work.created_user,
CAST(NULLIF(order_campaign_use_amount_work.created_datetime, '') AS TIMESTAMP),
order_campaign_use_amount_work.updated_user,
CAST(NULLIF(order_campaign_use_amount_work.updated_datetime, '') AS TIMESTAMP),
order_campaign_use_amount_work.dwh_created_user,
order_campaign_use_amount_work.dwh_created_datetime,
order_campaign_use_amount_work.dwh_updated_user,
order_campaign_use_amount_work.dwh_updated_datetime
);