INSERT INTO dwh_raw.stock_list (
corp_cd,
data_date,
center_code,
stock_group_code,
shipping_code,
product_cd,
first_day_stock_count,
arrival_quantity,
shipping_quantity,
arrival_quantity_irregular,
shipping_quantity_irregular,
carryover_stock_count,
dwh_created_user,
dwh_created_datetime,
dwh_updated_user,
dwh_updated_datetime
)
SELECT
corp_cd,
CAST(NULLIF(data_date, '') AS TIMESTAMP),
center_code,
stock_group_code,
shipping_code,
product_cd,
first_day_stock_count,
arrival_quantity,
shipping_quantity,
arrival_quantity_irregular,
shipping_quantity_irregular,
carryover_stock_count,
dwh_created_user,
dwh_created_datetime,
dwh_updated_user,
dwh_updated_datetime
FROM dwh_raw.stock_list_work;
