MERGE INTO dwh_raw.stock USING dwh_raw.stock_work
ON stock.shop_code = stock_work.shop_code AND
 stock.sku_code = stock_work.sku_code AND
 stock.allocated_warehouse_code = stock_work.allocated_warehouse_code
WHEN MATCHED THEN UPDATE SET
commodity_code = stock_work.commodity_code,
wms_stock_quantity = stock_work.wms_stock_quantity,
stock_quantity = stock_work.stock_quantity,
allocated_quantity = stock_work.allocated_quantity,
reserved_quantity = stock_work.reserved_quantity,
temporary_allocated_quantity = stock_work.temporary_allocated_quantity,
arrival_reserved_quantity = stock_work.arrival_reserved_quantity,
temporary_reserved_quantity = stock_work.temporary_reserved_quantity,
reservation_limit = stock_work.reservation_limit,
stock_threshold = stock_work.stock_threshold,
stock_arrival_date = CAST(NULLIF(stock_work.stock_arrival_date, '') AS DATE),
arrival_quantity = stock_work.arrival_quantity,
orm_rowid = stock_work.orm_rowid,
created_user = stock_work.created_user,
created_datetime = CAST(NULLIF(stock_work.created_datetime, '') AS TIMESTAMP),
updated_user = stock_work.updated_user,
updated_datetime = CAST(NULLIF(stock_work.updated_datetime, '') AS TIMESTAMP),
dwh_updated_user = stock_work.dwh_updated_user,
dwh_updated_datetime = stock_work.dwh_updated_datetime
WHEN NOT MATCHED THEN INSERT VALUES (
stock_work.shop_code,
stock_work.sku_code,
stock_work.allocated_warehouse_code,
stock_work.commodity_code,
stock_work.wms_stock_quantity,
stock_work.stock_quantity,
stock_work.allocated_quantity,
stock_work.reserved_quantity,
stock_work.temporary_allocated_quantity,
stock_work.arrival_reserved_quantity,
stock_work.temporary_reserved_quantity,
stock_work.reservation_limit,
stock_work.stock_threshold,
CAST(NULLIF(stock_work.stock_arrival_date, '') AS DATE),
stock_work.arrival_quantity,
stock_work.orm_rowid,
stock_work.created_user,
CAST(NULLIF(stock_work.created_datetime, '') AS TIMESTAMP),
stock_work.updated_user,
CAST(NULLIF(stock_work.updated_datetime, '') AS TIMESTAMP),
stock_work.dwh_created_user,
stock_work.dwh_created_datetime,
stock_work.dwh_updated_user,
stock_work.dwh_updated_datetime
);