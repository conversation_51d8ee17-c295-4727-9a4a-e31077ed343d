MERGE INTO dwh_raw.campaign_combi_limit USING dwh_raw.campaign_combi_limit_work
ON campaign_combi_limit.campaign_instructions_code = campaign_combi_limit_work.campaign_instructions_code AND
 campaign_combi_limit.campaign_combi_limit_code = campaign_combi_limit_work.campaign_combi_limit_code
WHEN MATCHED THEN UPDATE SET
orm_rowid = campaign_combi_limit_work.orm_rowid,
created_user = campaign_combi_limit_work.created_user,
created_datetime = CAST(NULLIF(campaign_combi_limit_work.created_datetime, '') AS TIMESTAMP),
updated_user = campaign_combi_limit_work.updated_user,
updated_datetime = CAST(NULLIF(campaign_combi_limit_work.updated_datetime, '') AS TIMESTAMP),
dwh_updated_user = campaign_combi_limit_work.dwh_updated_user,
dwh_updated_datetime = campaign_combi_limit_work.dwh_updated_datetime
WHEN NOT MATCHED THEN INSERT VALUES (
campaign_combi_limit_work.campaign_instructions_code,
campaign_combi_limit_work.campaign_combi_limit_code,
campaign_combi_limit_work.orm_rowid,
campaign_combi_limit_work.created_user,
CAST(NULLIF(campaign_combi_limit_work.created_datetime, '') AS TIMESTAMP),
campaign_combi_limit_work.updated_user,
CAST(NULLIF(campaign_combi_limit_work.updated_datetime, '') AS TIMESTAMP),
campaign_combi_limit_work.dwh_created_user,
campaign_combi_limit_work.dwh_created_datetime,
campaign_combi_limit_work.dwh_updated_user,
campaign_combi_limit_work.dwh_updated_datetime
);