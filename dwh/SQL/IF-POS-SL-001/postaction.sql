MERGE INTO dwh_raw.pos_value_discount_relation USING dwh_raw.pos_value_discount_relation_work
ON pos_value_discount_relation.shop_cd = pos_value_discount_relation_work.shop_cd AND
 pos_value_discount_relation.register_num = pos_value_discount_relation_work.register_num AND
 pos_value_discount_relation.business_date = pos_value_discount_relation_work.business_date AND
 pos_value_discount_relation.receipt_num = pos_value_discount_relation_work.receipt_num AND
 pos_value_discount_relation.line_num = pos_value_discount_relation_work.line_num AND
 pos_value_discount_relation.target_line_num = pos_value_discount_relation_work.target_line_num
WHEN MATCHED THEN UPDATE SET
discount_cd = pos_value_discount_relation_work.discount_cd,
discount_kind = pos_value_discount_relation_work.discount_kind,
disc_rate = pos_value_discount_relation_work.disc_rate,
minus_amount = pos_value_discount_relation_work.minus_amount,
tax_inclusive = pos_value_discount_relation_work.tax_inclusive,
ins_biz_date = CAST(NULLIF(pos_value_discount_relation_work.ins_biz_date, '') AS DATE),
upd_biz_date = CAST(NULLIF(pos_value_discount_relation_work.upd_biz_date, '') AS DATE),
ins_date = CAST(NULLIF(pos_value_discount_relation_work.ins_date, '') AS TIMESTAMP),
upd_date = CAST(NULLIF(pos_value_discount_relation_work.upd_date, '') AS TIMESTAMP),
ins_user_id = pos_value_discount_relation_work.ins_user_id,
upd_user_id = pos_value_discount_relation_work.upd_user_id,
ins_pgm_id = pos_value_discount_relation_work.ins_pgm_id,
upd_pgm_id = pos_value_discount_relation_work.upd_pgm_id,
dwh_updated_user = pos_value_discount_relation_work.dwh_updated_user,
dwh_updated_datetime = pos_value_discount_relation_work.dwh_updated_datetime
WHEN NOT MATCHED THEN INSERT VALUES (
pos_value_discount_relation_work.shop_cd,
pos_value_discount_relation_work.register_num,
CAST(NULLIF(pos_value_discount_relation_work.business_date, '') AS DATE),
pos_value_discount_relation_work.receipt_num,
pos_value_discount_relation_work.line_num,
pos_value_discount_relation_work.target_line_num,
pos_value_discount_relation_work.discount_cd,
pos_value_discount_relation_work.discount_kind,
pos_value_discount_relation_work.disc_rate,
pos_value_discount_relation_work.minus_amount,
pos_value_discount_relation_work.tax_inclusive,
CAST(NULLIF(pos_value_discount_relation_work.ins_biz_date, '') AS DATE),
CAST(NULLIF(pos_value_discount_relation_work.upd_biz_date, '') AS DATE),
CAST(NULLIF(pos_value_discount_relation_work.ins_date, '') AS TIMESTAMP),
CAST(NULLIF(pos_value_discount_relation_work.upd_date, '') AS TIMESTAMP),
pos_value_discount_relation_work.ins_user_id,
pos_value_discount_relation_work.upd_user_id,
pos_value_discount_relation_work.ins_pgm_id,
pos_value_discount_relation_work.upd_pgm_id,
pos_value_discount_relation_work.dwh_created_user,
pos_value_discount_relation_work.dwh_created_datetime,
pos_value_discount_relation_work.dwh_updated_user,
pos_value_discount_relation_work.dwh_updated_datetime
);