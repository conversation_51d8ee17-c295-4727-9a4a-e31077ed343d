MERGE INTO dwh_raw.stock_io_detail USING dwh_raw.stock_io_detail_work
ON stock_io_detail.stock_io_id = stock_io_detail_work.stock_io_id
WHEN MATCHED THEN UPDATE SET
shop_code = stock_io_detail_work.shop_code,
stock_io_date = CAST(NULLIF(stock_io_detail_work.stock_io_date, '') AS DATE),
sku_code = stock_io_detail_work.sku_code,
stock_io_quantity = stock_io_detail_work.stock_io_quantity,
stock_io_type = stock_io_detail_work.stock_io_type,
memo = stock_io_detail_work.memo,
orm_rowid = stock_io_detail_work.orm_rowid,
created_user = stock_io_detail_work.created_user,
created_datetime = CAST(NULLIF(stock_io_detail_work.created_datetime, '') AS TIMESTAMP),
updated_user = stock_io_detail_work.updated_user,
updated_datetime = CAST(NULLIF(stock_io_detail_work.updated_datetime, '') AS TIMESTAMP),
dwh_updated_user = stock_io_detail_work.dwh_updated_user,
dwh_updated_datetime = stock_io_detail_work.dwh_updated_datetime
WHEN NOT MATCHED THEN INSERT VALUES (
stock_io_detail_work.stock_io_id,
stock_io_detail_work.shop_code,
CAST(NULLIF(stock_io_detail_work.stock_io_date, '') AS DATE),
stock_io_detail_work.sku_code,
stock_io_detail_work.stock_io_quantity,
stock_io_detail_work.stock_io_type,
stock_io_detail_work.memo,
stock_io_detail_work.orm_rowid,
stock_io_detail_work.created_user,
CAST(NULLIF(stock_io_detail_work.created_datetime, '') AS TIMESTAMP),
stock_io_detail_work.updated_user,
CAST(NULLIF(stock_io_detail_work.updated_datetime, '') AS TIMESTAMP),
stock_io_detail_work.dwh_created_user,
stock_io_detail_work.dwh_created_datetime,
stock_io_detail_work.dwh_updated_user,
stock_io_detail_work.dwh_updated_datetime
);