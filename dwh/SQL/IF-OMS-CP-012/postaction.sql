MERGE INTO dwh_raw.order_campaign_history USING dwh_raw.order_campaign_history_work
ON order_campaign_history.order_history_id = order_campaign_history_work.order_history_id AND
 order_campaign_history.order_no = order_campaign_history_work.order_no AND
 order_campaign_history.campaign_instructions_code = order_campaign_history_work.campaign_instructions_code
WHEN MATCHED THEN UPDATE SET
campaign_instructions_name = order_campaign_history_work.campaign_instructions_name,
campaign_description = order_campaign_history_work.campaign_description,
campaign_end_date = CAST(NULLIF(order_campaign_history_work.campaign_end_date, '') AS TIMESTAMP),
orm_rowid = order_campaign_history_work.orm_rowid,
created_user = order_campaign_history_work.created_user,
created_datetime = CAST(NULLIF(order_campaign_history_work.created_datetime, '') AS TIMESTAMP),
updated_user = order_campaign_history_work.updated_user,
updated_datetime = CAST(NULLIF(order_campaign_history_work.updated_datetime, '') AS TIMESTAMP),
dwh_updated_user = order_campaign_history_work.dwh_updated_user,
dwh_updated_datetime = order_campaign_history_work.dwh_updated_datetime
WHEN NOT MATCHED THEN INSERT VALUES (
order_campaign_history_work.order_history_id,
order_campaign_history_work.order_no,
order_campaign_history_work.campaign_instructions_code,
order_campaign_history_work.campaign_instructions_name,
order_campaign_history_work.campaign_description,
CAST(NULLIF(order_campaign_history_work.campaign_end_date, '') AS TIMESTAMP),
order_campaign_history_work.orm_rowid,
order_campaign_history_work.created_user,
CAST(NULLIF(order_campaign_history_work.created_datetime, '') AS TIMESTAMP),
order_campaign_history_work.updated_user,
CAST(NULLIF(order_campaign_history_work.updated_datetime, '') AS TIMESTAMP),
order_campaign_history_work.dwh_created_user,
order_campaign_history_work.dwh_created_datetime,
order_campaign_history_work.dwh_updated_user,
order_campaign_history_work.dwh_updated_datetime
);