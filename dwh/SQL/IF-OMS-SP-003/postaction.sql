MERGE INTO dwh_raw.shipping_detail_composition USING dwh_raw.shipping_detail_composition_work
ON shipping_detail_composition.shipping_no = shipping_detail_composition_work.shipping_no AND
 shipping_detail_composition.shipping_detail_no = shipping_detail_composition_work.shipping_detail_no AND
 shipping_detail_composition.composition_no = shipping_detail_composition_work.composition_no
WHEN MATCHED THEN UPDATE SET
shop_code = shipping_detail_composition_work.shop_code,
parent_commodity_code = shipping_detail_composition_work.parent_commodity_code,
parent_sku_code = shipping_detail_composition_work.parent_sku_code,
child_commodity_code = shipping_detail_composition_work.child_commodity_code,
child_sku_code = shipping_detail_composition_work.child_sku_code,
commodity_name = shipping_detail_composition_work.commodity_name,
standard_detail1_name = shipping_detail_composition_work.standard_detail1_name,
standard_detail2_name = shipping_detail_composition_work.standard_detail2_name,
unit_price = shipping_detail_composition_work.unit_price,
discount_amount = shipping_detail_composition_work.discount_amount,
retail_price = shipping_detail_composition_work.retail_price,
retail_tax = shipping_detail_composition_work.retail_tax,
commodity_tax_group_code = shipping_detail_composition_work.commodity_tax_group_code,
commodity_tax_no = shipping_detail_composition_work.commodity_tax_no,
commodity_tax_rate = shipping_detail_composition_work.commodity_tax_rate,
commodity_tax = shipping_detail_composition_work.commodity_tax,
commodity_tax_type = shipping_detail_composition_work.commodity_tax_type,
composition_quantity = shipping_detail_composition_work.composition_quantity,
stock_management_type = shipping_detail_composition_work.stock_management_type,
stock_allocated_kbn = shipping_detail_composition_work.stock_allocated_kbn,
allocated_warehouse_code = shipping_detail_composition_work.allocated_warehouse_code,
allocated_quantity = shipping_detail_composition_work.allocated_quantity,
arrival_reserved_quantity = shipping_detail_composition_work.arrival_reserved_quantity,
orm_rowid = shipping_detail_composition_work.orm_rowid,
created_user = shipping_detail_composition_work.created_user,
created_datetime = CAST(NULLIF(shipping_detail_composition_work.created_datetime, '') AS TIMESTAMP),
updated_user = shipping_detail_composition_work.updated_user,
updated_datetime = CAST(NULLIF(shipping_detail_composition_work.updated_datetime, '') AS TIMESTAMP),
dwh_updated_user = shipping_detail_composition_work.dwh_updated_user,
dwh_updated_datetime = shipping_detail_composition_work.dwh_updated_datetime
WHEN NOT MATCHED THEN INSERT VALUES (
shipping_detail_composition_work.shipping_no,
shipping_detail_composition_work.shipping_detail_no,
shipping_detail_composition_work.composition_no,
shipping_detail_composition_work.shop_code,
shipping_detail_composition_work.parent_commodity_code,
shipping_detail_composition_work.parent_sku_code,
shipping_detail_composition_work.child_commodity_code,
shipping_detail_composition_work.child_sku_code,
shipping_detail_composition_work.commodity_name,
shipping_detail_composition_work.standard_detail1_name,
shipping_detail_composition_work.standard_detail2_name,
shipping_detail_composition_work.unit_price,
shipping_detail_composition_work.discount_amount,
shipping_detail_composition_work.retail_price,
shipping_detail_composition_work.retail_tax,
shipping_detail_composition_work.commodity_tax_group_code,
shipping_detail_composition_work.commodity_tax_no,
shipping_detail_composition_work.commodity_tax_rate,
shipping_detail_composition_work.commodity_tax,
shipping_detail_composition_work.commodity_tax_type,
shipping_detail_composition_work.composition_quantity,
shipping_detail_composition_work.stock_management_type,
shipping_detail_composition_work.stock_allocated_kbn,
shipping_detail_composition_work.allocated_warehouse_code,
shipping_detail_composition_work.allocated_quantity,
shipping_detail_composition_work.arrival_reserved_quantity,
shipping_detail_composition_work.orm_rowid,
shipping_detail_composition_work.created_user,
CAST(NULLIF(shipping_detail_composition_work.created_datetime, '') AS TIMESTAMP),
shipping_detail_composition_work.updated_user,
CAST(NULLIF(shipping_detail_composition_work.updated_datetime, '') AS TIMESTAMP),
shipping_detail_composition_work.dwh_created_user,
shipping_detail_composition_work.dwh_created_datetime,
shipping_detail_composition_work.dwh_updated_user,
shipping_detail_composition_work.dwh_updated_datetime
);