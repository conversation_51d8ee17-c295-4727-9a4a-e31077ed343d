MERGE INTO dwh_raw.core_department USING dwh_raw.core_department_work
ON core_department.core_department = core_department_work.core_department
WHEN MATCHED THEN UPDATE SET
core_department_name = core_department_work.core_department_name,
core_department_nk = core_department_work.core_department_nk,
insert_date = CAST(NULLIF(core_department_work.insert_date, '') AS TIMESTAMP),
insert_id = core_department_work.insert_id,
modify_date = CAST(NULLIF(core_department_work.modify_date, '') AS TIMESTAMP),
modify_id = core_department_work.modify_id,
dwh_updated_user = core_department_work.dwh_updated_user,
dwh_updated_datetime = core_department_work.dwh_updated_datetime
WHEN NOT MATCHED THEN INSERT VALUES (
core_department_work.core_department,
core_department_work.core_department_name,
core_department_work.core_department_nk,
CAST(NULLIF(core_department_work.insert_date, '') AS TIMESTAMP),
core_department_work.insert_id,
CAST(NULLIF(core_department_work.modify_date, '') AS TIMESTAMP),
core_department_work.modify_id,
core_department_work.dwh_created_user,
core_department_work.dwh_created_datetime,
core_department_work.dwh_updated_user,
core_department_work.dwh_updated_datetime
);