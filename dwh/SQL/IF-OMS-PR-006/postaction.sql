MERGE INTO dwh_raw.regular_sale_base USING dwh_raw.regular_sale_base_work
ON regular_sale_base.shop_code = regular_sale_base_work.shop_code AND
 regular_sale_base.regular_sale_code = regular_sale_base_work.regular_sale_code
WHEN MATCHED THEN UPDATE SET
sku_code = regular_sale_base_work.sku_code,
commodity_code = regular_sale_base_work.commodity_code,
regular_cycle_kind_list = regular_sale_base_work.regular_cycle_kind_list,
regular_cycle_days_list = regular_sale_base_work.regular_cycle_days_list,
regular_cycle_months_list = regular_sale_base_work.regular_cycle_months_list,
regular_sale_stop_from = regular_sale_base_work.regular_sale_stop_from,
regular_sale_stop_to = regular_sale_base_work.regular_sale_stop_to,
orm_rowid = regular_sale_base_work.orm_rowid,
created_user = regular_sale_base_work.created_user,
created_datetime = CAST(NULLIF(regular_sale_base_work.created_datetime, '') AS TIMESTAMP),
updated_user = regular_sale_base_work.updated_user,
updated_datetime = CAST(NULLIF(regular_sale_base_work.updated_datetime, '') AS TIMESTAMP),
dwh_updated_user = regular_sale_base_work.dwh_updated_user,
dwh_updated_datetime = regular_sale_base_work.dwh_updated_datetime
WHEN NOT MATCHED THEN INSERT VALUES (
regular_sale_base_work.shop_code,
regular_sale_base_work.regular_sale_code,
regular_sale_base_work.sku_code,
regular_sale_base_work.commodity_code,
regular_sale_base_work.regular_cycle_kind_list,
regular_sale_base_work.regular_cycle_days_list,
regular_sale_base_work.regular_cycle_months_list,
regular_sale_base_work.regular_sale_stop_from,
regular_sale_base_work.regular_sale_stop_to,
regular_sale_base_work.orm_rowid,
regular_sale_base_work.created_user,
CAST(NULLIF(regular_sale_base_work.created_datetime, '') AS TIMESTAMP),
regular_sale_base_work.updated_user,
CAST(NULLIF(regular_sale_base_work.updated_datetime, '') AS TIMESTAMP),
regular_sale_base_work.dwh_created_user,
regular_sale_base_work.dwh_created_datetime,
regular_sale_base_work.dwh_updated_user,
regular_sale_base_work.dwh_updated_datetime
);