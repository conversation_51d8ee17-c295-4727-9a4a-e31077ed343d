MERGE INTO dwh_raw.sales_record USING dwh_raw.sales_record_work
ON sales_record.earnings_jisseki_no = sales_record_work.earnings_jisseki_no
WHEN MATCHED THEN UPDATE SET
order_henpin_kbn = sales_record_work.order_henpin_kbn,
sales_detail_kbn = sales_record_work.sales_detail_kbn,
shipping_no = sales_record_work.shipping_no,
shipping_detail_no = sales_record_work.shipping_detail_no,
order_no = sales_record_work.order_no,
order_detail_no = sales_record_work.order_detail_no,
shipping_date = CAST(NULLIF(sales_record_work.shipping_date, '') AS DATE),
sales_recording_date = CAST(NULLIF(sales_record_work.sales_recording_date, '') AS DATE),
marketing_channel = sales_record_work.marketing_channel,
ext_payment_method_type = sales_record_work.ext_payment_method_type,
shop_code = sales_record_work.shop_code,
main_product_no = sales_record_work.main_product_no,
product_no = sales_record_work.product_no,
period_flg = sales_record_work.period_flg,
baitai_code = sales_record_work.baitai_code,
commodity_code = sales_record_work.commodity_code,
hinban_code = sales_record_work.hinban_code,
commodity_name = sales_record_work.commodity_name,
commodity_kind = sales_record_work.commodity_kind,
commodity_group = sales_record_work.commodity_group,
commodity_series = sales_record_work.commodity_series,
commodity_segment = sales_record_work.commodity_segment,
business_segment = sales_record_work.business_segment,
parent_commodity_code = sales_record_work.parent_commodity_code,
commodity_amount = sales_record_work.commodity_amount,
campaign_instructions_code = sales_record_work.campaign_instructions_code,
coupon_management_code = sales_record_work.coupon_management_code,
incurred_price = sales_record_work.incurred_price,
tax_group_code = sales_record_work.tax_group_code,
tax_no = sales_record_work.tax_no,
tax_rate = sales_record_work.tax_rate,
commodity_tax_type = sales_record_work.commodity_tax_type,
business_partner_code = sales_record_work.business_partner_code,
sales_bumon_cd = sales_record_work.sales_bumon_cd,
channel_cd = sales_record_work.channel_cd,
sales_link_status = sales_record_work.sales_link_status,
sales_link_datetime = CAST(NULLIF(sales_record_work.sales_link_datetime, '') AS TIMESTAMP),
orm_rowid = sales_record_work.orm_rowid,
created_user = sales_record_work.created_user,
created_datetime = CAST(NULLIF(sales_record_work.created_datetime, '') AS TIMESTAMP),
updated_user = sales_record_work.updated_user,
updated_datetime = CAST(NULLIF(sales_record_work.updated_datetime, '') AS TIMESTAMP),
dwh_updated_user = sales_record_work.dwh_updated_user,
dwh_updated_datetime = sales_record_work.dwh_updated_datetime
WHEN NOT MATCHED THEN INSERT VALUES (
sales_record_work.earnings_jisseki_no,
sales_record_work.order_henpin_kbn,
sales_record_work.sales_detail_kbn,
sales_record_work.shipping_no,
sales_record_work.shipping_detail_no,
sales_record_work.order_no,
sales_record_work.order_detail_no,
CAST(NULLIF(sales_record_work.shipping_date, '') AS DATE),
CAST(NULLIF(sales_record_work.sales_recording_date, '') AS DATE),
sales_record_work.marketing_channel,
sales_record_work.ext_payment_method_type,
sales_record_work.shop_code,
sales_record_work.main_product_no,
sales_record_work.product_no,
sales_record_work.period_flg,
sales_record_work.baitai_code,
sales_record_work.commodity_code,
sales_record_work.hinban_code,
sales_record_work.commodity_name,
sales_record_work.commodity_kind,
sales_record_work.commodity_group,
sales_record_work.commodity_series,
sales_record_work.commodity_segment,
sales_record_work.business_segment,
sales_record_work.parent_commodity_code,
sales_record_work.commodity_amount,
sales_record_work.campaign_instructions_code,
sales_record_work.coupon_management_code,
sales_record_work.incurred_price,
sales_record_work.tax_group_code,
sales_record_work.tax_no,
sales_record_work.tax_rate,
sales_record_work.commodity_tax_type,
sales_record_work.business_partner_code,
sales_record_work.sales_bumon_cd,
sales_record_work.channel_cd,
sales_record_work.sales_link_status,
CAST(NULLIF(sales_record_work.sales_link_datetime, '') AS TIMESTAMP),
sales_record_work.orm_rowid,
sales_record_work.created_user,
CAST(NULLIF(sales_record_work.created_datetime, '') AS TIMESTAMP),
sales_record_work.updated_user,
CAST(NULLIF(sales_record_work.updated_datetime, '') AS TIMESTAMP),
sales_record_work.dwh_created_user,
sales_record_work.dwh_created_datetime,
sales_record_work.dwh_updated_user,
sales_record_work.dwh_updated_datetime
);