MERGE INTO dwh_raw.category_commodity USING dwh_raw.category_commodity_work
ON category_commodity.shop_code = category_commodity_work.shop_code AND
 category_commodity.category_code = category_commodity_work.category_code AND
 category_commodity.commodity_code = category_commodity_work.commodity_code
WHEN MATCHED THEN UPDATE SET
category_search_path = category_commodity_work.category_search_path,
search_category_code0 = category_commodity_work.search_category_code0,
search_category_code1 = category_commodity_work.search_category_code1,
search_category_code2 = category_commodity_work.search_category_code2,
search_category_code3 = category_commodity_work.search_category_code3,
search_category_code4 = category_commodity_work.search_category_code4,
search_category_code5 = category_commodity_work.search_category_code5,
search_category_code6 = category_commodity_work.search_category_code6,
orm_rowid = category_commodity_work.orm_rowid,
created_user = category_commodity_work.created_user,
created_datetime = CAST(NULLIF(category_commodity_work.created_datetime, '') AS TIMESTAMP),
updated_user = category_commodity_work.updated_user,
updated_datetime = CAST(NULLIF(category_commodity_work.updated_datetime, '') AS TIMESTAMP),
dwh_updated_user = category_commodity_work.dwh_updated_user,
dwh_updated_datetime = category_commodity_work.dwh_updated_datetime
WHEN NOT MATCHED THEN INSERT VALUES (
category_commodity_work.shop_code,
category_commodity_work.category_code,
category_commodity_work.commodity_code,
category_commodity_work.category_search_path,
category_commodity_work.search_category_code0,
category_commodity_work.search_category_code1,
category_commodity_work.search_category_code2,
category_commodity_work.search_category_code3,
category_commodity_work.search_category_code4,
category_commodity_work.search_category_code5,
category_commodity_work.search_category_code6,
category_commodity_work.orm_rowid,
category_commodity_work.created_user,
CAST(NULLIF(category_commodity_work.created_datetime, '') AS TIMESTAMP),
category_commodity_work.updated_user,
CAST(NULLIF(category_commodity_work.updated_datetime, '') AS TIMESTAMP),
category_commodity_work.dwh_created_user,
category_commodity_work.dwh_created_datetime,
category_commodity_work.dwh_updated_user,
category_commodity_work.dwh_updated_datetime
);