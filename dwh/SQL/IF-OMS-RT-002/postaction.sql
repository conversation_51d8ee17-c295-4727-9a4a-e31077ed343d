MERGE INTO dwh_raw.returns_detail USING dwh_raw.returns_detail_work
ON returns_detail.henpin_request_no = returns_detail_work.henpin_request_no AND
 returns_detail.shipping_no = returns_detail_work.shipping_no AND
 returns_detail.shipping_detail_no = returns_detail_work.shipping_detail_no
WHEN MATCHED THEN UPDATE SET
order_no = returns_detail_work.order_no,
order_detail_no = returns_detail_work.order_detail_no,
shop_code = returns_detail_work.shop_code,
sku_code = returns_detail_work.sku_code,
commodity_code = returns_detail_work.commodity_code,
commodity_name = returns_detail_work.commodity_name,
commodity_kind = returns_detail_work.commodity_kind,
baitai_code = returns_detail_work.baitai_code,
baitai_name = returns_detail_work.baitai_name,
hinban_code = returns_detail_work.hinban_code,
benefits_code = returns_detail_work.benefits_code,
benefits_name = returns_detail_work.benefits_name,
henpin_qt = returns_detail_work.henpin_qt,
regular_contract_no = returns_detail_work.regular_contract_no,
unit_price = returns_detail_work.unit_price,
discount_price = returns_detail_work.discount_price,
discount_amount = returns_detail_work.discount_amount,
retail_price = returns_detail_work.retail_price,
retail_tax_group_code = returns_detail_work.retail_tax_group_code,
retail_tax_no = returns_detail_work.retail_tax_no,
retail_tax_rate = returns_detail_work.retail_tax_rate,
retail_tax = returns_detail_work.retail_tax,
commodity_tax = returns_detail_work.commodity_tax,
commodity_tax_type = returns_detail_work.commodity_tax_type,
purchasing_amount = returns_detail_work.purchasing_amount,
henpin_price = returns_detail_work.henpin_price,
henpin_yoyaku_qt = returns_detail_work.henpin_yoyaku_qt,
change_qt = returns_detail_work.change_qt,
henpin_support_kind = returns_detail_work.henpin_support_kind,
wms_henpin_rireki_no = returns_detail_work.wms_henpin_rireki_no,
copy_soko_shiji = returns_detail_work.copy_soko_shiji,
grant_plan_point_prod_detail = returns_detail_work.grant_plan_point_prod_detail,
reduction_plan_point_prod_detail = returns_detail_work.reduction_plan_point_prod_detail,
campaign_instructions_code = returns_detail_work.campaign_instructions_code,
campaign_instructions_name = returns_detail_work.campaign_instructions_name,
orm_rowid = returns_detail_work.orm_rowid,
created_user = returns_detail_work.created_user,
created_datetime = CAST(NULLIF(returns_detail_work.created_datetime, '') AS TIMESTAMP),
updated_user = returns_detail_work.updated_user,
updated_datetime = CAST(NULLIF(returns_detail_work.updated_datetime, '') AS TIMESTAMP),
dwh_updated_user = returns_detail_work.dwh_updated_user,
dwh_updated_datetime = returns_detail_work.dwh_updated_datetime
WHEN NOT MATCHED THEN INSERT VALUES (
returns_detail_work.henpin_request_no,
returns_detail_work.shipping_no,
returns_detail_work.shipping_detail_no,
returns_detail_work.order_no,
returns_detail_work.order_detail_no,
returns_detail_work.shop_code,
returns_detail_work.sku_code,
returns_detail_work.commodity_code,
returns_detail_work.commodity_name,
returns_detail_work.commodity_kind,
returns_detail_work.baitai_code,
returns_detail_work.baitai_name,
returns_detail_work.hinban_code,
returns_detail_work.benefits_code,
returns_detail_work.benefits_name,
returns_detail_work.henpin_qt,
returns_detail_work.regular_contract_no,
returns_detail_work.unit_price,
returns_detail_work.discount_price,
returns_detail_work.discount_amount,
returns_detail_work.retail_price,
returns_detail_work.retail_tax_group_code,
returns_detail_work.retail_tax_no,
returns_detail_work.retail_tax_rate,
returns_detail_work.retail_tax,
returns_detail_work.commodity_tax,
returns_detail_work.commodity_tax_type,
returns_detail_work.purchasing_amount,
returns_detail_work.henpin_price,
returns_detail_work.henpin_yoyaku_qt,
returns_detail_work.change_qt,
returns_detail_work.henpin_support_kind,
returns_detail_work.wms_henpin_rireki_no,
returns_detail_work.copy_soko_shiji,
returns_detail_work.grant_plan_point_prod_detail,
returns_detail_work.reduction_plan_point_prod_detail,
returns_detail_work.campaign_instructions_code,
returns_detail_work.campaign_instructions_name,
returns_detail_work.orm_rowid,
returns_detail_work.created_user,
CAST(NULLIF(returns_detail_work.created_datetime, '') AS TIMESTAMP),
returns_detail_work.updated_user,
CAST(NULLIF(returns_detail_work.updated_datetime, '') AS TIMESTAMP),
returns_detail_work.dwh_created_user,
returns_detail_work.dwh_created_datetime,
returns_detail_work.dwh_updated_user,
returns_detail_work.dwh_updated_datetime
);