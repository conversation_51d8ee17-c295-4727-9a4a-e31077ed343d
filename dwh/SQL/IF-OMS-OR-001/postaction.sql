MERGE INTO dwh_raw.order_header USING dwh_raw.order_header_work
ON order_header.order_no = order_header_work.order_no
WHEN MATCHED THEN UPDATE SET
shop_code = order_header_work.shop_code,
order_datetime = CAST(NULLIF(order_header_work.order_datetime, '') AS TIMESTAMP),
customer_code = order_header_work.customer_code,
neo_customer_no = order_header_work.neo_customer_no,
guest_flg = order_header_work.guest_flg,
last_name = order_header_work.last_name,
first_name = order_header_work.first_name,
last_name_kana = order_header_work.last_name_kana,
first_name_kana = order_header_work.first_name_kana,
email = order_header_work.email,
birth_date = CAST(NULLIF(order_header_work.birth_date, '') AS DATE),
sex = order_header_work.sex,
postal_code = order_header_work.postal_code,
prefecture_code = order_header_work.prefecture_code,
address1 = order_header_work.address1,
address2 = order_header_work.address2,
address3 = order_header_work.address3,
address4 = order_header_work.address4,
corporation_post_name = order_header_work.corporation_post_name,
phone_number = order_header_work.phone_number,
advance_later_flg = order_header_work.advance_later_flg,
payment_method_no = order_header_work.payment_method_no,
payment_method_type = order_header_work.payment_method_type,
payment_method_name = order_header_work.payment_method_name,
ext_payment_method_type = order_header_work.ext_payment_method_type,
payment_commission = order_header_work.payment_commission,
payment_commission_tax_gr_code = order_header_work.payment_commission_tax_gr_code,
payment_commission_tax_no = order_header_work.payment_commission_tax_no,
payment_commission_tax_rate = order_header_work.payment_commission_tax_rate,
payment_commission_tax = order_header_work.payment_commission_tax,
payment_commission_tax_type = order_header_work.payment_commission_tax_type,
coupon_management_code = order_header_work.coupon_management_code,
coupon_code = order_header_work.coupon_code,
coupon_name = order_header_work.coupon_name,
coupon_type = order_header_work.coupon_type,
coupon_use_purchase_price = order_header_work.coupon_use_purchase_price,
coupon_discount_type = order_header_work.coupon_discount_type,
coupon_discount_price = order_header_work.coupon_discount_price,
coupon_discount_rate = order_header_work.coupon_discount_rate,
coupon_used_amount = order_header_work.coupon_used_amount,
coupon_start_datetime = CAST(NULLIF(order_header_work.coupon_start_datetime, '') AS TIMESTAMP),
coupon_end_datetime = CAST(NULLIF(order_header_work.coupon_end_datetime, '') AS TIMESTAMP),
coupon_kbn = order_header_work.coupon_kbn,
goods_group = order_header_work.goods_group,
commodity_category_code = order_header_work.commodity_category_code,
commodity_series = order_header_work.commodity_series,
coupon_commodity_code_display = order_header_work.coupon_commodity_code_display,
baitai_name = order_header_work.baitai_name,
used_point = order_header_work.used_point,
total_amount = order_header_work.total_amount,
ec_promotion_id = order_header_work.ec_promotion_id,
ec_promotion_name = order_header_work.ec_promotion_name,
ec_promotion_discount_price = order_header_work.ec_promotion_discount_price,
ec_campaign_id = order_header_work.ec_campaign_id,
ec_campaign_name = order_header_work.ec_campaign_name,
payment_date = CAST(NULLIF(order_header_work.payment_date, '') AS DATE),
payment_limit_date = CAST(NULLIF(order_header_work.payment_limit_date, '') AS DATE),
payment_status = order_header_work.payment_status,
ext_payment_status = order_header_work.ext_payment_status,
customer_group_code = order_header_work.customer_group_code,
data_transport_status = order_header_work.data_transport_status,
order_status = order_header_work.order_status,
ext_order_status = order_header_work.ext_order_status,
tax_reference_date = CAST(NULLIF(order_header_work.tax_reference_date, '') AS DATE),
cancel_date = CAST(NULLIF(order_header_work.cancel_date, '') AS DATE),
client_group = order_header_work.client_group,
caution = order_header_work.caution,
message = order_header_work.message,
payment_order_id = order_header_work.payment_order_id,
cvs_code = order_header_work.cvs_code,
payment_receipt_no = order_header_work.payment_receipt_no,
payment_receipt_url = order_header_work.payment_receipt_url,
receipt_no = order_header_work.receipt_no,
customer_no = order_header_work.customer_no,
confirm_no = order_header_work.confirm_no,
career_key = order_header_work.career_key,
order_create_error_code = order_header_work.order_create_error_code,
order_display_status = order_header_work.order_display_status,
order_kind_kbn = order_header_work.order_kind_kbn,
marketing_channel = order_header_work.marketing_channel,
original_order_no = order_header_work.original_order_no,
external_order_no = order_header_work.external_order_no,
order_recieve_datetime = CAST(NULLIF(order_header_work.order_recieve_datetime, '') AS TIMESTAMP),
order_update_datetime = CAST(NULLIF(order_header_work.order_update_datetime, '') AS TIMESTAMP),
order_update_reason_kbn = order_header_work.order_update_reason_kbn,
cancel_reason_kbn = order_header_work.cancel_reason_kbn,
uncollectible_date = CAST(NULLIF(order_header_work.uncollectible_date, '') AS DATE),
order_total_price = order_header_work.order_total_price,
account_receivable_balance = order_header_work.account_receivable_balance,
appropriate_amount = order_header_work.appropriate_amount,
bill_address_kbn = order_header_work.bill_address_kbn,
receipt_flg = order_header_work.receipt_flg,
receipt_to = order_header_work.receipt_to,
receipt_detail = order_header_work.receipt_detail,
bill_price = order_header_work.bill_price,
bill_no = order_header_work.bill_no,
bill_print_count = order_header_work.bill_print_count,
authority_result_kbn = order_header_work.authority_result_kbn,
authority_no = order_header_work.authority_no,
card_password = order_header_work.card_password,
authority_approval_no = order_header_work.authority_approval_no,
authority_date = CAST(NULLIF(order_header_work.authority_date, '') AS DATE),
authority_price = order_header_work.authority_price,
authority_cancel_approval_no = order_header_work.authority_cancel_approval_no,
authority_cancel_date = CAST(NULLIF(order_header_work.authority_cancel_date, '') AS DATE),
credit_payment_no = order_header_work.credit_payment_no,
credit_payment_date = CAST(NULLIF(order_header_work.credit_payment_date, '') AS DATE),
credit_payment_price = order_header_work.credit_payment_price,
credit_cancel_payment_no = order_header_work.credit_cancel_payment_no,
credit_cancel_payment_date = CAST(NULLIF(order_header_work.credit_cancel_payment_date, '') AS DATE),
credit_result_kbn = order_header_work.credit_result_kbn,
card_brand = order_header_work.card_brand,
credit_card_kanri_no = order_header_work.credit_card_kanri_no,
credit_card_kanri_detail_no = order_header_work.credit_card_kanri_detail_no,
credit_card_no = order_header_work.credit_card_no,
credit_card_meigi = order_header_work.credit_card_meigi,
credit_card_valid_year = order_header_work.credit_card_valid_year,
credit_card_valid_month = order_header_work.credit_card_valid_month,
credit_card_pay_count = order_header_work.credit_card_pay_count,
payment_bar_code = order_header_work.payment_bar_code,
amzn_charge_permission_id = order_header_work.amzn_charge_permission_id,
amzn_charge_id = order_header_work.amzn_charge_id,
amzn_charge_status = order_header_work.amzn_charge_status,
amzn_authorization_datetime = CAST(NULLIF(order_header_work.amzn_authorization_datetime, '') AS TIMESTAMP),
amzn_capture_initiated_datetime = CAST(NULLIF(order_header_work.amzn_capture_initiated_datetime, '') AS TIMESTAMP),
amzn_captured_datetime = CAST(NULLIF(order_header_work.amzn_captured_datetime, '') AS TIMESTAMP),
amzn_canceled_datetime = CAST(NULLIF(order_header_work.amzn_canceled_datetime, '') AS TIMESTAMP),
order_user_code = order_header_work.order_user_code,
order_user = order_header_work.order_user,
change_user_code = order_header_work.change_user_code,
change_user = order_header_work.change_user,
demand_kbn = order_header_work.demand_kbn,
demand1_ref_date = CAST(NULLIF(order_header_work.demand1_ref_date, '') AS DATE),
demand1_date = CAST(NULLIF(order_header_work.demand1_date, '') AS DATE),
demand1_limit_date = CAST(NULLIF(order_header_work.demand1_limit_date, '') AS DATE),
demand1_amount = order_header_work.demand1_amount,
demand1_bar_code = order_header_work.demand1_bar_code,
demand2_ref_date = CAST(NULLIF(order_header_work.demand2_ref_date, '') AS DATE),
demand2_date = CAST(NULLIF(order_header_work.demand2_date, '') AS DATE),
demand2_limit_date = CAST(NULLIF(order_header_work.demand2_limit_date, '') AS DATE),
demand2_amount = order_header_work.demand2_amount,
demand2_bar_code = order_header_work.demand2_bar_code,
demand3_ref_date = CAST(NULLIF(order_header_work.demand3_ref_date, '') AS DATE),
demand3_date = CAST(NULLIF(order_header_work.demand3_date, '') AS DATE),
demand3_limit_date = CAST(NULLIF(order_header_work.demand3_limit_date, '') AS DATE),
demand3_amount = order_header_work.demand3_amount,
demand3_bar_code = order_header_work.demand3_bar_code,
kashidaore_date = CAST(NULLIF(order_header_work.kashidaore_date, '') AS DATE),
demand_exclude_reason_kbn = order_header_work.demand_exclude_reason_kbn,
demand_exclude_start_date = CAST(NULLIF(order_header_work.demand_exclude_start_date, '') AS DATE),
demand_exclude_end_date = CAST(NULLIF(order_header_work.demand_exclude_end_date, '') AS DATE),
bill_sei_kj = order_header_work.bill_sei_kj,
bill_mei_kj = order_header_work.bill_mei_kj,
bill_sei_kn = order_header_work.bill_sei_kn,
bill_mei_kn = order_header_work.bill_mei_kn,
bill_tel_no = order_header_work.bill_tel_no,
bill_zipcd = order_header_work.bill_zipcd,
bill_addr1 = order_header_work.bill_addr1,
bill_addr2 = order_header_work.bill_addr2,
bill_addr3 = order_header_work.bill_addr3,
bill_addr4 = order_header_work.bill_addr4,
bill_corporation_post_name = order_header_work.bill_corporation_post_name,
nohinsyo_uketsuke_tanto = order_header_work.nohinsyo_uketsuke_tanto,
grant_plan_point_prod = order_header_work.grant_plan_point_prod,
grant_plan_point_other = order_header_work.grant_plan_point_other,
grant_plan_point_total = order_header_work.grant_plan_point_total,
grant_point_prod = order_header_work.grant_point_prod,
grant_point_other = order_header_work.grant_point_other,
grant_point_total = order_header_work.grant_point_total,
reduction_plan_point_total = order_header_work.reduction_plan_point_total,
reduction_point_total = order_header_work.reduction_point_total,
subtotal_before_campaign = order_header_work.subtotal_before_campaign,
subtotal_after_campaign = order_header_work.subtotal_after_campaign,
total_before_campaign = order_header_work.total_before_campaign,
orm_rowid = order_header_work.orm_rowid,
created_user = order_header_work.created_user,
created_datetime = CAST(NULLIF(order_header_work.created_datetime, '') AS TIMESTAMP),
updated_user = order_header_work.updated_user,
updated_datetime = CAST(NULLIF(order_header_work.updated_datetime, '') AS TIMESTAMP),
dwh_updated_user = order_header_work.dwh_updated_user,
dwh_updated_datetime = order_header_work.dwh_updated_datetime
WHEN NOT MATCHED THEN INSERT VALUES (
order_header_work.order_no,
order_header_work.shop_code,
CAST(NULLIF(order_header_work.order_datetime, '') AS TIMESTAMP),
order_header_work.customer_code,
order_header_work.neo_customer_no,
order_header_work.guest_flg,
order_header_work.last_name,
order_header_work.first_name,
order_header_work.last_name_kana,
order_header_work.first_name_kana,
order_header_work.email,
CAST(NULLIF(order_header_work.birth_date, '') AS DATE),
order_header_work.sex,
order_header_work.postal_code,
order_header_work.prefecture_code,
order_header_work.address1,
order_header_work.address2,
order_header_work.address3,
order_header_work.address4,
order_header_work.corporation_post_name,
order_header_work.phone_number,
order_header_work.advance_later_flg,
order_header_work.payment_method_no,
order_header_work.payment_method_type,
order_header_work.payment_method_name,
order_header_work.ext_payment_method_type,
order_header_work.payment_commission,
order_header_work.payment_commission_tax_gr_code,
order_header_work.payment_commission_tax_no,
order_header_work.payment_commission_tax_rate,
order_header_work.payment_commission_tax,
order_header_work.payment_commission_tax_type,
order_header_work.coupon_management_code,
order_header_work.coupon_code,
order_header_work.coupon_name,
order_header_work.coupon_type,
order_header_work.coupon_use_purchase_price,
order_header_work.coupon_discount_type,
order_header_work.coupon_discount_price,
order_header_work.coupon_discount_rate,
order_header_work.coupon_used_amount,
CAST(NULLIF(order_header_work.coupon_start_datetime, '') AS TIMESTAMP),
CAST(NULLIF(order_header_work.coupon_end_datetime, '') AS TIMESTAMP),
order_header_work.coupon_kbn,
order_header_work.goods_group,
order_header_work.commodity_category_code,
order_header_work.commodity_series,
order_header_work.coupon_commodity_code_display,
order_header_work.baitai_name,
order_header_work.used_point,
order_header_work.total_amount,
order_header_work.ec_promotion_id,
order_header_work.ec_promotion_name,
order_header_work.ec_promotion_discount_price,
order_header_work.ec_campaign_id,
order_header_work.ec_campaign_name,
CAST(NULLIF(order_header_work.payment_date, '') AS DATE),
CAST(NULLIF(order_header_work.payment_limit_date, '') AS DATE),
order_header_work.payment_status,
order_header_work.ext_payment_status,
order_header_work.customer_group_code,
order_header_work.data_transport_status,
order_header_work.order_status,
order_header_work.ext_order_status,
CAST(NULLIF(order_header_work.tax_reference_date, '') AS DATE),
CAST(NULLIF(order_header_work.cancel_date, '') AS DATE),
order_header_work.client_group,
order_header_work.caution,
order_header_work.message,
order_header_work.payment_order_id,
order_header_work.cvs_code,
order_header_work.payment_receipt_no,
order_header_work.payment_receipt_url,
order_header_work.receipt_no,
order_header_work.customer_no,
order_header_work.confirm_no,
order_header_work.career_key,
order_header_work.order_create_error_code,
order_header_work.order_display_status,
order_header_work.order_kind_kbn,
order_header_work.marketing_channel,
order_header_work.original_order_no,
order_header_work.external_order_no,
CAST(NULLIF(order_header_work.order_recieve_datetime, '') AS TIMESTAMP),
CAST(NULLIF(order_header_work.order_update_datetime, '') AS TIMESTAMP),
order_header_work.order_update_reason_kbn,
order_header_work.cancel_reason_kbn,
CAST(NULLIF(order_header_work.uncollectible_date, '') AS DATE),
order_header_work.order_total_price,
order_header_work.account_receivable_balance,
order_header_work.appropriate_amount,
order_header_work.bill_address_kbn,
order_header_work.receipt_flg,
order_header_work.receipt_to,
order_header_work.receipt_detail,
order_header_work.bill_price,
order_header_work.bill_no,
order_header_work.bill_print_count,
order_header_work.authority_result_kbn,
order_header_work.authority_no,
order_header_work.card_password,
order_header_work.authority_approval_no,
CAST(NULLIF(order_header_work.authority_date, '') AS DATE),
order_header_work.authority_price,
order_header_work.authority_cancel_approval_no,
CAST(NULLIF(order_header_work.authority_cancel_date, '') AS DATE),
order_header_work.credit_payment_no,
CAST(NULLIF(order_header_work.credit_payment_date, '') AS DATE),
order_header_work.credit_payment_price,
order_header_work.credit_cancel_payment_no,
CAST(NULLIF(order_header_work.credit_cancel_payment_date, '') AS DATE),
order_header_work.credit_result_kbn,
order_header_work.card_brand,
order_header_work.credit_card_kanri_no,
order_header_work.credit_card_kanri_detail_no,
order_header_work.credit_card_no,
order_header_work.credit_card_meigi,
order_header_work.credit_card_valid_year,
order_header_work.credit_card_valid_month,
order_header_work.credit_card_pay_count,
order_header_work.payment_bar_code,
order_header_work.amzn_charge_permission_id,
order_header_work.amzn_charge_id,
order_header_work.amzn_charge_status,
CAST(NULLIF(order_header_work.amzn_authorization_datetime, '') AS TIMESTAMP),
CAST(NULLIF(order_header_work.amzn_capture_initiated_datetime, '') AS TIMESTAMP),
CAST(NULLIF(order_header_work.amzn_captured_datetime, '') AS TIMESTAMP),
CAST(NULLIF(order_header_work.amzn_canceled_datetime, '') AS TIMESTAMP),
order_header_work.order_user_code,
order_header_work.order_user,
order_header_work.change_user_code,
order_header_work.change_user,
order_header_work.demand_kbn,
CAST(NULLIF(order_header_work.demand1_ref_date, '') AS DATE),
CAST(NULLIF(order_header_work.demand1_date, '') AS DATE),
CAST(NULLIF(order_header_work.demand1_limit_date, '') AS DATE),
order_header_work.demand1_amount,
order_header_work.demand1_bar_code,
CAST(NULLIF(order_header_work.demand2_ref_date, '') AS DATE),
CAST(NULLIF(order_header_work.demand2_date, '') AS DATE),
CAST(NULLIF(order_header_work.demand2_limit_date, '') AS DATE),
order_header_work.demand2_amount,
order_header_work.demand2_bar_code,
CAST(NULLIF(order_header_work.demand3_ref_date, '') AS DATE),
CAST(NULLIF(order_header_work.demand3_date, '') AS DATE),
CAST(NULLIF(order_header_work.demand3_limit_date, '') AS DATE),
order_header_work.demand3_amount,
order_header_work.demand3_bar_code,
CAST(NULLIF(order_header_work.kashidaore_date, '') AS DATE),
order_header_work.demand_exclude_reason_kbn,
CAST(NULLIF(order_header_work.demand_exclude_start_date, '') AS DATE),
CAST(NULLIF(order_header_work.demand_exclude_end_date, '') AS DATE),
order_header_work.bill_sei_kj,
order_header_work.bill_mei_kj,
order_header_work.bill_sei_kn,
order_header_work.bill_mei_kn,
order_header_work.bill_tel_no,
order_header_work.bill_zipcd,
order_header_work.bill_addr1,
order_header_work.bill_addr2,
order_header_work.bill_addr3,
order_header_work.bill_addr4,
order_header_work.bill_corporation_post_name,
order_header_work.nohinsyo_uketsuke_tanto,
order_header_work.grant_plan_point_prod,
order_header_work.grant_plan_point_other,
order_header_work.grant_plan_point_total,
order_header_work.grant_point_prod,
order_header_work.grant_point_other,
order_header_work.grant_point_total,
order_header_work.reduction_plan_point_total,
order_header_work.reduction_point_total,
order_header_work.subtotal_before_campaign,
order_header_work.subtotal_after_campaign,
order_header_work.total_before_campaign,
order_header_work.orm_rowid,
order_header_work.created_user,
CAST(NULLIF(order_header_work.created_datetime, '') AS TIMESTAMP),
order_header_work.updated_user,
CAST(NULLIF(order_header_work.updated_datetime, '') AS TIMESTAMP),
order_header_work.dwh_created_user,
order_header_work.dwh_created_datetime,
order_header_work.dwh_updated_user,
order_header_work.dwh_updated_datetime
);