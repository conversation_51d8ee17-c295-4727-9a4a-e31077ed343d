MERGE INTO dwh_raw.product_linkage USING dwh_raw.product_linkage_work
ON product_linkage.mdm_integration_management_cd = product_linkage_work.mdm_integration_management_cd
WHEN MATCHED THEN UPDATE SET
product_picture_id = product_linkage_work.product_picture_id,
product_no = product_linkage_work.product_no,
mail_order_product_cd = product_linkage_work.mail_order_product_cd,
store_sales_product_cd = product_linkage_work.store_sales_product_cd,
warehouse_management_cd = product_linkage_work.warehouse_management_cd,
jan = product_linkage_work.jan,
jan_issue_flg = product_linkage_work.jan_issue_flg,
main_product_no = product_linkage_work.main_product_no,
core_product_name = product_linkage_work.core_product_name,
web_product_name = product_linkage_work.web_product_name,
product_name = product_linkage_work.product_name,
registration_name = product_linkage_work.registration_name,
law_cat_cd = product_linkage_work.law_cat_cd,
product_segment = product_linkage_work.product_segment,
business_segment = product_linkage_work.business_segment,
product_cat = product_linkage_work.product_cat,
product_series = product_linkage_work.product_series,
sale_start_date = CAST(NULLIF(product_linkage_work.sale_start_date, '') AS TIMESTAMP),
period_set_sales_channel_1 = product_linkage_work.period_set_sales_channel_1,
sales_channel_1_sale_start_date = CAST(NULLIF(product_linkage_work.sales_channel_1_sale_start_date, '') AS TIMESTAMP),
sales_channel_1_sale_end_date = CAST(NULLIF(product_linkage_work.sales_channel_1_sale_end_date, '') AS TIMESTAMP),
period_set_sales_channel_2 = product_linkage_work.period_set_sales_channel_2,
sales_channel_2_sale_start_date = CAST(NULLIF(product_linkage_work.sales_channel_2_sale_start_date, '') AS TIMESTAMP),
sales_channel_2_sale_end_date = CAST(NULLIF(product_linkage_work.sales_channel_2_sale_end_date, '') AS TIMESTAMP),
period_set_sales_channel_3 = product_linkage_work.period_set_sales_channel_3,
sales_channel_3_sale_start_date = CAST(NULLIF(product_linkage_work.sales_channel_3_sale_start_date, '') AS TIMESTAMP),
sales_channel_3_sale_end_date = CAST(NULLIF(product_linkage_work.sales_channel_3_sale_end_date, '') AS TIMESTAMP),
sale_status = product_linkage_work.sale_status,
lgroup = product_linkage_work.lgroup,
mgroup = product_linkage_work.mgroup,
sgroup = product_linkage_work.sgroup,
dgroup = product_linkage_work.dgroup,
product_type = product_linkage_work.product_type,
core_department = product_linkage_work.core_department,
accountin_pattern_gb = product_linkage_work.accountin_pattern_gb,
material = product_linkage_work.material,
preferential_product_flg = product_linkage_work.preferential_product_flg,
set_product_flg = product_linkage_work.set_product_flg,
set_composition_flg = product_linkage_work.set_composition_flg,
company_sales_buy_flg = product_linkage_work.company_sales_buy_flg,
emprate_pms_flg = product_linkage_work.emprate_pms_flg,
age_limit_cd = product_linkage_work.age_limit_cd,
store_po_gb = product_linkage_work.store_po_gb,
web = product_linkage_work.web,
callcenter = product_linkage_work.callcenter,
before_renewal_product_no = product_linkage_work.before_renewal_product_no,
dep = product_linkage_work.dep,
representative_product_cd = product_linkage_work.representative_product_cd,
order_per_order_max = product_linkage_work.order_per_order_max,
buttobi_subsc_bundle_yn = product_linkage_work.buttobi_subsc_bundle_yn,
return_yn = product_linkage_work.return_yn,
exch_yn = product_linkage_work.exch_yn,
lot_management_target_product = product_linkage_work.lot_management_target_product,
reduction_base = product_linkage_work.reduction_base,
depth = product_linkage_work.depth,
width = product_linkage_work.width,
height = product_linkage_work.height,
trade_cnt = product_linkage_work.trade_cnt,
weight = product_linkage_work.weight,
outerbox_depth = product_linkage_work.outerbox_depth,
outerbox_width = product_linkage_work.outerbox_width,
outerbox_height = product_linkage_work.outerbox_height,
outerbox_weight = product_linkage_work.outerbox_weight,
case_per_include_cnt = product_linkage_work.case_per_include_cnt,
insertion_depth = product_linkage_work.insertion_depth,
insertion_width = product_linkage_work.insertion_width,
insertion_height = product_linkage_work.insertion_height,
palette_stack_cnt_face = product_linkage_work.palette_stack_cnt_face,
palette_stack_cnt_lavel = product_linkage_work.palette_stack_cnt_lavel,
contents = product_linkage_work.contents,
nekoposu_volume_rate = product_linkage_work.nekoposu_volume_rate,
outside_home_volume_rate = product_linkage_work.outside_home_volume_rate,
color_name = product_linkage_work.color_name,
color_cd = product_linkage_work.color_cd,
original_color_cd = product_linkage_work.original_color_cd,
size_name = product_linkage_work.size_name,
size_cd = product_linkage_work.size_cd,
shape_name = product_linkage_work.shape_name,
shape_cd = product_linkage_work.shape_cd,
season = product_linkage_work.season,
unit_cd = product_linkage_work.unit_cd,
buy_send_reservation_flg = product_linkage_work.buy_send_reservation_flg,
delivery_notice_undisplay_flg = product_linkage_work.delivery_notice_undisplay_flg,
airmail_rack_yn = product_linkage_work.airmail_rack_yn,
mail_delivery_flg = product_linkage_work.mail_delivery_flg,
outside_home_receive_service_flg = product_linkage_work.outside_home_receive_service_flg,
out_indicate_warehouse = product_linkage_work.out_indicate_warehouse,
warehouse_assembly_set_product_flg = product_linkage_work.warehouse_assembly_set_product_flg,
sagawa_yn_flg = product_linkage_work.sagawa_yn_flg,
folding_flg = product_linkage_work.folding_flg,
vender = product_linkage_work.vender,
use_point_cnt = product_linkage_work.use_point_cnt,
composition_oms_link_flg = product_linkage_work.composition_oms_link_flg,
mdm_integration_management_cd_nk = product_linkage_work.mdm_integration_management_cd_nk,
insert_date = CAST(NULLIF(product_linkage_work.insert_date, '') AS TIMESTAMP),
insert_id = product_linkage_work.insert_id,
modify_date = CAST(NULLIF(product_linkage_work.modify_date, '') AS TIMESTAMP),
modify_id = product_linkage_work.modify_id,
dwh_updated_user = product_linkage_work.dwh_updated_user,
dwh_updated_datetime = product_linkage_work.dwh_updated_datetime
WHEN NOT MATCHED THEN INSERT VALUES (
product_linkage_work.mdm_integration_management_cd,
product_linkage_work.product_picture_id,
product_linkage_work.product_no,
product_linkage_work.mail_order_product_cd,
product_linkage_work.store_sales_product_cd,
product_linkage_work.warehouse_management_cd,
product_linkage_work.jan,
product_linkage_work.jan_issue_flg,
product_linkage_work.main_product_no,
product_linkage_work.core_product_name,
product_linkage_work.web_product_name,
product_linkage_work.product_name,
product_linkage_work.registration_name,
product_linkage_work.law_cat_cd,
product_linkage_work.product_segment,
product_linkage_work.business_segment,
product_linkage_work.product_cat,
product_linkage_work.product_series,
CAST(NULLIF(product_linkage_work.sale_start_date, '') AS TIMESTAMP),
product_linkage_work.period_set_sales_channel_1,
CAST(NULLIF(product_linkage_work.sales_channel_1_sale_start_date, '') AS TIMESTAMP),
CAST(NULLIF(product_linkage_work.sales_channel_1_sale_end_date, '') AS TIMESTAMP),
product_linkage_work.period_set_sales_channel_2,
CAST(NULLIF(product_linkage_work.sales_channel_2_sale_start_date, '') AS TIMESTAMP),
CAST(NULLIF(product_linkage_work.sales_channel_2_sale_end_date, '') AS TIMESTAMP),
product_linkage_work.period_set_sales_channel_3,
CAST(NULLIF(product_linkage_work.sales_channel_3_sale_start_date, '') AS TIMESTAMP),
CAST(NULLIF(product_linkage_work.sales_channel_3_sale_end_date, '') AS TIMESTAMP),
product_linkage_work.sale_status,
product_linkage_work.lgroup,
product_linkage_work.mgroup,
product_linkage_work.sgroup,
product_linkage_work.dgroup,
product_linkage_work.product_type,
product_linkage_work.core_department,
product_linkage_work.accountin_pattern_gb,
product_linkage_work.material,
product_linkage_work.preferential_product_flg,
product_linkage_work.set_product_flg,
product_linkage_work.set_composition_flg,
product_linkage_work.company_sales_buy_flg,
product_linkage_work.emprate_pms_flg,
product_linkage_work.age_limit_cd,
product_linkage_work.store_po_gb,
product_linkage_work.web,
product_linkage_work.callcenter,
product_linkage_work.before_renewal_product_no,
product_linkage_work.dep,
product_linkage_work.representative_product_cd,
product_linkage_work.order_per_order_max,
product_linkage_work.buttobi_subsc_bundle_yn,
product_linkage_work.return_yn,
product_linkage_work.exch_yn,
product_linkage_work.lot_management_target_product,
product_linkage_work.reduction_base,
product_linkage_work.depth,
product_linkage_work.width,
product_linkage_work.height,
product_linkage_work.trade_cnt,
product_linkage_work.weight,
product_linkage_work.outerbox_depth,
product_linkage_work.outerbox_width,
product_linkage_work.outerbox_height,
product_linkage_work.outerbox_weight,
product_linkage_work.case_per_include_cnt,
product_linkage_work.insertion_depth,
product_linkage_work.insertion_width,
product_linkage_work.insertion_height,
product_linkage_work.palette_stack_cnt_face,
product_linkage_work.palette_stack_cnt_lavel,
product_linkage_work.contents,
product_linkage_work.nekoposu_volume_rate,
product_linkage_work.outside_home_volume_rate,
product_linkage_work.color_name,
product_linkage_work.color_cd,
product_linkage_work.original_color_cd,
product_linkage_work.size_name,
product_linkage_work.size_cd,
product_linkage_work.shape_name,
product_linkage_work.shape_cd,
product_linkage_work.season,
product_linkage_work.unit_cd,
product_linkage_work.buy_send_reservation_flg,
product_linkage_work.delivery_notice_undisplay_flg,
product_linkage_work.airmail_rack_yn,
product_linkage_work.mail_delivery_flg,
product_linkage_work.outside_home_receive_service_flg,
product_linkage_work.out_indicate_warehouse,
product_linkage_work.warehouse_assembly_set_product_flg,
product_linkage_work.sagawa_yn_flg,
product_linkage_work.folding_flg,
product_linkage_work.vender,
product_linkage_work.use_point_cnt,
product_linkage_work.composition_oms_link_flg,
product_linkage_work.mdm_integration_management_cd_nk,
CAST(NULLIF(product_linkage_work.insert_date, '') AS TIMESTAMP),
product_linkage_work.insert_id,
CAST(NULLIF(product_linkage_work.modify_date, '') AS TIMESTAMP),
product_linkage_work.modify_id,
product_linkage_work.dwh_created_user,
product_linkage_work.dwh_created_datetime,
product_linkage_work.dwh_updated_user,
product_linkage_work.dwh_updated_datetime
);