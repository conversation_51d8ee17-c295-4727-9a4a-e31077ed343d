MERGE INTO dwh_raw.out_achievements_store_sales USING dwh_raw.out_achievements_store_sales_work
ON out_achievements_store_sales.accept_no = out_achievements_store_sales_work.accept_no AND
 out_achievements_store_sales.close_date = out_achievements_store_sales_work.close_date AND
 out_achievements_store_sales.wh_code = out_achievements_store_sales_work.wh_code AND
 out_achievements_store_sales.goods_code = out_achievements_store_sales_work.goods_code
WHEN MATCHED THEN UPDATE SET
out_qty = out_achievements_store_sales_work.out_qty,
logimane_slip_no = out_achievements_store_sales_work.logimane_slip_no,
dwh_updated_user = out_achievements_store_sales_work.dwh_updated_user,
dwh_updated_datetime = out_achievements_store_sales_work.dwh_updated_datetime
WHEN NOT MATCHED THEN INSERT VALUES (
out_achievements_store_sales_work.accept_no,
CAST(NULLIF(out_achievements_store_sales_work.close_date, '') AS DATE),
out_achievements_store_sales_work.wh_code,
out_achievements_store_sales_work.goods_code,
out_achievements_store_sales_work.out_qty,
out_achievements_store_sales_work.logimane_slip_no,
out_achievements_store_sales_work.dwh_created_user,
out_achievements_store_sales_work.dwh_created_datetime,
out_achievements_store_sales_work.dwh_updated_user,
out_achievements_store_sales_work.dwh_updated_datetime
);