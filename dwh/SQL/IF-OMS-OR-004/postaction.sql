MERGE INTO dwh_raw.regular_sale_cont_header USING dwh_raw.regular_sale_cont_header_work
ON regular_sale_cont_header.regular_contract_no = regular_sale_cont_header_work.regular_contract_no
WHEN MATCHED THEN UPDATE SET
shop_code = regular_sale_cont_header_work.shop_code,
regular_sale_cont_datetime = CAST(NULLIF(regular_sale_cont_header_work.regular_sale_cont_datetime, '') AS TIMESTAMP),
customer_code = regular_sale_cont_header_work.customer_code,
neo_customer_no = regular_sale_cont_header_work.neo_customer_no,
payment_method_no = regular_sale_cont_header_work.payment_method_no,
address_no = regular_sale_cont_header_work.address_no,
regular_sale_cont_status = regular_sale_cont_header_work.regular_sale_cont_status,
next_delivery_request_date = CAST(NULLIF(regular_sale_cont_header_work.next_delivery_request_date, '') AS DATE),
external_order_no = regular_sale_cont_header_work.external_order_no,
order_user_code = regular_sale_cont_header_work.order_user_code,
regular_update_datetime = CAST(NULLIF(regular_sale_cont_header_work.regular_update_datetime, '') AS TIMESTAMP),
change_user_code = regular_sale_cont_header_work.change_user_code,
regular_update_reason_kbn = regular_sale_cont_header_work.regular_update_reason_kbn,
otodoke_hope_time_kbn = regular_sale_cont_header_work.otodoke_hope_time_kbn,
marketing_channel = regular_sale_cont_header_work.marketing_channel,
delivery_type_no = regular_sale_cont_header_work.delivery_type_no,
shipping_method_flg = regular_sale_cont_header_work.shipping_method_flg,
ext_payment_method_type = regular_sale_cont_header_work.ext_payment_method_type,
card_brand = regular_sale_cont_header_work.card_brand,
credit_card_kanri_no = regular_sale_cont_header_work.credit_card_kanri_no,
credit_card_kanri_detail_no = regular_sale_cont_header_work.credit_card_kanri_detail_no,
credit_card_no = regular_sale_cont_header_work.credit_card_no,
credit_card_meigi = regular_sale_cont_header_work.credit_card_meigi,
credit_card_valid_year = regular_sale_cont_header_work.credit_card_valid_year,
credit_card_valid_month = regular_sale_cont_header_work.credit_card_valid_month,
credit_card_pay_count = regular_sale_cont_header_work.credit_card_pay_count,
amzn_charge_permission_id = regular_sale_cont_header_work.amzn_charge_permission_id,
bill_address_kbn = regular_sale_cont_header_work.bill_address_kbn,
bill_print_otodoke_id = regular_sale_cont_header_work.bill_print_otodoke_id,
o_name_disp_kbn = regular_sale_cont_header_work.o_name_disp_kbn,
delivery_note_flg = regular_sale_cont_header_work.delivery_note_flg,
include_flg = regular_sale_cont_header_work.include_flg,
receipt_flg = regular_sale_cont_header_work.receipt_flg,
receipt_to = regular_sale_cont_header_work.receipt_to,
receipt_detail = regular_sale_cont_header_work.receipt_detail,
first_shipping_date = CAST(NULLIF(regular_sale_cont_header_work.first_shipping_date, '') AS DATE),
lastest_shipping_date = CAST(NULLIF(regular_sale_cont_header_work.lastest_shipping_date, '') AS DATE),
first_delivery_date = CAST(NULLIF(regular_sale_cont_header_work.first_delivery_date, '') AS DATE),
lastest_delivery_date = CAST(NULLIF(regular_sale_cont_header_work.lastest_delivery_date, '') AS DATE),
regular_stop_date = CAST(NULLIF(regular_sale_cont_header_work.regular_stop_date, '') AS DATE),
regular_stop_reason_kbn = regular_sale_cont_header_work.regular_stop_reason_kbn,
regular_hold_date = CAST(NULLIF(regular_sale_cont_header_work.regular_hold_date, '') AS DATE),
regular_hold_clear_date = CAST(NULLIF(regular_sale_cont_header_work.regular_hold_clear_date, '') AS DATE),
regular_kaiji = regular_sale_cont_header_work.regular_kaiji,
shipped_regular_count = regular_sale_cont_header_work.shipped_regular_count,
delivery_memo = regular_sale_cont_header_work.delivery_memo,
regular_hold_reason_kbn = regular_sale_cont_header_work.regular_hold_reason_kbn,
niyose_flg = regular_sale_cont_header_work.niyose_flg,
orm_rowid = regular_sale_cont_header_work.orm_rowid,
created_user = regular_sale_cont_header_work.created_user,
created_datetime = CAST(NULLIF(regular_sale_cont_header_work.created_datetime, '') AS TIMESTAMP),
updated_user = regular_sale_cont_header_work.updated_user,
updated_datetime = CAST(NULLIF(regular_sale_cont_header_work.updated_datetime, '') AS TIMESTAMP),
dwh_updated_user = regular_sale_cont_header_work.dwh_updated_user,
dwh_updated_datetime = regular_sale_cont_header_work.dwh_updated_datetime
WHEN NOT MATCHED THEN INSERT VALUES (
regular_sale_cont_header_work.regular_contract_no,
regular_sale_cont_header_work.shop_code,
CAST(NULLIF(regular_sale_cont_header_work.regular_sale_cont_datetime, '') AS TIMESTAMP),
regular_sale_cont_header_work.customer_code,
regular_sale_cont_header_work.neo_customer_no,
regular_sale_cont_header_work.payment_method_no,
regular_sale_cont_header_work.address_no,
regular_sale_cont_header_work.regular_sale_cont_status,
CAST(NULLIF(regular_sale_cont_header_work.next_delivery_request_date, '') AS DATE),
regular_sale_cont_header_work.external_order_no,
regular_sale_cont_header_work.order_user_code,
CAST(NULLIF(regular_sale_cont_header_work.regular_update_datetime, '') AS TIMESTAMP),
regular_sale_cont_header_work.change_user_code,
regular_sale_cont_header_work.regular_update_reason_kbn,
regular_sale_cont_header_work.otodoke_hope_time_kbn,
regular_sale_cont_header_work.marketing_channel,
regular_sale_cont_header_work.delivery_type_no,
regular_sale_cont_header_work.shipping_method_flg,
regular_sale_cont_header_work.ext_payment_method_type,
regular_sale_cont_header_work.card_brand,
regular_sale_cont_header_work.credit_card_kanri_no,
regular_sale_cont_header_work.credit_card_kanri_detail_no,
regular_sale_cont_header_work.credit_card_no,
regular_sale_cont_header_work.credit_card_meigi,
regular_sale_cont_header_work.credit_card_valid_year,
regular_sale_cont_header_work.credit_card_valid_month,
regular_sale_cont_header_work.credit_card_pay_count,
regular_sale_cont_header_work.amzn_charge_permission_id,
regular_sale_cont_header_work.bill_address_kbn,
regular_sale_cont_header_work.bill_print_otodoke_id,
regular_sale_cont_header_work.o_name_disp_kbn,
regular_sale_cont_header_work.delivery_note_flg,
regular_sale_cont_header_work.include_flg,
regular_sale_cont_header_work.receipt_flg,
regular_sale_cont_header_work.receipt_to,
regular_sale_cont_header_work.receipt_detail,
CAST(NULLIF(regular_sale_cont_header_work.first_shipping_date, '') AS DATE),
CAST(NULLIF(regular_sale_cont_header_work.lastest_shipping_date, '') AS DATE),
CAST(NULLIF(regular_sale_cont_header_work.first_delivery_date, '') AS DATE),
CAST(NULLIF(regular_sale_cont_header_work.lastest_delivery_date, '') AS DATE),
CAST(NULLIF(regular_sale_cont_header_work.regular_stop_date, '') AS DATE),
regular_sale_cont_header_work.regular_stop_reason_kbn,
CAST(NULLIF(regular_sale_cont_header_work.regular_hold_date, '') AS DATE),
CAST(NULLIF(regular_sale_cont_header_work.regular_hold_clear_date, '') AS DATE),
regular_sale_cont_header_work.regular_kaiji,
regular_sale_cont_header_work.shipped_regular_count,
regular_sale_cont_header_work.delivery_memo,
regular_sale_cont_header_work.regular_hold_reason_kbn,
regular_sale_cont_header_work.niyose_flg,
regular_sale_cont_header_work.orm_rowid,
regular_sale_cont_header_work.created_user,
CAST(NULLIF(regular_sale_cont_header_work.created_datetime, '') AS TIMESTAMP),
regular_sale_cont_header_work.updated_user,
CAST(NULLIF(regular_sale_cont_header_work.updated_datetime, '') AS TIMESTAMP),
regular_sale_cont_header_work.dwh_created_user,
regular_sale_cont_header_work.dwh_created_datetime,
regular_sale_cont_header_work.dwh_updated_user,
regular_sale_cont_header_work.dwh_updated_datetime
);