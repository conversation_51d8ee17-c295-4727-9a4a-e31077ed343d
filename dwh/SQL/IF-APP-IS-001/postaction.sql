MERGE INTO dwh_raw.arrival_schedule_apparel USING dwh_raw.arrival_schedule_apparel_work
ON arrival_schedule_apparel.po_no = arrival_schedule_apparel_work.po_no AND
 arrival_schedule_apparel.warehouse_management_no = arrival_schedule_apparel_work.warehouse_management_no
WHEN MATCHED THEN UPDATE SET
stock_arrival_date = CAST(NULLIF(arrival_schedule_apparel_work.stock_arrival_date, '') AS DATE),
warehouse_cd = arrival_schedule_apparel_work.warehouse_cd,
order_count = arrival_schedule_apparel_work.order_count,
agent_cd = arrival_schedule_apparel_work.agent_cd,
order_registrant_name = arrival_schedule_apparel_work.order_registrant_name,
comment_code = arrival_schedule_apparel_work.comment_code,
dwh_updated_user = arrival_schedule_apparel_work.dwh_updated_user,
dwh_updated_datetime = arrival_schedule_apparel_work.dwh_updated_datetime
WHEN NOT MATCHED THEN INSERT VALUES (
CAST(NULLIF(arrival_schedule_apparel_work.stock_arrival_date, '') AS DATE),
arrival_schedule_apparel_work.warehouse_cd,
arrival_schedule_apparel_work.po_no,
arrival_schedule_apparel_work.warehouse_management_no,
arrival_schedule_apparel_work.order_count,
arrival_schedule_apparel_work.agent_cd,
arrival_schedule_apparel_work.order_registrant_name,
arrival_schedule_apparel_work.comment_code,
arrival_schedule_apparel_work.dwh_created_user,
arrival_schedule_apparel_work.dwh_created_datetime,
arrival_schedule_apparel_work.dwh_updated_user,
arrival_schedule_apparel_work.dwh_updated_datetime
);