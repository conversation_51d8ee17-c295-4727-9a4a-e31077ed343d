MERGE INTO dwh_raw.address_item_code_linkage USING dwh_raw.address_item_code_linkage_work
ON address_item_code_linkage.mdm_integration_management_cd = address_item_code_linkage_work.mdm_integration_management_cd AND
 address_item_code_linkage.address_item_code = address_item_code_linkage_work.address_item_code
WHEN MATCHED THEN UPDATE SET
mdm_integration_management_cd_nk = address_item_code_linkage_work.mdm_integration_management_cd_nk,
insert_date = CAST(NULLIF(address_item_code_linkage_work.insert_date, '') AS TIMESTAMP),
insert_id = address_item_code_linkage_work.insert_id,
modify_date = CAST(NULLIF(address_item_code_linkage_work.modify_date, '') AS TIMESTAMP),
modify_id = address_item_code_linkage_work.modify_id,
dwh_updated_user = address_item_code_linkage_work.dwh_updated_user,
dwh_updated_datetime = address_item_code_linkage_work.dwh_updated_datetime
WHEN NOT MATCHED THEN INSERT VALUES (
address_item_code_linkage_work.mdm_integration_management_cd,
address_item_code_linkage_work.address_item_code,
address_item_code_linkage_work.mdm_integration_management_cd_nk,
CAST(NULLIF(address_item_code_linkage_work.insert_date, '') AS TIMESTAMP),
address_item_code_linkage_work.insert_id,
CAST(NULLIF(address_item_code_linkage_work.modify_date, '') AS TIMESTAMP),
address_item_code_linkage_work.modify_id,
address_item_code_linkage_work.dwh_created_user,
address_item_code_linkage_work.dwh_created_datetime,
address_item_code_linkage_work.dwh_updated_user,
address_item_code_linkage_work.dwh_updated_datetime
);