MERGE INTO dwh_raw.sales_info_alignment_payment USING dwh_raw.sales_info_alignment_payment_work
ON sales_info_alignment_payment.shop_cd = sales_info_alignment_payment_work.shop_cd AND
 sales_info_alignment_payment.register_num = sales_info_alignment_payment_work.register_num AND
 sales_info_alignment_payment.business_date = sales_info_alignment_payment_work.business_date AND
 sales_info_alignment_payment.receipt_num = sales_info_alignment_payment_work.receipt_num AND
 sales_info_alignment_payment.line_num = sales_info_alignment_payment_work.line_num
WHEN MATCHED THEN UPDATE SET
pay_kind = sales_info_alignment_payment_work.pay_kind,
pay_cd = sales_info_alignment_payment_work.pay_cd,
credit_linkage_kind = sales_info_alignment_payment_work.credit_linkage_kind,
claim_kind = sales_info_alignment_payment_work.claim_kind,
pay_amount = sales_info_alignment_payment_work.pay_amount,
change_amount = sales_info_alignment_payment_work.change_amount,
surplus_amount = sales_info_alignment_payment_work.surplus_amount,
coupon_num = sales_info_alignment_payment_work.coupon_num,
ticket_quantity = sales_info_alignment_payment_work.ticket_quantity,
ticket_bar_cd = sales_info_alignment_payment_work.ticket_bar_cd,
is_worn_cash = sales_info_alignment_payment_work.is_worn_cash,
is_emoney_cash_back = sales_info_alignment_payment_work.is_emoney_cash_back,
numeric_reserve1 = sales_info_alignment_payment_work.numeric_reserve1,
numeric_reserve2 = sales_info_alignment_payment_work.numeric_reserve2,
numeric_reserve3 = sales_info_alignment_payment_work.numeric_reserve3,
numeric_reserve4 = sales_info_alignment_payment_work.numeric_reserve4,
numeric_reserve5 = sales_info_alignment_payment_work.numeric_reserve5,
numeric_reserve6 = sales_info_alignment_payment_work.numeric_reserve6,
numeric_reserve7 = sales_info_alignment_payment_work.numeric_reserve7,
numeric_reserve8 = sales_info_alignment_payment_work.numeric_reserve8,
numeric_reserve9 = sales_info_alignment_payment_work.numeric_reserve9,
numeric_reserve10 = sales_info_alignment_payment_work.numeric_reserve10,
string_reserve1 = sales_info_alignment_payment_work.string_reserve1,
string_reserve2 = sales_info_alignment_payment_work.string_reserve2,
string_reserve3 = sales_info_alignment_payment_work.string_reserve3,
string_reserve4 = sales_info_alignment_payment_work.string_reserve4,
string_reserve5 = sales_info_alignment_payment_work.string_reserve5,
string_reserve6 = sales_info_alignment_payment_work.string_reserve6,
string_reserve7 = sales_info_alignment_payment_work.string_reserve7,
string_reserve8 = sales_info_alignment_payment_work.string_reserve8,
string_reserve9 = sales_info_alignment_payment_work.string_reserve9,
string_reserve10 = sales_info_alignment_payment_work.string_reserve10,
dwh_updated_user = sales_info_alignment_payment_work.dwh_updated_user,
dwh_updated_datetime = sales_info_alignment_payment_work.dwh_updated_datetime
WHEN NOT MATCHED THEN INSERT VALUES (
sales_info_alignment_payment_work.shop_cd,
sales_info_alignment_payment_work.register_num,
CAST(NULLIF(sales_info_alignment_payment_work.business_date, '') AS DATE),
sales_info_alignment_payment_work.receipt_num,
sales_info_alignment_payment_work.line_num,
sales_info_alignment_payment_work.pay_kind,
sales_info_alignment_payment_work.pay_cd,
sales_info_alignment_payment_work.credit_linkage_kind,
sales_info_alignment_payment_work.claim_kind,
sales_info_alignment_payment_work.pay_amount,
sales_info_alignment_payment_work.change_amount,
sales_info_alignment_payment_work.surplus_amount,
sales_info_alignment_payment_work.coupon_num,
sales_info_alignment_payment_work.ticket_quantity,
sales_info_alignment_payment_work.ticket_bar_cd,
sales_info_alignment_payment_work.is_worn_cash,
sales_info_alignment_payment_work.is_emoney_cash_back,
sales_info_alignment_payment_work.numeric_reserve1,
sales_info_alignment_payment_work.numeric_reserve2,
sales_info_alignment_payment_work.numeric_reserve3,
sales_info_alignment_payment_work.numeric_reserve4,
sales_info_alignment_payment_work.numeric_reserve5,
sales_info_alignment_payment_work.numeric_reserve6,
sales_info_alignment_payment_work.numeric_reserve7,
sales_info_alignment_payment_work.numeric_reserve8,
sales_info_alignment_payment_work.numeric_reserve9,
sales_info_alignment_payment_work.numeric_reserve10,
sales_info_alignment_payment_work.string_reserve1,
sales_info_alignment_payment_work.string_reserve2,
sales_info_alignment_payment_work.string_reserve3,
sales_info_alignment_payment_work.string_reserve4,
sales_info_alignment_payment_work.string_reserve5,
sales_info_alignment_payment_work.string_reserve6,
sales_info_alignment_payment_work.string_reserve7,
sales_info_alignment_payment_work.string_reserve8,
sales_info_alignment_payment_work.string_reserve9,
sales_info_alignment_payment_work.string_reserve10,
sales_info_alignment_payment_work.dwh_created_user,
sales_info_alignment_payment_work.dwh_created_datetime,
sales_info_alignment_payment_work.dwh_updated_user,
sales_info_alignment_payment_work.dwh_updated_datetime
);