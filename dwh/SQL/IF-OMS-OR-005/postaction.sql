MERGE INTO dwh_raw.regular_sale_cont_detail USING dwh_raw.regular_sale_cont_detail_work
ON regular_sale_cont_detail.regular_contract_no = regular_sale_cont_detail_work.regular_contract_no AND
 regular_sale_cont_detail.regular_contract_detail_no = regular_sale_cont_detail_work.regular_contract_detail_no
WHEN MATCHED THEN UPDATE SET
shop_code = regular_sale_cont_detail_work.shop_code,
sku_code = regular_sale_cont_detail_work.sku_code,
commodity_code = regular_sale_cont_detail_work.commodity_code,
contract_amount = regular_sale_cont_detail_work.contract_amount,
commodity_name = regular_sale_cont_detail_work.commodity_name,
commodity_subcategory_code = regular_sale_cont_detail_work.commodity_subcategory_code,
commodity_subcategory_code_name = regular_sale_cont_detail_work.commodity_subcategory_code_name,
baitai_code = regular_sale_cont_detail_work.baitai_code,
regular_cycle_delivery_kbn = regular_sale_cont_detail_work.regular_cycle_delivery_kbn,
regular_cycle_kijun_date = CAST(NULLIF(regular_sale_cont_detail_work.regular_cycle_kijun_date, '') AS DATE),
regular_kind = regular_sale_cont_detail_work.regular_kind,
regular_cycle_day_int = regular_sale_cont_detail_work.regular_cycle_day_int,
regular_cycle_day = regular_sale_cont_detail_work.regular_cycle_day,
regular_cycle_mon_interval = regular_sale_cont_detail_work.regular_cycle_mon_interval,
regular_cycle_mon_interval_day = regular_sale_cont_detail_work.regular_cycle_mon_interval_day,
regular_cycle_week_num = regular_sale_cont_detail_work.regular_cycle_week_num,
regular_cycle_week_kbn = regular_sale_cont_detail_work.regular_cycle_week_kbn,
regular_cycle_week_mon = regular_sale_cont_detail_work.regular_cycle_week_mon,
regular_cycle_week_tue = regular_sale_cont_detail_work.regular_cycle_week_tue,
regular_cycle_week_wed = regular_sale_cont_detail_work.regular_cycle_week_wed,
regular_cycle_week_thu = regular_sale_cont_detail_work.regular_cycle_week_thu,
regular_cycle_week_fri = regular_sale_cont_detail_work.regular_cycle_week_fri,
regular_cycle_week_sat = regular_sale_cont_detail_work.regular_cycle_week_sat,
regular_cycle_week_sun = regular_sale_cont_detail_work.regular_cycle_week_sun,
regular_cycle_week_hol = regular_sale_cont_detail_work.regular_cycle_week_hol,
cycle_disp_name = regular_sale_cont_detail_work.cycle_disp_name,
next_shipping_plan_date = CAST(NULLIF(regular_sale_cont_detail_work.next_shipping_plan_date, '') AS DATE),
next_shipping_date = CAST(NULLIF(regular_sale_cont_detail_work.next_shipping_date, '') AS DATE),
next_delivery_plan_date = CAST(NULLIF(regular_sale_cont_detail_work.next_delivery_plan_date, '') AS DATE),
next_delivery_date = CAST(NULLIF(regular_sale_cont_detail_work.next_delivery_date, '') AS DATE),
lastest_delivery_date = CAST(NULLIF(regular_sale_cont_detail_work.lastest_delivery_date, '') AS DATE),
regular_kaiji = regular_sale_cont_detail_work.regular_kaiji,
shipped_regular_count = regular_sale_cont_detail_work.shipped_regular_count,
regular_sale_stop_from = regular_sale_cont_detail_work.regular_sale_stop_from,
regular_sale_stop_to = regular_sale_cont_detail_work.regular_sale_stop_to,
hasso_souko_cd = regular_sale_cont_detail_work.hasso_souko_cd,
shipping_area = regular_sale_cont_detail_work.shipping_area,
regular_check_memo = regular_sale_cont_detail_work.regular_check_memo,
regular_memo_hold_flg = regular_sale_cont_detail_work.regular_memo_hold_flg,
souko_shiji = regular_sale_cont_detail_work.souko_shiji,
next_regular_sale_stop_status = regular_sale_cont_detail_work.next_regular_sale_stop_status,
regular_stop_reason_kbn = regular_sale_cont_detail_work.regular_stop_reason_kbn,
orm_rowid = regular_sale_cont_detail_work.orm_rowid,
created_user = regular_sale_cont_detail_work.created_user,
created_datetime = CAST(NULLIF(regular_sale_cont_detail_work.created_datetime, '') AS TIMESTAMP),
updated_user = regular_sale_cont_detail_work.updated_user,
updated_datetime = CAST(NULLIF(regular_sale_cont_detail_work.updated_datetime, '') AS TIMESTAMP),
dwh_updated_user = regular_sale_cont_detail_work.dwh_updated_user,
dwh_updated_datetime = regular_sale_cont_detail_work.dwh_updated_datetime
WHEN NOT MATCHED THEN INSERT VALUES (
regular_sale_cont_detail_work.regular_contract_no,
regular_sale_cont_detail_work.regular_contract_detail_no,
regular_sale_cont_detail_work.shop_code,
regular_sale_cont_detail_work.sku_code,
regular_sale_cont_detail_work.commodity_code,
regular_sale_cont_detail_work.contract_amount,
regular_sale_cont_detail_work.commodity_name,
regular_sale_cont_detail_work.commodity_subcategory_code,
regular_sale_cont_detail_work.commodity_subcategory_code_name,
regular_sale_cont_detail_work.baitai_code,
regular_sale_cont_detail_work.regular_cycle_delivery_kbn,
CAST(NULLIF(regular_sale_cont_detail_work.regular_cycle_kijun_date, '') AS DATE),
regular_sale_cont_detail_work.regular_kind,
regular_sale_cont_detail_work.regular_cycle_day_int,
regular_sale_cont_detail_work.regular_cycle_day,
regular_sale_cont_detail_work.regular_cycle_mon_interval,
regular_sale_cont_detail_work.regular_cycle_mon_interval_day,
regular_sale_cont_detail_work.regular_cycle_week_num,
regular_sale_cont_detail_work.regular_cycle_week_kbn,
regular_sale_cont_detail_work.regular_cycle_week_mon,
regular_sale_cont_detail_work.regular_cycle_week_tue,
regular_sale_cont_detail_work.regular_cycle_week_wed,
regular_sale_cont_detail_work.regular_cycle_week_thu,
regular_sale_cont_detail_work.regular_cycle_week_fri,
regular_sale_cont_detail_work.regular_cycle_week_sat,
regular_sale_cont_detail_work.regular_cycle_week_sun,
regular_sale_cont_detail_work.regular_cycle_week_hol,
regular_sale_cont_detail_work.cycle_disp_name,
CAST(NULLIF(regular_sale_cont_detail_work.next_shipping_plan_date, '') AS DATE),
CAST(NULLIF(regular_sale_cont_detail_work.next_shipping_date, '') AS DATE),
CAST(NULLIF(regular_sale_cont_detail_work.next_delivery_plan_date, '') AS DATE),
CAST(NULLIF(regular_sale_cont_detail_work.next_delivery_date, '') AS DATE),
CAST(NULLIF(regular_sale_cont_detail_work.lastest_delivery_date, '') AS DATE),
regular_sale_cont_detail_work.regular_kaiji,
regular_sale_cont_detail_work.shipped_regular_count,
regular_sale_cont_detail_work.regular_sale_stop_from,
regular_sale_cont_detail_work.regular_sale_stop_to,
regular_sale_cont_detail_work.hasso_souko_cd,
regular_sale_cont_detail_work.shipping_area,
regular_sale_cont_detail_work.regular_check_memo,
regular_sale_cont_detail_work.regular_memo_hold_flg,
regular_sale_cont_detail_work.souko_shiji,
regular_sale_cont_detail_work.next_regular_sale_stop_status,
regular_sale_cont_detail_work.regular_stop_reason_kbn,
regular_sale_cont_detail_work.orm_rowid,
regular_sale_cont_detail_work.created_user,
CAST(NULLIF(regular_sale_cont_detail_work.created_datetime, '') AS TIMESTAMP),
regular_sale_cont_detail_work.updated_user,
CAST(NULLIF(regular_sale_cont_detail_work.updated_datetime, '') AS TIMESTAMP),
regular_sale_cont_detail_work.dwh_created_user,
regular_sale_cont_detail_work.dwh_created_datetime,
regular_sale_cont_detail_work.dwh_updated_user,
regular_sale_cont_detail_work.dwh_updated_datetime
);