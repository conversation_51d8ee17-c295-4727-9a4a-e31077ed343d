MERGE INTO dwh_raw.coupon_commodity USING dwh_raw.coupon_commodity_work
ON coupon_commodity.coupon_management_code = coupon_commodity_work.coupon_management_code AND
 coupon_commodity.shop_code = coupon_commodity_work.shop_code AND
 coupon_commodity.commodity_code = coupon_commodity_work.commodity_code
WHEN MATCHED THEN UPDATE SET
orm_rowid = coupon_commodity_work.orm_rowid,
created_user = coupon_commodity_work.created_user,
created_datetime = CAST(NULLIF(coupon_commodity_work.created_datetime, '') AS TIMESTAMP),
updated_user = coupon_commodity_work.updated_user,
updated_datetime = CAST(NULLIF(coupon_commodity_work.updated_datetime, '') AS TIMESTAMP),
dwh_updated_user = coupon_commodity_work.dwh_updated_user,
dwh_updated_datetime = coupon_commodity_work.dwh_updated_datetime
WHEN NOT MATCHED THEN INSERT VALUES (
coupon_commodity_work.coupon_management_code,
coupon_commodity_work.shop_code,
coupon_commodity_work.commodity_code,
coupon_commodity_work.orm_rowid,
coupon_commodity_work.created_user,
CAST(NULLIF(coupon_commodity_work.created_datetime, '') AS TIMESTAMP),
coupon_commodity_work.updated_user,
CAST(NULLIF(coupon_commodity_work.updated_datetime, '') AS TIMESTAMP),
coupon_commodity_work.dwh_created_user,
coupon_commodity_work.dwh_created_datetime,
coupon_commodity_work.dwh_updated_user,
coupon_commodity_work.dwh_updated_datetime
);