MERGE INTO dwh_raw.order_payment_mng USING dwh_raw.order_payment_mng_work
ON order_payment_mng.order_no = order_payment_mng_work.order_no AND
 order_payment_mng.order_payment_no = order_payment_mng_work.order_payment_no
WHEN MATCHED THEN UPDATE SET
payment_type = order_payment_mng_work.payment_type,
payment_identify_code = order_payment_mng_work.payment_identify_code,
payment_process_type = order_payment_mng_work.payment_process_type,
payment_process_status = order_payment_mng_work.payment_process_status,
payment_process_price = order_payment_mng_work.payment_process_price,
payment_process_datetime = CAST(NULLIF(order_payment_mng_work.payment_process_datetime, '') AS TIMESTAMP),
payment_process_send_content = order_payment_mng_work.payment_process_send_content,
payment_process_receive_content = order_payment_mng_work.payment_process_receive_content,
henpin_request_no = order_payment_mng_work.henpin_request_no,
orm_rowid = order_payment_mng_work.orm_rowid,
created_user = order_payment_mng_work.created_user,
created_datetime = CAST(NULLIF(order_payment_mng_work.created_datetime, '') AS TIMESTAMP),
updated_user = order_payment_mng_work.updated_user,
updated_datetime = CAST(NULLIF(order_payment_mng_work.updated_datetime, '') AS TIMESTAMP),
dwh_updated_user = order_payment_mng_work.dwh_updated_user,
dwh_updated_datetime = order_payment_mng_work.dwh_updated_datetime
WHEN NOT MATCHED THEN INSERT VALUES (
order_payment_mng_work.order_no,
order_payment_mng_work.order_payment_no,
order_payment_mng_work.payment_type,
order_payment_mng_work.payment_identify_code,
order_payment_mng_work.payment_process_type,
order_payment_mng_work.payment_process_status,
order_payment_mng_work.payment_process_price,
CAST(NULLIF(order_payment_mng_work.payment_process_datetime, '') AS TIMESTAMP),
order_payment_mng_work.payment_process_send_content,
order_payment_mng_work.payment_process_receive_content,
order_payment_mng_work.henpin_request_no,
order_payment_mng_work.orm_rowid,
order_payment_mng_work.created_user,
CAST(NULLIF(order_payment_mng_work.created_datetime, '') AS TIMESTAMP),
order_payment_mng_work.updated_user,
CAST(NULLIF(order_payment_mng_work.updated_datetime, '') AS TIMESTAMP),
order_payment_mng_work.dwh_created_user,
order_payment_mng_work.dwh_created_datetime,
order_payment_mng_work.dwh_updated_user,
order_payment_mng_work.dwh_updated_datetime
);