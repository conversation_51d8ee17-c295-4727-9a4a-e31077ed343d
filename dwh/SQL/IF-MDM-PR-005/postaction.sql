MERGE INTO dwh_raw.business_segment USING dwh_raw.business_segment_work
ON business_segment.business_segment = business_segment_work.business_segment
WHEN MATCHED THEN UPDATE SET
business_segment_name = business_segment_work.business_segment_name,
business_segment_nk = business_segment_work.business_segment_nk,
insert_date = CAST(NULLIF(business_segment_work.insert_date, '') AS TIMESTAMP),
insert_id = business_segment_work.insert_id,
modify_date = CAST(NULLIF(business_segment_work.modify_date, '') AS TIMESTAMP),
modify_id = business_segment_work.modify_id,
dwh_updated_user = business_segment_work.dwh_updated_user,
dwh_updated_datetime = business_segment_work.dwh_updated_datetime
WHEN NOT MATCHED THEN INSERT VALUES (
business_segment_work.business_segment,
business_segment_work.business_segment_name,
business_segment_work.business_segment_nk,
CAST(NULLIF(business_segment_work.insert_date, '') AS TIMESTAMP),
business_segment_work.insert_id,
CAST(NULLIF(business_segment_work.modify_date, '') AS TIMESTAMP),
business_segment_work.modify_id,
business_segment_work.dwh_created_user,
business_segment_work.dwh_created_datetime,
business_segment_work.dwh_updated_user,
business_segment_work.dwh_updated_datetime
);