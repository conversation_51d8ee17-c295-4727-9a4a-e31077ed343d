INSERT INTO dwh_raw.secure_move (
corp_cd,
stock_io_type,
move_date,
center_code,
stock_group_code,
shop_code_swh,
sh_control_number,
move_quantity,
move_center_code,
move_stock_group_code,
move_shop_code_swh,
upd_user_id,
data_date,
dwh_created_user,
dwh_created_datetime,
dwh_updated_user,
dwh_updated_datetime
)
SELECT
corp_cd,
stock_io_type,
CAST(NULLIF(move_date, '') AS DATE),
center_code,
stock_group_code,
shop_code_swh,
sh_control_number,
move_quantity,
move_center_code,
move_stock_group_code,
move_shop_code_swh,
upd_user_id,
CAST(NULLIF(data_date, '') AS TIMESTAMP),
dwh_created_user,
dwh_created_datetime,
dwh_updated_user,
dwh_updated_datetime
FROM dwh_raw.secure_move_work;
