MERGE INTO dwh_raw.sgroup USING dwh_raw.sgroup_work
ON sgroup.lgroup = sgroup_work.lgroup AND
 sgroup.mgroup = sgroup_work.mgroup AND
 sgroup.sgroup = sgroup_work.sgroup
WHEN MATCHED THEN UPDATE SET
sgroup_name = sgroup_work.sgroup_name,
lgroup_nk = sgroup_work.lgroup_nk,
insert_date = CAST(NULLIF(sgroup_work.insert_date, '') AS TIMESTAMP),
insert_id = sgroup_work.insert_id,
modify_date = CAST(NULLIF(sgroup_work.modify_date, '') AS TIMESTAMP),
modify_id = sgroup_work.modify_id,
dwh_updated_user = sgroup_work.dwh_updated_user,
dwh_updated_datetime = sgroup_work.dwh_updated_datetime
WHEN NOT MATCHED THEN INSERT VALUES (
sgroup_work.lgroup,
sgroup_work.mgroup,
sgroup_work.sgroup,
sgroup_work.sgroup_name,
sgroup_work.lgroup_nk,
CAST(NULLIF(sgroup_work.insert_date, '') AS TIMESTAMP),
sgroup_work.insert_id,
CAST(NULLIF(sgroup_work.modify_date, '') AS TIMESTAMP),
sgroup_work.modify_id,
sgroup_work.dwh_created_user,
sgroup_work.dwh_created_datetime,
sgroup_work.dwh_updated_user,
sgroup_work.dwh_updated_datetime
);