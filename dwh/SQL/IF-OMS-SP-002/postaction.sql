MERGE INTO dwh_raw.shipping_header USING dwh_raw.shipping_header_work
ON shipping_header.shipping_no = shipping_header_work.shipping_no
WHEN MATCHED THEN UPDATE SET
order_no = shipping_header_work.order_no,
shop_code = shipping_header_work.shop_code,
customer_code = shipping_header_work.customer_code,
neo_customer_no = shipping_header_work.neo_customer_no,
address_no = shipping_header_work.address_no,
address_last_name = shipping_header_work.address_last_name,
address_first_name = shipping_header_work.address_first_name,
address_last_name_kana = shipping_header_work.address_last_name_kana,
address_first_name_kana = shipping_header_work.address_first_name_kana,
postal_code = shipping_header_work.postal_code,
prefecture_code = shipping_header_work.prefecture_code,
address1 = shipping_header_work.address1,
address2 = shipping_header_work.address2,
address3 = shipping_header_work.address3,
address4 = shipping_header_work.address4,
corporation_post_name = shipping_header_work.corporation_post_name,
phone_number = shipping_header_work.phone_number,
delivery_remark = shipping_header_work.delivery_remark,
acquired_point = shipping_header_work.acquired_point,
delivery_slip_no = shipping_header_work.delivery_slip_no,
shipping_charge = shipping_header_work.shipping_charge,
shipping_charge_tax_type = shipping_header_work.shipping_charge_tax_type,
shipping_charge_tax_group_code = shipping_header_work.shipping_charge_tax_group_code,
shipping_charge_tax_no = shipping_header_work.shipping_charge_tax_no,
shipping_charge_tax_rate = shipping_header_work.shipping_charge_tax_rate,
shipping_charge_tax = shipping_header_work.shipping_charge_tax,
delivery_type_no = shipping_header_work.delivery_type_no,
shipping_method = shipping_header_work.shipping_method,
delivery_type_name = shipping_header_work.delivery_type_name,
delivery_appointed_date = CAST(NULLIF(shipping_header_work.delivery_appointed_date, '') AS DATE),
delivery_appointed_time_start = shipping_header_work.delivery_appointed_time_start,
delivery_appointed_time_end = shipping_header_work.delivery_appointed_time_end,
arrival_date = CAST(NULLIF(shipping_header_work.arrival_date, '') AS DATE),
arrival_time_start = shipping_header_work.arrival_time_start,
arrival_time_end = shipping_header_work.arrival_time_end,
fixed_sales_status = shipping_header_work.fixed_sales_status,
shipping_status = shipping_header_work.shipping_status,
shipping_direct_date = CAST(NULLIF(shipping_header_work.shipping_direct_date, '') AS DATE),
shipping_date = CAST(NULLIF(shipping_header_work.shipping_date, '') AS DATE),
original_shipping_no = shipping_header_work.original_shipping_no,
return_item_date = CAST(NULLIF(shipping_header_work.return_item_date, '') AS DATE),
return_item_type = shipping_header_work.return_item_type,
shipping_area = shipping_header_work.shipping_area,
delivery_note_flg = shipping_header_work.delivery_note_flg,
include_flg = shipping_header_work.include_flg,
delivery_memo = shipping_header_work.delivery_memo,
shipping_bill_price = shipping_header_work.shipping_bill_price,
shipping_dokon_shiji_code = shipping_header_work.shipping_dokon_shiji_code,
o_name_disp_kbn = shipping_header_work.o_name_disp_kbn,
member_stage = shipping_header_work.member_stage,
possession_point = shipping_header_work.possession_point,
sales_recording_date = CAST(NULLIF(shipping_header_work.sales_recording_date, '') AS DATE),
prod_pack_type = shipping_header_work.prod_pack_type,
shipping_method_kbn = shipping_header_work.shipping_method_kbn,
box_code = shipping_header_work.box_code,
delivery_note_message = shipping_header_work.delivery_note_message,
orm_rowid = shipping_header_work.orm_rowid,
created_user = shipping_header_work.created_user,
created_datetime = CAST(NULLIF(shipping_header_work.created_datetime, '') AS TIMESTAMP),
updated_user = shipping_header_work.updated_user,
updated_datetime = CAST(NULLIF(shipping_header_work.updated_datetime, '') AS TIMESTAMP),
dwh_updated_user = shipping_header_work.dwh_updated_user,
dwh_updated_datetime = shipping_header_work.dwh_updated_datetime
WHEN NOT MATCHED THEN INSERT VALUES (
shipping_header_work.shipping_no,
shipping_header_work.order_no,
shipping_header_work.shop_code,
shipping_header_work.customer_code,
shipping_header_work.neo_customer_no,
shipping_header_work.address_no,
shipping_header_work.address_last_name,
shipping_header_work.address_first_name,
shipping_header_work.address_last_name_kana,
shipping_header_work.address_first_name_kana,
shipping_header_work.postal_code,
shipping_header_work.prefecture_code,
shipping_header_work.address1,
shipping_header_work.address2,
shipping_header_work.address3,
shipping_header_work.address4,
shipping_header_work.corporation_post_name,
shipping_header_work.phone_number,
shipping_header_work.delivery_remark,
shipping_header_work.acquired_point,
shipping_header_work.delivery_slip_no,
shipping_header_work.shipping_charge,
shipping_header_work.shipping_charge_tax_type,
shipping_header_work.shipping_charge_tax_group_code,
shipping_header_work.shipping_charge_tax_no,
shipping_header_work.shipping_charge_tax_rate,
shipping_header_work.shipping_charge_tax,
shipping_header_work.delivery_type_no,
shipping_header_work.shipping_method,
shipping_header_work.delivery_type_name,
CAST(NULLIF(shipping_header_work.delivery_appointed_date, '') AS DATE),
shipping_header_work.delivery_appointed_time_start,
shipping_header_work.delivery_appointed_time_end,
CAST(NULLIF(shipping_header_work.arrival_date, '') AS DATE),
shipping_header_work.arrival_time_start,
shipping_header_work.arrival_time_end,
shipping_header_work.fixed_sales_status,
shipping_header_work.shipping_status,
CAST(NULLIF(shipping_header_work.shipping_direct_date, '') AS DATE),
CAST(NULLIF(shipping_header_work.shipping_date, '') AS DATE),
shipping_header_work.original_shipping_no,
CAST(NULLIF(shipping_header_work.return_item_date, '') AS DATE),
shipping_header_work.return_item_type,
shipping_header_work.shipping_area,
shipping_header_work.delivery_note_flg,
shipping_header_work.include_flg,
shipping_header_work.delivery_memo,
shipping_header_work.shipping_bill_price,
shipping_header_work.shipping_dokon_shiji_code,
shipping_header_work.o_name_disp_kbn,
shipping_header_work.member_stage,
shipping_header_work.possession_point,
CAST(NULLIF(shipping_header_work.sales_recording_date, '') AS DATE),
shipping_header_work.prod_pack_type,
shipping_header_work.shipping_method_kbn,
shipping_header_work.box_code,
shipping_header_work.delivery_note_message,
shipping_header_work.orm_rowid,
shipping_header_work.created_user,
CAST(NULLIF(shipping_header_work.created_datetime, '') AS TIMESTAMP),
shipping_header_work.updated_user,
CAST(NULLIF(shipping_header_work.updated_datetime, '') AS TIMESTAMP),
shipping_header_work.dwh_created_user,
shipping_header_work.dwh_created_datetime,
shipping_header_work.dwh_updated_user,
shipping_header_work.dwh_updated_datetime
);