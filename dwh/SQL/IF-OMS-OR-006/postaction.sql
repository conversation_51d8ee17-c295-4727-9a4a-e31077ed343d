MERGE INTO dwh_raw.regular_sale_cont_composition USING dwh_raw.regular_sale_cont_composition_work
ON regular_sale_cont_composition.regular_contract_no = regular_sale_cont_composition_work.regular_contract_no AND
 regular_sale_cont_composition.regular_contract_detail_no = regular_sale_cont_composition_work.regular_contract_detail_no AND
 regular_sale_cont_composition.composition_no = regular_sale_cont_composition_work.composition_no
WHEN MATCHED THEN UPDATE SET
shop_code = regular_sale_cont_composition_work.shop_code,
parent_commodity_code = regular_sale_cont_composition_work.parent_commodity_code,
parent_sku_code = regular_sale_cont_composition_work.parent_sku_code,
child_commodity_code = regular_sale_cont_composition_work.child_commodity_code,
child_sku_code = regular_sale_cont_composition_work.child_sku_code,
commodity_name = regular_sale_cont_composition_work.commodity_name,
composition_quantity = regular_sale_cont_composition_work.composition_quantity,
regular_sale_composition_no = regular_sale_cont_composition_work.regular_sale_composition_no,
regular_sale_composition_name = regular_sale_cont_composition_work.regular_sale_composition_name,
regular_sale_commodity_type = regular_sale_cont_composition_work.regular_sale_commodity_type,
regular_order_count_min_limit = regular_sale_cont_composition_work.regular_order_count_min_limit,
regular_order_count_max_limit = regular_sale_cont_composition_work.regular_order_count_max_limit,
regular_order_count_interval = regular_sale_cont_composition_work.regular_order_count_interval,
orm_rowid = regular_sale_cont_composition_work.orm_rowid,
created_user = regular_sale_cont_composition_work.created_user,
created_datetime = CAST(NULLIF(regular_sale_cont_composition_work.created_datetime, '') AS TIMESTAMP),
updated_user = regular_sale_cont_composition_work.updated_user,
updated_datetime = CAST(NULLIF(regular_sale_cont_composition_work.updated_datetime, '') AS TIMESTAMP),
dwh_updated_user = regular_sale_cont_composition_work.dwh_updated_user,
dwh_updated_datetime = regular_sale_cont_composition_work.dwh_updated_datetime
WHEN NOT MATCHED THEN INSERT VALUES (
regular_sale_cont_composition_work.regular_contract_no,
regular_sale_cont_composition_work.regular_contract_detail_no,
regular_sale_cont_composition_work.composition_no,
regular_sale_cont_composition_work.shop_code,
regular_sale_cont_composition_work.parent_commodity_code,
regular_sale_cont_composition_work.parent_sku_code,
regular_sale_cont_composition_work.child_commodity_code,
regular_sale_cont_composition_work.child_sku_code,
regular_sale_cont_composition_work.commodity_name,
regular_sale_cont_composition_work.composition_quantity,
regular_sale_cont_composition_work.regular_sale_composition_no,
regular_sale_cont_composition_work.regular_sale_composition_name,
regular_sale_cont_composition_work.regular_sale_commodity_type,
regular_sale_cont_composition_work.regular_order_count_min_limit,
regular_sale_cont_composition_work.regular_order_count_max_limit,
regular_sale_cont_composition_work.regular_order_count_interval,
regular_sale_cont_composition_work.orm_rowid,
regular_sale_cont_composition_work.created_user,
CAST(NULLIF(regular_sale_cont_composition_work.created_datetime, '') AS TIMESTAMP),
regular_sale_cont_composition_work.updated_user,
CAST(NULLIF(regular_sale_cont_composition_work.updated_datetime, '') AS TIMESTAMP),
regular_sale_cont_composition_work.dwh_created_user,
regular_sale_cont_composition_work.dwh_created_datetime,
regular_sale_cont_composition_work.dwh_updated_user,
regular_sale_cont_composition_work.dwh_updated_datetime
);