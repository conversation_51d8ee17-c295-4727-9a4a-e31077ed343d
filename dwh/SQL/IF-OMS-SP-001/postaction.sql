MERGE INTO dwh_raw.shipping_detail USING dwh_raw.shipping_detail_work
ON shipping_detail.shipping_no = shipping_detail_work.shipping_no AND
 shipping_detail.shipping_detail_no = shipping_detail_work.shipping_detail_no
WHEN MATCHED THEN UPDATE SET
shop_code = shipping_detail_work.shop_code,
sku_code = shipping_detail_work.sku_code,
unit_price = shipping_detail_work.unit_price,
discount_price = shipping_detail_work.discount_price,
discount_amount = shipping_detail_work.discount_amount,
retail_price = shipping_detail_work.retail_price,
retail_tax_group_code = shipping_detail_work.retail_tax_group_code,
retail_tax_no = shipping_detail_work.retail_tax_no,
retail_tax_rate = shipping_detail_work.retail_tax_rate,
retail_tax = shipping_detail_work.retail_tax,
purchasing_amount = shipping_detail_work.purchasing_amount,
gift_code = shipping_detail_work.gift_code,
gift_name = shipping_detail_work.gift_name,
gift_price = shipping_detail_work.gift_price,
gift_tax_group_code = shipping_detail_work.gift_tax_group_code,
gift_tax_no = shipping_detail_work.gift_tax_no,
gift_tax_rate = shipping_detail_work.gift_tax_rate,
gift_tax = shipping_detail_work.gift_tax,
gift_tax_type = shipping_detail_work.gift_tax_type,
noshi_code = shipping_detail_work.noshi_code,
noshi_name = shipping_detail_work.noshi_name,
noshi_price = shipping_detail_work.noshi_price,
noshi_tax_group_code = shipping_detail_work.noshi_tax_group_code,
noshi_tax_no = shipping_detail_work.noshi_tax_no,
noshi_tax_rate = shipping_detail_work.noshi_tax_rate,
noshi_tax = shipping_detail_work.noshi_tax,
noshi_tax_type = shipping_detail_work.noshi_tax_type,
noshi_nameplate = shipping_detail_work.noshi_nameplate,
noshi_message = shipping_detail_work.noshi_message,
air_transport_flg = shipping_detail_work.air_transport_flg,
delivery_note_no_disp_flg = shipping_detail_work.delivery_note_no_disp_flg,
hasso_souko_cd = shipping_detail_work.hasso_souko_cd,
shipping_hold_kbn = shipping_detail_work.shipping_hold_kbn,
shipping_hold_date = CAST(NULLIF(shipping_detail_work.shipping_hold_date, '') AS DATE),
order_detail_no = shipping_detail_work.order_detail_no,
tracking_out_flg = shipping_detail_work.tracking_out_flg,
souko_shiji = shipping_detail_work.souko_shiji,
orm_rowid = shipping_detail_work.orm_rowid,
created_user = shipping_detail_work.created_user,
created_datetime = CAST(NULLIF(shipping_detail_work.created_datetime, '') AS TIMESTAMP),
updated_user = shipping_detail_work.updated_user,
updated_datetime = CAST(NULLIF(shipping_detail_work.updated_datetime, '') AS TIMESTAMP),
dwh_updated_user = shipping_detail_work.dwh_updated_user,
dwh_updated_datetime = shipping_detail_work.dwh_updated_datetime
WHEN NOT MATCHED THEN INSERT VALUES (
shipping_detail_work.shipping_no,
shipping_detail_work.shipping_detail_no,
shipping_detail_work.shop_code,
shipping_detail_work.sku_code,
shipping_detail_work.unit_price,
shipping_detail_work.discount_price,
shipping_detail_work.discount_amount,
shipping_detail_work.retail_price,
shipping_detail_work.retail_tax_group_code,
shipping_detail_work.retail_tax_no,
shipping_detail_work.retail_tax_rate,
shipping_detail_work.retail_tax,
shipping_detail_work.purchasing_amount,
shipping_detail_work.gift_code,
shipping_detail_work.gift_name,
shipping_detail_work.gift_price,
shipping_detail_work.gift_tax_group_code,
shipping_detail_work.gift_tax_no,
shipping_detail_work.gift_tax_rate,
shipping_detail_work.gift_tax,
shipping_detail_work.gift_tax_type,
shipping_detail_work.noshi_code,
shipping_detail_work.noshi_name,
shipping_detail_work.noshi_price,
shipping_detail_work.noshi_tax_group_code,
shipping_detail_work.noshi_tax_no,
shipping_detail_work.noshi_tax_rate,
shipping_detail_work.noshi_tax,
shipping_detail_work.noshi_tax_type,
shipping_detail_work.noshi_nameplate,
shipping_detail_work.noshi_message,
shipping_detail_work.air_transport_flg,
shipping_detail_work.delivery_note_no_disp_flg,
shipping_detail_work.hasso_souko_cd,
shipping_detail_work.shipping_hold_kbn,
CAST(NULLIF(shipping_detail_work.shipping_hold_date, '') AS DATE),
shipping_detail_work.order_detail_no,
shipping_detail_work.tracking_out_flg,
shipping_detail_work.souko_shiji,
shipping_detail_work.orm_rowid,
shipping_detail_work.created_user,
CAST(NULLIF(shipping_detail_work.created_datetime, '') AS TIMESTAMP),
shipping_detail_work.updated_user,
CAST(NULLIF(shipping_detail_work.updated_datetime, '') AS TIMESTAMP),
shipping_detail_work.dwh_created_user,
shipping_detail_work.dwh_created_datetime,
shipping_detail_work.dwh_updated_user,
shipping_detail_work.dwh_updated_datetime
);