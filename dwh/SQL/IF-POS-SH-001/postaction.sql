MERGE INTO dwh_raw.shop_mst_raw USING dwh_raw.shop_mst_raw_work
ON shop_mst_raw.shop_cd = shop_mst_raw_work.shop_cd
WHEN MATCHED THEN UPDATE SET
group_cd = shop_mst_raw_work.group_cd,
area_cd = shop_mst_raw_work.area_cd,
district_cd = shop_mst_raw_work.district_cd,
shop_kind = shop_mst_raw_work.shop_kind,
shop_name_full = shop_mst_raw_work.shop_name_full,
shop_name_half = shop_mst_raw_work.shop_name_half,
shop_name_short = shop_mst_raw_work.shop_name_short,
shop_name_english = shop_mst_raw_work.shop_name_english,
zip_cd = shop_mst_raw_work.zip_cd,
prefecture_cd = shop_mst_raw_work.prefecture_cd,
address1 = shop_mst_raw_work.address1,
address2 = shop_mst_raw_work.address2,
address3 = shop_mst_raw_work.address3,
address4 = shop_mst_raw_work.address4,
tel_num = shop_mst_raw_work.tel_num,
fax_num = shop_mst_raw_work.fax_num,
tax_inclusive_round_kind = shop_mst_raw_work.tax_inclusive_round_kind,
tax_exclusive_round_kind = shop_mst_raw_work.tax_exclusive_round_kind,
condition_kind = shop_mst_raw_work.condition_kind,
condition_detail_kind = shop_mst_raw_work.condition_detail_kind,
channel_kind = shop_mst_raw_work.channel_kind,
is_stock_control = shop_mst_raw_work.is_stock_control,
begin_date = CAST(NULLIF(shop_mst_raw_work.begin_date, '') AS DATE),
end_date = CAST(NULLIF(shop_mst_raw_work.end_date, '') AS DATE),
shop_floor_space = shop_mst_raw_work.shop_floor_space,
is_abandon_price_change = shop_mst_raw_work.is_abandon_price_change,
is_abandon_stock_transfer = shop_mst_raw_work.is_abandon_stock_transfer,
form_management_kind = shop_mst_raw_work.form_management_kind,
is_shop_terminal = shop_mst_raw_work.is_shop_terminal,
allow_transfer_group_cd = shop_mst_raw_work.allow_transfer_group_cd,
main_brand_cd = shop_mst_raw_work.main_brand_cd,
disc_round_position = shop_mst_raw_work.disc_round_position,
disc_round_kind = shop_mst_raw_work.disc_round_kind,
is_emp_salse_place = shop_mst_raw_work.is_emp_salse_place,
is_move_operation = shop_mst_raw_work.is_move_operation,
is_sales_register = shop_mst_raw_work.is_sales_register,
is_stock_display = shop_mst_raw_work.is_stock_display,
brock_cd = shop_mst_raw_work.brock_cd,
pricechange_disc_round_position = shop_mst_raw_work.pricechange_disc_round_position,
pricechange_disc_round_kind = shop_mst_raw_work.pricechange_disc_round_kind,
numeric_reserve1 = shop_mst_raw_work.numeric_reserve1,
numeric_reserve2 = shop_mst_raw_work.numeric_reserve2,
numeric_reserve3 = shop_mst_raw_work.numeric_reserve3,
numeric_reserve4 = shop_mst_raw_work.numeric_reserve4,
numeric_reserve5 = shop_mst_raw_work.numeric_reserve5,
string_reserve1 = shop_mst_raw_work.string_reserve1,
string_reserve2 = shop_mst_raw_work.string_reserve2,
string_reserve3 = shop_mst_raw_work.string_reserve3,
string_reserve4 = shop_mst_raw_work.string_reserve4,
string_reserve5 = shop_mst_raw_work.string_reserve5,
is_deleted = shop_mst_raw_work.is_deleted,
spare_numeric_reserve1 = shop_mst_raw_work.spare_numeric_reserve1,
spare_numeric_reserve2 = shop_mst_raw_work.spare_numeric_reserve2,
spare_numeric_reserve3 = shop_mst_raw_work.spare_numeric_reserve3,
apare_numeric_reserve4 = shop_mst_raw_work.apare_numeric_reserve4,
spare_numeric_reserve5 = shop_mst_raw_work.spare_numeric_reserve5,
spare_string_reserve1 = shop_mst_raw_work.spare_string_reserve1,
spare_string_reserve2 = shop_mst_raw_work.spare_string_reserve2,
spare_string_reserve3 = shop_mst_raw_work.spare_string_reserve3,
spare_string_reserve4 = shop_mst_raw_work.spare_string_reserve4,
spare_string_reserve5 = shop_mst_raw_work.spare_string_reserve5,
spare_string_reserve6 = shop_mst_raw_work.spare_string_reserve6,
spare_string_reserve7 = shop_mst_raw_work.spare_string_reserve7,
dwh_updated_user = shop_mst_raw_work.dwh_updated_user,
dwh_updated_datetime = shop_mst_raw_work.dwh_updated_datetime
WHEN NOT MATCHED THEN INSERT VALUES (
shop_mst_raw_work.shop_cd,
shop_mst_raw_work.group_cd,
shop_mst_raw_work.area_cd,
shop_mst_raw_work.district_cd,
shop_mst_raw_work.shop_kind,
shop_mst_raw_work.shop_name_full,
shop_mst_raw_work.shop_name_half,
shop_mst_raw_work.shop_name_short,
shop_mst_raw_work.shop_name_english,
shop_mst_raw_work.zip_cd,
shop_mst_raw_work.prefecture_cd,
shop_mst_raw_work.address1,
shop_mst_raw_work.address2,
shop_mst_raw_work.address3,
shop_mst_raw_work.address4,
shop_mst_raw_work.tel_num,
shop_mst_raw_work.fax_num,
shop_mst_raw_work.tax_inclusive_round_kind,
shop_mst_raw_work.tax_exclusive_round_kind,
shop_mst_raw_work.condition_kind,
shop_mst_raw_work.condition_detail_kind,
shop_mst_raw_work.channel_kind,
shop_mst_raw_work.is_stock_control,
CAST(NULLIF(shop_mst_raw_work.begin_date, '') AS DATE),
CAST(NULLIF(shop_mst_raw_work.end_date, '') AS DATE),
shop_mst_raw_work.shop_floor_space,
shop_mst_raw_work.is_abandon_price_change,
shop_mst_raw_work.is_abandon_stock_transfer,
shop_mst_raw_work.form_management_kind,
shop_mst_raw_work.is_shop_terminal,
shop_mst_raw_work.allow_transfer_group_cd,
shop_mst_raw_work.main_brand_cd,
shop_mst_raw_work.disc_round_position,
shop_mst_raw_work.disc_round_kind,
shop_mst_raw_work.is_emp_salse_place,
shop_mst_raw_work.is_move_operation,
shop_mst_raw_work.is_sales_register,
shop_mst_raw_work.is_stock_display,
shop_mst_raw_work.brock_cd,
shop_mst_raw_work.pricechange_disc_round_position,
shop_mst_raw_work.pricechange_disc_round_kind,
shop_mst_raw_work.numeric_reserve1,
shop_mst_raw_work.numeric_reserve2,
shop_mst_raw_work.numeric_reserve3,
shop_mst_raw_work.numeric_reserve4,
shop_mst_raw_work.numeric_reserve5,
shop_mst_raw_work.string_reserve1,
shop_mst_raw_work.string_reserve2,
shop_mst_raw_work.string_reserve3,
shop_mst_raw_work.string_reserve4,
shop_mst_raw_work.string_reserve5,
shop_mst_raw_work.is_deleted,
shop_mst_raw_work.spare_numeric_reserve1,
shop_mst_raw_work.spare_numeric_reserve2,
shop_mst_raw_work.spare_numeric_reserve3,
shop_mst_raw_work.apare_numeric_reserve4,
shop_mst_raw_work.spare_numeric_reserve5,
shop_mst_raw_work.spare_string_reserve1,
shop_mst_raw_work.spare_string_reserve2,
shop_mst_raw_work.spare_string_reserve3,
shop_mst_raw_work.spare_string_reserve4,
shop_mst_raw_work.spare_string_reserve5,
shop_mst_raw_work.spare_string_reserve6,
shop_mst_raw_work.spare_string_reserve7,
shop_mst_raw_work.dwh_created_user,
shop_mst_raw_work.dwh_created_datetime,
shop_mst_raw_work.dwh_updated_user,
shop_mst_raw_work.dwh_updated_datetime
);