MERGE INTO dwh_raw.favorite_product USING dwh_raw.favorite_product_work
ON favorite_product.id = favorite_product_work.id
WHEN MATCHED THEN UPDATE SET
ownerid = favorite_product_work.ownerid,
isdeleted = favorite_product_work.isdeleted,
name = favorite_product_work.name,
createddate = CAST(NULLIF(favorite_product_work.createddate, '') AS TIMESTAMP),
createdbyid = favorite_product_work.createdbyid,
lastmodifieddate = CAST(NULLIF(favorite_product_work.lastmodifieddate, '') AS TIMESTAMP),
lastmodifiedbyid = favorite_product_work.lastmodifiedbyid,
systemmodstamp = CAST(NULLIF(favorite_product_work.systemmodstamp, '') AS TIMESTAMP),
accountid__c = favorite_product_work.accountid__c,
productid__c = favorite_product_work.productid__c,
productcode__c = favorite_product_work.productcode__c,
productname__c = favorite_product_work.productname__c,
isdeleted__c = favorite_product_work.isdeleted__c,
dwh_updated_user = favorite_product_work.dwh_updated_user,
dwh_updated_datetime = favorite_product_work.dwh_updated_datetime
WHEN NOT MATCHED THEN INSERT VALUES (
favorite_product_work.id,
favorite_product_work.ownerid,
favorite_product_work.isdeleted,
favorite_product_work.name,
CAST(NULLIF(favorite_product_work.createddate, '') AS TIMESTAMP),
favorite_product_work.createdbyid,
CAST(NULLIF(favorite_product_work.lastmodifieddate, '') AS TIMESTAMP),
favorite_product_work.lastmodifiedbyid,
CAST(NULLIF(favorite_product_work.systemmodstamp, '') AS TIMESTAMP),
favorite_product_work.accountid__c,
favorite_product_work.productid__c,
favorite_product_work.productcode__c,
favorite_product_work.productname__c,
favorite_product_work.isdeleted__c,
favorite_product_work.dwh_created_user,
favorite_product_work.dwh_created_datetime,
favorite_product_work.dwh_updated_user,
favorite_product_work.dwh_updated_datetime
);