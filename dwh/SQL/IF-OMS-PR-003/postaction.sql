MERGE INTO dwh_raw.commodity_header USING dwh_raw.commodity_header_work
ON commodity_header.shop_code = commodity_header_work.shop_code AND
 commodity_header.commodity_code = commodity_header_work.commodity_code
WHEN MATCHED THEN UPDATE SET
commodity_name = commodity_header_work.commodity_name,
commodity_type = commodity_header_work.commodity_type,
represent_sku_code = commodity_header_work.represent_sku_code,
represent_sku_unit_price = commodity_header_work.represent_sku_unit_price,
stock_status_no = commodity_header_work.stock_status_no,
stock_management_type = commodity_header_work.stock_management_type,
age_limit_code = commodity_header_work.age_limit_code,
commodity_tax_type = commodity_header_work.commodity_tax_type,
tax_group_code = commodity_header_work.tax_group_code,
short_description = commodity_header_work.short_description,
commodity_search_words = commodity_header_work.commodity_search_words,
prior_printing_description = commodity_header_work.prior_printing_description,
posterior_printing_description = commodity_header_work.posterior_printing_description,
delivery_description = commodity_header_work.delivery_description,
sale_start_datetime = CAST(NULLIF(commodity_header_work.sale_start_datetime, '') AS TIMESTAMP),
sale_end_datetime = CAST(NULLIF(commodity_header_work.sale_end_datetime, '') AS TIMESTAMP),
discount_price_start_datetime = CAST(NULLIF(commodity_header_work.discount_price_start_datetime, '') AS TIMESTAMP),
discount_price_end_datetime = CAST(NULLIF(commodity_header_work.discount_price_end_datetime, '') AS TIMESTAMP),
reservation_start_datetime = CAST(NULLIF(commodity_header_work.reservation_start_datetime, '') AS TIMESTAMP),
reservation_end_datetime = CAST(NULLIF(commodity_header_work.reservation_end_datetime, '') AS TIMESTAMP),
prior_printing_start_date = CAST(NULLIF(commodity_header_work.prior_printing_start_date, '') AS TIMESTAMP),
prior_printing_end_date = CAST(NULLIF(commodity_header_work.prior_printing_end_date, '') AS TIMESTAMP),
posterior_printing_start_date = CAST(NULLIF(commodity_header_work.posterior_printing_start_date, '') AS TIMESTAMP),
posterior_printing_end_date = CAST(NULLIF(commodity_header_work.posterior_printing_end_date, '') AS TIMESTAMP),
delivery_type_no = commodity_header_work.delivery_type_no,
sales_method_type = commodity_header_work.sales_method_type,
manufacturer_model_no = commodity_header_work.manufacturer_model_no,
link_url = commodity_header_work.link_url,
recommend_commodity_rank = commodity_header_work.recommend_commodity_rank,
commodity_popular_rank = commodity_header_work.commodity_popular_rank,
commodity_standard1_name = commodity_header_work.commodity_standard1_name,
commodity_standard2_name = commodity_header_work.commodity_standard2_name,
commodity_point_rate = commodity_header_work.commodity_point_rate,
commodity_point_start_datetime = CAST(NULLIF(commodity_header_work.commodity_point_start_datetime, '') AS TIMESTAMP),
commodity_point_end_datetime = CAST(NULLIF(commodity_header_work.commodity_point_end_datetime, '') AS TIMESTAMP),
sale_flg = commodity_header_work.sale_flg,
noshi_effective_flg = commodity_header_work.noshi_effective_flg,
arrival_goods_flg = commodity_header_work.arrival_goods_flg,
oneshot_order_limit = commodity_header_work.oneshot_order_limit,
standard_image_type = commodity_header_work.standard_image_type,
purchasing_confirm_flg_pc = commodity_header_work.purchasing_confirm_flg_pc,
purchasing_confirm_flg_sp = commodity_header_work.purchasing_confirm_flg_sp,
commodity_kind = commodity_header_work.commodity_kind,
keihi_hurikae_target_flg = commodity_header_work.keihi_hurikae_target_flg,
charge_user_code = commodity_header_work.charge_user_code,
commodity_remark = commodity_header_work.commodity_remark,
channel_cc_sale_flg = commodity_header_work.channel_cc_sale_flg,
channel_ec_sale_flg = commodity_header_work.channel_ec_sale_flg,
shipping_charge_target_flg = commodity_header_work.shipping_charge_target_flg,
first_purchase_limit_flg = commodity_header_work.first_purchase_limit_flg,
purchase_hold_flg = commodity_header_work.purchase_hold_flg,
commodity_exclude_flg = commodity_header_work.commodity_exclude_flg,
commodity_subsubcategory_code = commodity_header_work.commodity_subsubcategory_code,
pack_calc_pattern = commodity_header_work.pack_calc_pattern,
pad_type = commodity_header_work.pad_type,
fall_down_flg = commodity_header_work.fall_down_flg,
height = commodity_header_work.height,
width = commodity_header_work.width,
deepness = commodity_header_work.deepness,
weight = commodity_header_work.weight,
tracking_out_flg = commodity_header_work.tracking_out_flg,
mdm_management_code = commodity_header_work.mdm_management_code,
commodity_segment = commodity_header_work.commodity_segment,
business_segment = commodity_header_work.business_segment,
commodity_group = commodity_header_work.commodity_group,
commodity_series = commodity_header_work.commodity_series,
core_department = commodity_header_work.core_department,
accounting_pattern_type = commodity_header_work.accounting_pattern_type,
return_enabled_flg = commodity_header_work.return_enabled_flg,
exchange_enabled_flg = commodity_header_work.exchange_enabled_flg,
exterior_box_weight = commodity_header_work.exterior_box_weight,
nekoposu_volume_rate = commodity_header_work.nekoposu_volume_rate,
warehouse_assembly_flg = commodity_header_work.warehouse_assembly_flg,
mail_delivery_flg = commodity_header_work.mail_delivery_flg,
before_renewal_commodity_code = commodity_header_work.before_renewal_commodity_code,
preorder_enable_days = commodity_header_work.preorder_enable_days,
main_product_no = commodity_header_work.main_product_no,
product_no = commodity_header_work.product_no,
orm_rowid = commodity_header_work.orm_rowid,
created_user = commodity_header_work.created_user,
created_datetime = CAST(NULLIF(commodity_header_work.created_datetime, '') AS TIMESTAMP),
updated_user = commodity_header_work.updated_user,
updated_datetime = CAST(NULLIF(commodity_header_work.updated_datetime, '') AS TIMESTAMP),
dwh_updated_user = commodity_header_work.dwh_updated_user,
dwh_updated_datetime = commodity_header_work.dwh_updated_datetime
WHEN NOT MATCHED THEN INSERT VALUES (
commodity_header_work.shop_code,
commodity_header_work.commodity_code,
commodity_header_work.commodity_name,
commodity_header_work.commodity_type,
commodity_header_work.represent_sku_code,
commodity_header_work.represent_sku_unit_price,
commodity_header_work.stock_status_no,
commodity_header_work.stock_management_type,
commodity_header_work.age_limit_code,
commodity_header_work.commodity_tax_type,
commodity_header_work.tax_group_code,
commodity_header_work.short_description,
commodity_header_work.commodity_search_words,
commodity_header_work.prior_printing_description,
commodity_header_work.posterior_printing_description,
commodity_header_work.delivery_description,
CAST(NULLIF(commodity_header_work.sale_start_datetime, '') AS TIMESTAMP),
CAST(NULLIF(commodity_header_work.sale_end_datetime, '') AS TIMESTAMP),
CAST(NULLIF(commodity_header_work.discount_price_start_datetime, '') AS TIMESTAMP),
CAST(NULLIF(commodity_header_work.discount_price_end_datetime, '') AS TIMESTAMP),
CAST(NULLIF(commodity_header_work.reservation_start_datetime, '') AS TIMESTAMP),
CAST(NULLIF(commodity_header_work.reservation_end_datetime, '') AS TIMESTAMP),
CAST(NULLIF(commodity_header_work.prior_printing_start_date, '') AS TIMESTAMP),
CAST(NULLIF(commodity_header_work.prior_printing_end_date, '') AS TIMESTAMP),
CAST(NULLIF(commodity_header_work.posterior_printing_start_date, '') AS TIMESTAMP),
CAST(NULLIF(commodity_header_work.posterior_printing_end_date, '') AS TIMESTAMP),
commodity_header_work.delivery_type_no,
commodity_header_work.sales_method_type,
commodity_header_work.manufacturer_model_no,
commodity_header_work.link_url,
commodity_header_work.recommend_commodity_rank,
commodity_header_work.commodity_popular_rank,
commodity_header_work.commodity_standard1_name,
commodity_header_work.commodity_standard2_name,
commodity_header_work.commodity_point_rate,
CAST(NULLIF(commodity_header_work.commodity_point_start_datetime, '') AS TIMESTAMP),
CAST(NULLIF(commodity_header_work.commodity_point_end_datetime, '') AS TIMESTAMP),
commodity_header_work.sale_flg,
commodity_header_work.noshi_effective_flg,
commodity_header_work.arrival_goods_flg,
commodity_header_work.oneshot_order_limit,
commodity_header_work.standard_image_type,
commodity_header_work.purchasing_confirm_flg_pc,
commodity_header_work.purchasing_confirm_flg_sp,
commodity_header_work.commodity_kind,
commodity_header_work.keihi_hurikae_target_flg,
commodity_header_work.charge_user_code,
commodity_header_work.commodity_remark,
commodity_header_work.channel_cc_sale_flg,
commodity_header_work.channel_ec_sale_flg,
commodity_header_work.shipping_charge_target_flg,
commodity_header_work.first_purchase_limit_flg,
commodity_header_work.purchase_hold_flg,
commodity_header_work.commodity_exclude_flg,
commodity_header_work.commodity_subsubcategory_code,
commodity_header_work.pack_calc_pattern,
commodity_header_work.pad_type,
commodity_header_work.fall_down_flg,
commodity_header_work.height,
commodity_header_work.width,
commodity_header_work.deepness,
commodity_header_work.weight,
commodity_header_work.tracking_out_flg,
commodity_header_work.mdm_management_code,
commodity_header_work.commodity_segment,
commodity_header_work.business_segment,
commodity_header_work.commodity_group,
commodity_header_work.commodity_series,
commodity_header_work.core_department,
commodity_header_work.accounting_pattern_type,
commodity_header_work.return_enabled_flg,
commodity_header_work.exchange_enabled_flg,
commodity_header_work.exterior_box_weight,
commodity_header_work.nekoposu_volume_rate,
commodity_header_work.warehouse_assembly_flg,
commodity_header_work.mail_delivery_flg,
commodity_header_work.before_renewal_commodity_code,
commodity_header_work.preorder_enable_days,
commodity_header_work.main_product_no,
commodity_header_work.product_no,
commodity_header_work.orm_rowid,
commodity_header_work.created_user,
CAST(NULLIF(commodity_header_work.created_datetime, '') AS TIMESTAMP),
commodity_header_work.updated_user,
CAST(NULLIF(commodity_header_work.updated_datetime, '') AS TIMESTAMP),
commodity_header_work.dwh_created_user,
commodity_header_work.dwh_created_datetime,
commodity_header_work.dwh_updated_user,
commodity_header_work.dwh_updated_datetime
);