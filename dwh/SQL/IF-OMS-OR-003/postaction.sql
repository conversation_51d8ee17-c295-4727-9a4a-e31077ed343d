MERGE INTO dwh_raw.order_detail USING dwh_raw.order_detail_work
ON order_detail.order_no = order_detail_work.order_no AND
 order_detail.order_detail_no = order_detail_work.order_detail_no
WHEN MATCHED THEN UPDATE SET
shop_code = order_detail_work.shop_code,
sku_code = order_detail_work.sku_code,
commodity_code = order_detail_work.commodity_code,
commodity_name = order_detail_work.commodity_name,
commodity_kind = order_detail_work.commodity_kind,
baitai_code = order_detail_work.baitai_code,
baitai_name = order_detail_work.baitai_name,
hinban_code = order_detail_work.hinban_code,
standard_detail1_name = order_detail_work.standard_detail1_name,
standard_detail2_name = order_detail_work.standard_detail2_name,
purchasing_amount = order_detail_work.purchasing_amount,
unit_price = order_detail_work.unit_price,
retail_price = order_detail_work.retail_price,
retail_tax = order_detail_work.retail_tax,
commodity_tax_group_code = order_detail_work.commodity_tax_group_code,
commodity_tax_no = order_detail_work.commodity_tax_no,
commodity_tax_rate = order_detail_work.commodity_tax_rate,
commodity_tax = order_detail_work.commodity_tax,
commodity_tax_type = order_detail_work.commodity_tax_type,
campaign_code = order_detail_work.campaign_code,
campaign_name = order_detail_work.campaign_name,
campaign_instructions_code = order_detail_work.campaign_instructions_code,
campaign_instructions_name = order_detail_work.campaign_instructions_name,
campaign_discount_rate = order_detail_work.campaign_discount_rate,
campaign_discount_price = order_detail_work.campaign_discount_price,
present_campaign_instructions_code = order_detail_work.present_campaign_instructions_code,
present_order_detail_no = order_detail_work.present_order_detail_no,
age_limit_code = order_detail_work.age_limit_code,
age_limit_name = order_detail_work.age_limit_name,
age = order_detail_work.age,
age_limit_confirm_type = order_detail_work.age_limit_confirm_type,
applied_point_rate = order_detail_work.applied_point_rate,
benefits_code = order_detail_work.benefits_code,
benefits_name = order_detail_work.benefits_name,
benefits_commodity_code = order_detail_work.benefits_commodity_code,
stock_management_type = order_detail_work.stock_management_type,
stock_allocated_kbn = order_detail_work.stock_allocated_kbn,
allocated_warehouse_code = order_detail_work.allocated_warehouse_code,
allocated_quantity = order_detail_work.allocated_quantity,
arrival_reserved_quantity = order_detail_work.arrival_reserved_quantity,
cancel_quantity = order_detail_work.cancel_quantity,
henpin_qt = order_detail_work.henpin_qt,
coupon_management_code = order_detail_work.coupon_management_code,
coupon_code = order_detail_work.coupon_code,
coupon_name = order_detail_work.coupon_name,
coupon_discount_rate = order_detail_work.coupon_discount_rate,
coupon_discount_price = order_detail_work.coupon_discount_price,
ec_promotion_id = order_detail_work.ec_promotion_id,
ec_promotion_name = order_detail_work.ec_promotion_name,
ec_promotion_discount_price = order_detail_work.ec_promotion_discount_price,
ec_campaign_id = order_detail_work.ec_campaign_id,
ec_campaign_name = order_detail_work.ec_campaign_name,
adjustment_price = order_detail_work.adjustment_price,
keihi_hurikae_target_flg = order_detail_work.keihi_hurikae_target_flg,
member_price_applied_flg = order_detail_work.member_price_applied_flg,
shipping_charge_target_flg = order_detail_work.shipping_charge_target_flg,
regular_contract_no = order_detail_work.regular_contract_no,
regular_contract_detail_no = order_detail_work.regular_contract_detail_no,
regular_kaiji = order_detail_work.regular_kaiji,
regular_check_memo = order_detail_work.regular_check_memo,
total_commodity_buy_count = order_detail_work.total_commodity_buy_count,
total_commodity_regular_kaiji = order_detail_work.total_commodity_regular_kaiji,
regular_total_commodity_regular_kaiji = order_detail_work.regular_total_commodity_regular_kaiji,
commodity_category_code = order_detail_work.commodity_category_code,
total_category_buy_count = order_detail_work.total_category_buy_count,
total_categoryregular_kaiji = order_detail_work.total_categoryregular_kaiji,
regular_total_categoryregular_kaiji = order_detail_work.regular_total_categoryregular_kaiji,
commodity_subcategory_code = order_detail_work.commodity_subcategory_code,
total_subcategory_buy_count = order_detail_work.total_subcategory_buy_count,
total_subcategoryregular_kaiji = order_detail_work.total_subcategoryregular_kaiji,
regular_total_subcategoryregular_kaiji = order_detail_work.regular_total_subcategoryregular_kaiji,
commodity_subsubcategory_code = order_detail_work.commodity_subsubcategory_code,
total_subsubcategory_buy_count = order_detail_work.total_subsubcategory_buy_count,
total_subsubcategoryregular_kaiji = order_detail_work.total_subsubcategoryregular_kaiji,
regular_total_subsubcategoryregular_kaiji = order_detail_work.regular_total_subsubcategoryregular_kaiji,
grant_plan_point_prod_detail = order_detail_work.grant_plan_point_prod_detail,
reduction_plan_point_prod_detail = order_detail_work.reduction_plan_point_prod_detail,
orm_rowid = order_detail_work.orm_rowid,
created_user = order_detail_work.created_user,
created_datetime = CAST(NULLIF(order_detail_work.created_datetime, '') AS TIMESTAMP),
updated_user = order_detail_work.updated_user,
updated_datetime = CAST(NULLIF(order_detail_work.updated_datetime, '') AS TIMESTAMP),
dwh_updated_user = order_detail_work.dwh_updated_user,
dwh_updated_datetime = order_detail_work.dwh_updated_datetime
WHEN NOT MATCHED THEN INSERT VALUES (
order_detail_work.order_no,
order_detail_work.order_detail_no,
order_detail_work.shop_code,
order_detail_work.sku_code,
order_detail_work.commodity_code,
order_detail_work.commodity_name,
order_detail_work.commodity_kind,
order_detail_work.baitai_code,
order_detail_work.baitai_name,
order_detail_work.hinban_code,
order_detail_work.standard_detail1_name,
order_detail_work.standard_detail2_name,
order_detail_work.purchasing_amount,
order_detail_work.unit_price,
order_detail_work.retail_price,
order_detail_work.retail_tax,
order_detail_work.commodity_tax_group_code,
order_detail_work.commodity_tax_no,
order_detail_work.commodity_tax_rate,
order_detail_work.commodity_tax,
order_detail_work.commodity_tax_type,
order_detail_work.campaign_code,
order_detail_work.campaign_name,
order_detail_work.campaign_instructions_code,
order_detail_work.campaign_instructions_name,
order_detail_work.campaign_discount_rate,
order_detail_work.campaign_discount_price,
order_detail_work.present_campaign_instructions_code,
order_detail_work.present_order_detail_no,
order_detail_work.age_limit_code,
order_detail_work.age_limit_name,
order_detail_work.age,
order_detail_work.age_limit_confirm_type,
order_detail_work.applied_point_rate,
order_detail_work.benefits_code,
order_detail_work.benefits_name,
order_detail_work.benefits_commodity_code,
order_detail_work.stock_management_type,
order_detail_work.stock_allocated_kbn,
order_detail_work.allocated_warehouse_code,
order_detail_work.allocated_quantity,
order_detail_work.arrival_reserved_quantity,
order_detail_work.cancel_quantity,
order_detail_work.henpin_qt,
order_detail_work.coupon_management_code,
order_detail_work.coupon_code,
order_detail_work.coupon_name,
order_detail_work.coupon_discount_rate,
order_detail_work.coupon_discount_price,
order_detail_work.ec_promotion_id,
order_detail_work.ec_promotion_name,
order_detail_work.ec_promotion_discount_price,
order_detail_work.ec_campaign_id,
order_detail_work.ec_campaign_name,
order_detail_work.adjustment_price,
order_detail_work.keihi_hurikae_target_flg,
order_detail_work.member_price_applied_flg,
order_detail_work.shipping_charge_target_flg,
order_detail_work.regular_contract_no,
order_detail_work.regular_contract_detail_no,
order_detail_work.regular_kaiji,
order_detail_work.regular_check_memo,
order_detail_work.total_commodity_buy_count,
order_detail_work.total_commodity_regular_kaiji,
order_detail_work.regular_total_commodity_regular_kaiji,
order_detail_work.commodity_category_code,
order_detail_work.total_category_buy_count,
order_detail_work.total_categoryregular_kaiji,
order_detail_work.regular_total_categoryregular_kaiji,
order_detail_work.commodity_subcategory_code,
order_detail_work.total_subcategory_buy_count,
order_detail_work.total_subcategoryregular_kaiji,
order_detail_work.regular_total_subcategoryregular_kaiji,
order_detail_work.commodity_subsubcategory_code,
order_detail_work.total_subsubcategory_buy_count,
order_detail_work.total_subsubcategoryregular_kaiji,
order_detail_work.regular_total_subsubcategoryregular_kaiji,
order_detail_work.grant_plan_point_prod_detail,
order_detail_work.reduction_plan_point_prod_detail,
order_detail_work.orm_rowid,
order_detail_work.created_user,
CAST(NULLIF(order_detail_work.created_datetime, '') AS TIMESTAMP),
order_detail_work.updated_user,
CAST(NULLIF(order_detail_work.updated_datetime, '') AS TIMESTAMP),
order_detail_work.dwh_created_user,
order_detail_work.dwh_created_datetime,
order_detail_work.dwh_updated_user,
order_detail_work.dwh_updated_datetime
);