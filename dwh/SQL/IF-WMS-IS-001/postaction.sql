MERGE INTO dwh_raw.arrival_achievements USING dwh_raw.arrival_achievements_work
ON arrival_achievements.receiving_stock_slip_no = arrival_achievements_work.receiving_stock_slip_no AND
 arrival_achievements.receiving_stock_slip_row_no = arrival_achievements_work.receiving_stock_slip_row_no
WHEN MATCHED THEN UPDATE SET
warehouse_cd = arrival_achievements_work.warehouse_cd,
receiving_stock_date = CAST(NULLIF(arrival_achievements_work.receiving_stock_date, '') AS DATE),
receiving_stock_former_cd = arrival_achievements_work.receiving_stock_former_cd,
po_no = arrival_achievements_work.po_no,
product_cd = arrival_achievements_work.product_cd,
receiving_stock_cnt = arrival_achievements_work.receiving_stock_cnt,
dwh_updated_user = arrival_achievements_work.dwh_updated_user,
dwh_updated_datetime = arrival_achievements_work.dwh_updated_datetime
WHEN NOT MATCHED THEN INSERT VALUES (
arrival_achievements_work.warehouse_cd,
CAST(NULLIF(arrival_achievements_work.receiving_stock_date, '') AS DATE),
arrival_achievements_work.receiving_stock_former_cd,
arrival_achievements_work.receiving_stock_slip_no,
arrival_achievements_work.receiving_stock_slip_row_no,
arrival_achievements_work.po_no,
arrival_achievements_work.product_cd,
arrival_achievements_work.receiving_stock_cnt,
arrival_achievements_work.dwh_created_user,
arrival_achievements_work.dwh_created_datetime,
arrival_achievements_work.dwh_updated_user,
arrival_achievements_work.dwh_updated_datetime
);