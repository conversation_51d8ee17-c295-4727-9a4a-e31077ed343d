MERGE INTO dwh_raw.regular_sale_payment USING dwh_raw.regular_sale_payment_work
ON regular_sale_payment.shop_code = regular_sale_payment_work.shop_code AND
 regular_sale_payment.regular_sale_code = regular_sale_payment_work.regular_sale_code AND
 regular_sale_payment.payment_method_no = regular_sale_payment_work.payment_method_no
WHEN MATCHED THEN UPDATE SET
orm_rowid = regular_sale_payment_work.orm_rowid,
created_user = regular_sale_payment_work.created_user,
created_datetime = CAST(NULLIF(regular_sale_payment_work.created_datetime, '') AS TIMESTAMP),
updated_user = regular_sale_payment_work.updated_user,
updated_datetime = CAST(NULLIF(regular_sale_payment_work.updated_datetime, '') AS TIMESTAMP),
dwh_updated_user = regular_sale_payment_work.dwh_updated_user,
dwh_updated_datetime = regular_sale_payment_work.dwh_updated_datetime
WHEN NOT MATCHED THEN INSERT VALUES (
regular_sale_payment_work.shop_code,
regular_sale_payment_work.regular_sale_code,
regular_sale_payment_work.payment_method_no,
regular_sale_payment_work.orm_rowid,
regular_sale_payment_work.created_user,
CAST(NULLIF(regular_sale_payment_work.created_datetime, '') AS TIMESTAMP),
regular_sale_payment_work.updated_user,
CAST(NULLIF(regular_sale_payment_work.updated_datetime, '') AS TIMESTAMP),
regular_sale_payment_work.dwh_created_user,
regular_sale_payment_work.dwh_created_datetime,
regular_sale_payment_work.dwh_updated_user,
regular_sale_payment_work.dwh_updated_datetime
);