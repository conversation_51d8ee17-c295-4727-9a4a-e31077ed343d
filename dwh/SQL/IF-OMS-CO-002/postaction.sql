MERGE INTO dwh_raw.coupon_customer USING dwh_raw.coupon_customer_work
ON coupon_customer.coupon_management_code = coupon_customer_work.coupon_management_code AND
 coupon_customer.customer_code = coupon_customer_work.customer_code
WHEN MATCHED THEN UPDATE SET
neo_customer_no = coupon_customer_work.neo_customer_no,
coupon_issue_status = coupon_customer_work.coupon_issue_status,
coupon_used_count = coupon_customer_work.coupon_used_count,
coupon_used_date = CAST(NULLIF(coupon_customer_work.coupon_used_date, '') AS DATE),
baitai_code = coupon_customer_work.baitai_code,
orm_rowid = coupon_customer_work.orm_rowid,
created_user = coupon_customer_work.created_user,
created_datetime = CAST(NULLIF(coupon_customer_work.created_datetime, '') AS TIMESTAMP),
updated_user = coupon_customer_work.updated_user,
updated_datetime = CAST(NULLIF(coupon_customer_work.updated_datetime, '') AS TIMESTAMP),
dwh_updated_user = coupon_customer_work.dwh_updated_user,
dwh_updated_datetime = coupon_customer_work.dwh_updated_datetime
WHEN NOT MATCHED THEN INSERT VALUES (
coupon_customer_work.coupon_management_code,
coupon_customer_work.customer_code,
coupon_customer_work.neo_customer_no,
coupon_customer_work.coupon_issue_status,
coupon_customer_work.coupon_used_count,
CAST(NULLIF(coupon_customer_work.coupon_used_date, '') AS DATE),
coupon_customer_work.baitai_code,
coupon_customer_work.orm_rowid,
coupon_customer_work.created_user,
CAST(NULLIF(coupon_customer_work.created_datetime, '') AS TIMESTAMP),
coupon_customer_work.updated_user,
CAST(NULLIF(coupon_customer_work.updated_datetime, '') AS TIMESTAMP),
coupon_customer_work.dwh_created_user,
coupon_customer_work.dwh_created_datetime,
coupon_customer_work.dwh_updated_user,
coupon_customer_work.dwh_updated_datetime
);