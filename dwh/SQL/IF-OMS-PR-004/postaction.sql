MERGE INTO dwh_raw.commodity_detail USING dwh_raw.commodity_detail_work
ON commodity_detail.shop_code = commodity_detail_work.shop_code AND
 commodity_detail.sku_code = commodity_detail_work.sku_code
WHEN MATCHED THEN UPDATE SET
commodity_code = commodity_detail_work.commodity_code,
unit_price = commodity_detail_work.unit_price,
discount_price = commodity_detail_work.discount_price,
reservation_price = commodity_detail_work.reservation_price,
jan_code = commodity_detail_work.jan_code,
standard_detail1_name = commodity_detail_work.standard_detail1_name,
standard_detail2_name = commodity_detail_work.standard_detail2_name,
hinban_code = commodity_detail_work.hinban_code,
hinban_kind = commodity_detail_work.hinban_kind,
member_price_applied_flg = commodity_detail_work.member_price_applied_flg,
member_price_discount_rate = commodity_detail_work.member_price_discount_rate,
member_price = commodity_detail_work.member_price,
air_transport_flg = commodity_detail_work.air_transport_flg,
commodity_prod_pack_type = commodity_detail_work.commodity_prod_pack_type,
delivery_note_no_disp_flg = commodity_detail_work.delivery_note_no_disp_flg,
reduction_point = commodity_detail_work.reduction_point,
orm_rowid = commodity_detail_work.orm_rowid,
created_user = commodity_detail_work.created_user,
created_datetime = CAST(NULLIF(commodity_detail_work.created_datetime, '') AS TIMESTAMP),
updated_user = commodity_detail_work.updated_user,
updated_datetime = CAST(NULLIF(commodity_detail_work.updated_datetime, '') AS TIMESTAMP),
dwh_updated_user = commodity_detail_work.dwh_updated_user,
dwh_updated_datetime = commodity_detail_work.dwh_updated_datetime
WHEN NOT MATCHED THEN INSERT VALUES (
commodity_detail_work.shop_code,
commodity_detail_work.sku_code,
commodity_detail_work.commodity_code,
commodity_detail_work.unit_price,
commodity_detail_work.discount_price,
commodity_detail_work.reservation_price,
commodity_detail_work.jan_code,
commodity_detail_work.standard_detail1_name,
commodity_detail_work.standard_detail2_name,
commodity_detail_work.hinban_code,
commodity_detail_work.hinban_kind,
commodity_detail_work.member_price_applied_flg,
commodity_detail_work.member_price_discount_rate,
commodity_detail_work.member_price,
commodity_detail_work.air_transport_flg,
commodity_detail_work.commodity_prod_pack_type,
commodity_detail_work.delivery_note_no_disp_flg,
commodity_detail_work.reduction_point,
commodity_detail_work.orm_rowid,
commodity_detail_work.created_user,
CAST(NULLIF(commodity_detail_work.created_datetime, '') AS TIMESTAMP),
commodity_detail_work.updated_user,
CAST(NULLIF(commodity_detail_work.updated_datetime, '') AS TIMESTAMP),
commodity_detail_work.dwh_created_user,
commodity_detail_work.dwh_created_datetime,
commodity_detail_work.dwh_updated_user,
commodity_detail_work.dwh_updated_datetime
);