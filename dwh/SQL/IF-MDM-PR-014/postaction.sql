MERGE INTO dwh_raw.lgroup USING dwh_raw.lgroup_work
ON lgroup.lgroup = lgroup_work.lgroup
WHEN MATCHED THEN UPDATE SET
lgroup_name = lgroup_work.lgroup_name,
lgroup_nk = lgroup_work.lgroup_nk,
insert_date = CAST(NULLIF(lgroup_work.insert_date, '') AS TIMESTAMP),
insert_id = lgroup_work.insert_id,
modify_date = CAST(NULLIF(lgroup_work.modify_date, '') AS TIMESTAMP),
modify_id = lgroup_work.modify_id,
dwh_updated_user = lgroup_work.dwh_updated_user,
dwh_updated_datetime = lgroup_work.dwh_updated_datetime
WHEN NOT MATCHED THEN INSERT VALUES (
lgroup_work.lgroup,
lgroup_work.lgroup_name,
lgroup_work.lgroup_nk,
CAST(NULLIF(lgroup_work.insert_date, '') AS TIMESTAMP),
lgroup_work.insert_id,
CAST(NULLIF(lgroup_work.modify_date, '') AS TIMESTAMP),
lgroup_work.modify_id,
lgroup_work.dwh_created_user,
lgroup_work.dwh_created_datetime,
lgroup_work.dwh_updated_user,
lgroup_work.dwh_updated_datetime
);