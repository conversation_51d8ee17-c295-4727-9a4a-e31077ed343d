MERGE INTO dwh_raw.accountin_pattern_gb USING dwh_raw.accountin_pattern_gb_work
ON accountin_pattern_gb.accountin_pattern_gb = accountin_pattern_gb_work.accountin_pattern_gb
WHEN MATCHED THEN UPDATE SET
accountin_pattern_gb_name = accountin_pattern_gb_work.accountin_pattern_gb_name,
accountin_pattern_gb_nk = accountin_pattern_gb_work.accountin_pattern_gb_nk,
insert_date = CAST(NULLIF(accountin_pattern_gb_work.insert_date, '') AS TIMESTAMP),
insert_id = accountin_pattern_gb_work.insert_id,
modify_date = CAST(NULLIF(accountin_pattern_gb_work.modify_date, '') AS TIMESTAMP),
modify_id = accountin_pattern_gb_work.modify_id,
dwh_updated_user = accountin_pattern_gb_work.dwh_updated_user,
dwh_updated_datetime = accountin_pattern_gb_work.dwh_updated_datetime
WHEN NOT MATCHED THEN INSERT VALUES (
accountin_pattern_gb_work.accountin_pattern_gb,
accountin_pattern_gb_work.accountin_pattern_gb_name,
accountin_pattern_gb_work.accountin_pattern_gb_nk,
CAST(NULLIF(accountin_pattern_gb_work.insert_date, '') AS TIMESTAMP),
accountin_pattern_gb_work.insert_id,
CAST(NULLIF(accountin_pattern_gb_work.modify_date, '') AS TIMESTAMP),
accountin_pattern_gb_work.modify_id,
accountin_pattern_gb_work.dwh_created_user,
accountin_pattern_gb_work.dwh_created_datetime,
accountin_pattern_gb_work.dwh_updated_user,
accountin_pattern_gb_work.dwh_updated_datetime
);