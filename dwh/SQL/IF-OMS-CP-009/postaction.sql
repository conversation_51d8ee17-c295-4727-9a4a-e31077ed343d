MERGE INTO dwh_raw.campaign_count_commodity USING dwh_raw.campaign_count_commodity_work
ON campaign_count_commodity.campaign_instructions_code = campaign_count_commodity_work.campaign_instructions_code AND
 campaign_count_commodity.customer_code = campaign_count_commodity_work.customer_code AND
 campaign_count_commodity.shop_code = campaign_count_commodity_work.shop_code AND
 campaign_count_commodity.commodity_code = campaign_count_commodity_work.commodity_code
WHEN MATCHED THEN UPDATE SET
campaign_used_count = campaign_count_commodity_work.campaign_used_count,
orm_rowid = campaign_count_commodity_work.orm_rowid,
created_user = campaign_count_commodity_work.created_user,
created_datetime = CAST(NULLIF(campaign_count_commodity_work.created_datetime, '') AS TIMESTAMP),
updated_user = campaign_count_commodity_work.updated_user,
updated_datetime = CAST(NULLIF(campaign_count_commodity_work.updated_datetime, '') AS TIMESTAMP),
dwh_updated_user = campaign_count_commodity_work.dwh_updated_user,
dwh_updated_datetime = campaign_count_commodity_work.dwh_updated_datetime
WHEN NOT MATCHED THEN INSERT VALUES (
campaign_count_commodity_work.campaign_instructions_code,
campaign_count_commodity_work.customer_code,
campaign_count_commodity_work.shop_code,
campaign_count_commodity_work.commodity_code,
campaign_count_commodity_work.campaign_used_count,
campaign_count_commodity_work.orm_rowid,
campaign_count_commodity_work.created_user,
CAST(NULLIF(campaign_count_commodity_work.created_datetime, '') AS TIMESTAMP),
campaign_count_commodity_work.updated_user,
CAST(NULLIF(campaign_count_commodity_work.updated_datetime, '') AS TIMESTAMP),
campaign_count_commodity_work.dwh_created_user,
campaign_count_commodity_work.dwh_created_datetime,
campaign_count_commodity_work.dwh_updated_user,
campaign_count_commodity_work.dwh_updated_datetime
);