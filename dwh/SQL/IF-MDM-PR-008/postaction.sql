MERGE INTO dwh_raw.product_cat USING dwh_raw.product_cat_work
ON product_cat.product_cat = product_cat_work.product_cat
WHEN MATCHED THEN UPDATE SET
product_cat_name = product_cat_work.product_cat_name,
product_cat_nk = product_cat_work.product_cat_nk,
insert_date = CAST(NULLIF(product_cat_work.insert_date, '') AS TIMESTAMP),
insert_id = product_cat_work.insert_id,
modify_date = CAST(NULLIF(product_cat_work.modify_date, '') AS TIMESTAMP),
modify_id = product_cat_work.modify_id,
dwh_updated_user = product_cat_work.dwh_updated_user,
dwh_updated_datetime = product_cat_work.dwh_updated_datetime
WHEN NOT MATCHED THEN INSERT VALUES (
product_cat_work.product_cat,
product_cat_work.product_cat_name,
product_cat_work.product_cat_nk,
CAST(NULLIF(product_cat_work.insert_date, '') AS TIMESTAMP),
product_cat_work.insert_id,
CAST(NULLIF(product_cat_work.modify_date, '') AS TIMESTAMP),
product_cat_work.modify_id,
product_cat_work.dwh_created_user,
product_cat_work.dwh_created_datetime,
product_cat_work.dwh_updated_user,
product_cat_work.dwh_updated_datetime
);