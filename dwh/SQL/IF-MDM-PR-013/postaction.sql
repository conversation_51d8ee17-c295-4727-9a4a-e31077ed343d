MERGE INTO dwh_raw.product_type USING dwh_raw.product_type_work
ON product_type.product_type = product_type_work.product_type
WHEN MATCHED THEN UPDATE SET
product_type_name = product_type_work.product_type_name,
product_type_nk = product_type_work.product_type_nk,
insert_date = CAST(NULLIF(product_type_work.insert_date, '') AS TIMESTAMP),
insert_id = product_type_work.insert_id,
modify_date = CAST(NULLIF(product_type_work.modify_date, '') AS TIMESTAMP),
modify_id = product_type_work.modify_id,
dwh_updated_user = product_type_work.dwh_updated_user,
dwh_updated_datetime = product_type_work.dwh_updated_datetime
WHEN NOT MATCHED THEN INSERT VALUES (
product_type_work.product_type,
product_type_work.product_type_name,
product_type_work.product_type_nk,
CAST(NULLIF(product_type_work.insert_date, '') AS TIMESTAMP),
product_type_work.insert_id,
CAST(NULLIF(product_type_work.modify_date, '') AS TIMESTAMP),
product_type_work.modify_id,
product_type_work.dwh_created_user,
product_type_work.dwh_created_datetime,
product_type_work.dwh_updated_user,
product_type_work.dwh_updated_datetime
);