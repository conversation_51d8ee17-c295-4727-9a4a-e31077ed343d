MERGE INTO dwh_raw.member USING dwh_raw.member_work
ON member.id = member_work.id
WHEN MATCHED THEN UPDATE SET
isdeleted = member_work.isdeleted,
masterrecordid = member_work.masterrecordid,
name = member_work.name,
lastname = member_work.lastname,
firstname = member_work.firstname,
salutation = member_work.salutation,
type = member_work.type,
recordtypeid = member_work.recordtypeid,
parentid = member_work.parentid,
personmailingstreet = member_work.personmailingstreet,
personmailingcity = member_work.personmailingcity,
personmailingstate = member_work.personmailingstate,
personmailingstatecode = member_work.personmailingstatecode,
personmailingpostalcode = member_work.personmailingpostalcode,
personmailingcountry = member_work.personmailingcountry,
personmailinglatitude = member_work.personmailinglatitude,
personmailinglongitude = member_work.personmailinglongitude,
personmailinggeocodeaccuracy = member_work.personmailinggeocodeaccuracy,
personmailingaddress = member_work.personmailingaddress,
shippingstreet = member_work.shippingstreet,
shippingcity = member_work.shippingcity,
shippingstate = member_work.shippingstate,
shippingpostalcode = member_work.shippingpostalcode,
shippingcountry = member_work.shippingcountry,
shippinglatitude = member_work.shippinglatitude,
shippinglongitude = member_work.shippinglongitude,
shippinggeocodeaccuracy = member_work.shippinggeocodeaccuracy,
shippingaddress = member_work.shippingaddress,
phone = member_work.phone,
fax = member_work.fax,
accountnumber = member_work.accountnumber,
website = member_work.website,
photourl = member_work.photourl,
sic = member_work.sic,
industry = member_work.industry,
annualrevenue = member_work.annualrevenue,
numberofemployees = member_work.numberofemployees,
ownership = member_work.ownership,
tickersymbol = member_work.tickersymbol,
description = member_work.description,
rating = member_work.rating,
site = member_work.site,
ownerid = member_work.ownerid,
createddate = CAST(NULLIF(member_work.createddate, '') AS TIMESTAMP),
createdbyid = member_work.createdbyid,
lastmodifieddate = CAST(NULLIF(member_work.lastmodifieddate, '') AS TIMESTAMP),
lastmodifiedbyid = member_work.lastmodifiedbyid,
systemmodstamp = CAST(NULLIF(member_work.systemmodstamp, '') AS TIMESTAMP),
lastactivitydate = CAST(NULLIF(member_work.lastactivitydate, '') AS DATE),
lastvieweddate = CAST(NULLIF(member_work.lastvieweddate, '') AS TIMESTAMP),
lastreferenceddate = CAST(NULLIF(member_work.lastreferenceddate, '') AS TIMESTAMP),
ispersonaccount = member_work.ispersonaccount,
billingstreet = member_work.billingstreet,
billingcity = member_work.billingcity,
billingstate = member_work.billingstate,
billingpostalcode = member_work.billingpostalcode,
billingcountry = member_work.billingcountry,
billinglatitude = member_work.billinglatitude,
billinglongitude = member_work.billinglongitude,
billinggeocodeaccuracy = member_work.billinggeocodeaccuracy,
billingaddress = member_work.billingaddress,
personotherstreet = member_work.personotherstreet,
personothercity = member_work.personothercity,
personotherstate = member_work.personotherstate,
personotherpostalcode = member_work.personotherpostalcode,
personothercountry = member_work.personothercountry,
personotherlatitude = member_work.personotherlatitude,
personotherlongitude = member_work.personotherlongitude,
personothergeocodeaccuracy = member_work.personothergeocodeaccuracy,
personotheraddress = member_work.personotheraddress,
personmobilephone = member_work.personmobilephone,
personotherphone = member_work.personotherphone,
personassistantphone = member_work.personassistantphone,
personemail = member_work.personemail,
persontitle = member_work.persontitle,
persondepartment = member_work.persondepartment,
personassistantname = member_work.personassistantname,
personleadsource = member_work.personleadsource,
personbirthdate = CAST(NULLIF(member_work.personbirthdate, '') AS DATE),
personhasoptedoutofemail = member_work.personhasoptedoutofemail,
personhasoptedoutoffax = member_work.personhasoptedoutoffax,
persondonotcall = member_work.persondonotcall,
personlastcurequestdate = CAST(NULLIF(member_work.personlastcurequestdate, '') AS TIMESTAMP),
personlastcuupdatedate = CAST(NULLIF(member_work.personlastcuupdatedate, '') AS TIMESTAMP),
personemailbouncedreason = member_work.personemailbouncedreason,
personemailbounceddate = CAST(NULLIF(member_work.personemailbounceddate, '') AS TIMESTAMP),
personindividualid = member_work.personindividualid,
personpronouns = member_work.personpronouns,
persongenderidentity = member_work.persongenderidentity,
jigsaw = member_work.jigsaw,
jigsawcompanyid = member_work.jigsawcompanyid,
accountsource = member_work.accountsource,
sicdesc = member_work.sicdesc,
gender__c = member_work.gender__c,
number__c = member_work.number__c,
preferredshipmentservice__c = member_work.preferredshipmentservice__c,
accountcloseddate__c = CAST(NULLIF(member_work.accountcloseddate__c, '') AS DATE),
accountclosedreason__c = member_work.accountclosedreason__c,
shopcardbarcode__c = member_work.shopcardbarcode__c,
personmailingaddress__c = member_work.personmailingaddress__c,
isemployee__c = member_work.isemployee__c,
isoptedinemalmagazine__c = member_work.isoptedinemalmagazine__c,
emailmagazineunsubscribeddate__c = CAST(NULLIF(member_work.emailmagazineunsubscribeddate__c, '') AS DATE),
emailmagazinesubscribeddate__c = CAST(NULLIF(member_work.emailmagazinesubscribeddate__c, '') AS DATE),
beautycatalogsendtype__c = member_work.beautycatalogsendtype__c,
healthcatalogsendtype__c = member_work.healthcatalogsendtype__c,
apparelcatalogsendtype__c = member_work.apparelcatalogsendtype__c,
medicinecatalogsendtype__c = member_work.medicinecatalogsendtype__c,
petcatalogsendtype__c = member_work.petcatalogsendtype__c,
faxpurchaseordersendtype__c = member_work.faxpurchaseordersendtype__c,
lastnamekana__c = member_work.lastnamekana__c,
firstnamekana__c = member_work.firstnamekana__c,
rank__c = member_work.rank__c,
source__c = member_work.source__c,
status__c = member_work.status__c,
memo__c = member_work.memo__c,
memoforstore__c = member_work.memoforstore__c,
isdhccreditcardowner__c = member_work.isdhccreditcardowner__c,
age__c = member_work.age__c,
isoptedindm__c = member_work.isoptedindm__c,
isoptedincatalog__c = member_work.isoptedincatalog__c,
isunmailablepostway__c = member_work.isunmailablepostway__c,
isunmailablepost__c = member_work.isunmailablepost__c,
isstoporder__c = member_work.isstoporder__c,
isordermonitoring__c = member_work.isordermonitoring__c,
isrequiredcaution__c = member_work.isrequiredcaution__c,
guestorderonly__c = member_work.guestorderonly__c,
customernumber__c = member_work.customernumber__c,
isoptedinsurvey__c = member_work.isoptedinsurvey__c,
margedaccountid__c = member_work.margedaccountid__c,
rankexpirydate__c = CAST(NULLIF(member_work.rankexpirydate__c, '') AS DATE),
preferredcontactway__c = member_work.preferredcontactway__c,
lineminiappuserid__c = member_work.lineminiappuserid__c,
namekana__c = member_work.namekana__c,
birthdate__c = CAST(NULLIF(member_work.birthdate__c, '') AS DATE),
emailmagazineoptedouturl__c = member_work.emailmagazineoptedouturl__c,
personemail__c = member_work.personemail__c,
optinstoreemailmagazinestatus__c = member_work.optinstoreemailmagazinestatus__c,
storeemailmagazineemail__c = member_work.storeemailmagazineemail__c,
storeemailmagazineoptedoutkey__c = member_work.storeemailmagazineoptedoutkey__c,
storeemailmagazineoptedouturl__c = member_work.storeemailmagazineoptedouturl__c,
tonariwaid__c = member_work.tonariwaid__c,
storeemailmagazinestorecode__c = member_work.storeemailmagazinestorecode__c,
unmailablereason__c = member_work.unmailablereason__c,
dwh_updated_user = member_work.dwh_updated_user,
dwh_updated_datetime = member_work.dwh_updated_datetime
WHEN NOT MATCHED THEN INSERT VALUES (
member_work.id,
member_work.isdeleted,
member_work.masterrecordid,
member_work.name,
member_work.lastname,
member_work.firstname,
member_work.salutation,
member_work.type,
member_work.recordtypeid,
member_work.parentid,
member_work.personmailingstreet,
member_work.personmailingcity,
member_work.personmailingstate,
member_work.personmailingstatecode,
member_work.personmailingpostalcode,
member_work.personmailingcountry,
member_work.personmailinglatitude,
member_work.personmailinglongitude,
member_work.personmailinggeocodeaccuracy,
member_work.personmailingaddress,
member_work.shippingstreet,
member_work.shippingcity,
member_work.shippingstate,
member_work.shippingpostalcode,
member_work.shippingcountry,
member_work.shippinglatitude,
member_work.shippinglongitude,
member_work.shippinggeocodeaccuracy,
member_work.shippingaddress,
member_work.phone,
member_work.fax,
member_work.accountnumber,
member_work.website,
member_work.photourl,
member_work.sic,
member_work.industry,
member_work.annualrevenue,
member_work.numberofemployees,
member_work.ownership,
member_work.tickersymbol,
member_work.description,
member_work.rating,
member_work.site,
member_work.ownerid,
CAST(NULLIF(member_work.createddate, '') AS TIMESTAMP),
member_work.createdbyid,
CAST(NULLIF(member_work.lastmodifieddate, '') AS TIMESTAMP),
member_work.lastmodifiedbyid,
CAST(NULLIF(member_work.systemmodstamp, '') AS TIMESTAMP),
CAST(NULLIF(member_work.lastactivitydate, '') AS DATE),
CAST(NULLIF(member_work.lastvieweddate, '') AS TIMESTAMP),
CAST(NULLIF(member_work.lastreferenceddate, '') AS TIMESTAMP),
member_work.ispersonaccount,
member_work.billingstreet,
member_work.billingcity,
member_work.billingstate,
member_work.billingpostalcode,
member_work.billingcountry,
member_work.billinglatitude,
member_work.billinglongitude,
member_work.billinggeocodeaccuracy,
member_work.billingaddress,
member_work.personotherstreet,
member_work.personothercity,
member_work.personotherstate,
member_work.personotherpostalcode,
member_work.personothercountry,
member_work.personotherlatitude,
member_work.personotherlongitude,
member_work.personothergeocodeaccuracy,
member_work.personotheraddress,
member_work.personmobilephone,
member_work.personotherphone,
member_work.personassistantphone,
member_work.personemail,
member_work.persontitle,
member_work.persondepartment,
member_work.personassistantname,
member_work.personleadsource,
CAST(NULLIF(member_work.personbirthdate, '') AS DATE),
member_work.personhasoptedoutofemail,
member_work.personhasoptedoutoffax,
member_work.persondonotcall,
CAST(NULLIF(member_work.personlastcurequestdate, '') AS TIMESTAMP),
CAST(NULLIF(member_work.personlastcuupdatedate, '') AS TIMESTAMP),
member_work.personemailbouncedreason,
CAST(NULLIF(member_work.personemailbounceddate, '') AS TIMESTAMP),
member_work.personindividualid,
member_work.personpronouns,
member_work.persongenderidentity,
member_work.jigsaw,
member_work.jigsawcompanyid,
member_work.accountsource,
member_work.sicdesc,
member_work.gender__c,
member_work.number__c,
member_work.preferredshipmentservice__c,
CAST(NULLIF(member_work.accountcloseddate__c, '') AS DATE),
member_work.accountclosedreason__c,
member_work.shopcardbarcode__c,
member_work.personmailingaddress__c,
member_work.isemployee__c,
member_work.isoptedinemalmagazine__c,
CAST(NULLIF(member_work.emailmagazineunsubscribeddate__c, '') AS DATE),
CAST(NULLIF(member_work.emailmagazinesubscribeddate__c, '') AS DATE),
member_work.beautycatalogsendtype__c,
member_work.healthcatalogsendtype__c,
member_work.apparelcatalogsendtype__c,
member_work.medicinecatalogsendtype__c,
member_work.petcatalogsendtype__c,
member_work.faxpurchaseordersendtype__c,
member_work.lastnamekana__c,
member_work.firstnamekana__c,
member_work.rank__c,
member_work.source__c,
member_work.status__c,
member_work.memo__c,
member_work.memoforstore__c,
member_work.isdhccreditcardowner__c,
member_work.age__c,
member_work.isoptedindm__c,
member_work.isoptedincatalog__c,
member_work.isunmailablepostway__c,
member_work.isunmailablepost__c,
member_work.isstoporder__c,
member_work.isordermonitoring__c,
member_work.isrequiredcaution__c,
member_work.guestorderonly__c,
member_work.customernumber__c,
member_work.isoptedinsurvey__c,
member_work.margedaccountid__c,
CAST(NULLIF(member_work.rankexpirydate__c, '') AS DATE),
member_work.preferredcontactway__c,
member_work.lineminiappuserid__c,
member_work.namekana__c,
CAST(NULLIF(member_work.birthdate__c, '') AS DATE),
member_work.emailmagazineoptedouturl__c,
member_work.personemail__c,
member_work.optinstoreemailmagazinestatus__c,
member_work.storeemailmagazineemail__c,
member_work.storeemailmagazineoptedoutkey__c,
member_work.storeemailmagazineoptedouturl__c,
member_work.tonariwaid__c,
member_work.storeemailmagazinestorecode__c,
member_work.unmailablereason__c,
member_work.dwh_created_user,
member_work.dwh_created_datetime,
member_work.dwh_updated_user,
member_work.dwh_updated_datetime
);