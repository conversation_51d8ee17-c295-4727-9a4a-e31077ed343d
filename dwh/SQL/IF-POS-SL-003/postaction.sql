MERGE INTO dwh_raw.sales_info_alignment_header USING dwh_raw.sales_info_alignment_header_work
ON sales_info_alignment_header.shop_cd = sales_info_alignment_header_work.shop_cd AND
 sales_info_alignment_header.register_num = sales_info_alignment_header_work.register_num AND
 sales_info_alignment_header.business_date = sales_info_alignment_header_work.business_date AND
 sales_info_alignment_header.receipt_num = sales_info_alignment_header_work.receipt_num
WHEN MATCHED THEN UPDATE SET
chit_num = sales_info_alignment_header_work.chit_num,
system_datetime = CAST(NULLIF(sales_info_alignment_header_work.system_datetime, '') AS TIMESTAMP),
open_count = sales_info_alignment_header_work.open_count,
is_void = sales_info_alignment_header_work.is_void,
check_kind = sales_info_alignment_header_work.check_kind,
return_kind = sales_info_alignment_header_work.return_kind,
sub_check_kind = sales_info_alignment_header_work.sub_check_kind,
sales_group_cd = sales_info_alignment_header_work.sales_group_cd,
deposit_kind = sales_info_alignment_header_work.deposit_kind,
operator_cd = sales_info_alignment_header_work.operator_cd,
operator_name = sales_info_alignment_header_work.operator_name,
sales_man_cd = sales_info_alignment_header_work.sales_man_cd,
sales_man_name = sales_info_alignment_header_work.sales_man_name,
return_employee_cd = sales_info_alignment_header_work.return_employee_cd,
return_employee_name = sales_info_alignment_header_work.return_employee_name,
void_employee_cd = sales_info_alignment_header_work.void_employee_cd,
void_employee_name = sales_info_alignment_header_work.void_employee_name,
staff_sale_employee_cd = sales_info_alignment_header_work.staff_sale_employee_cd,
staff_sale_employee_name = sales_info_alignment_header_work.staff_sale_employee_name,
customer_cd = sales_info_alignment_header_work.customer_cd,
customer_layer_cd = sales_info_alignment_header_work.customer_layer_cd,
customer_layer_cd2 = sales_info_alignment_header_work.customer_layer_cd2,
purchase_motive_cd = sales_info_alignment_header_work.purchase_motive_cd,
return_reason_cd = sales_info_alignment_header_work.return_reason_cd,
void_reason_cd = sales_info_alignment_header_work.void_reason_cd,
net_sales_amount_of_outside_tax = sales_info_alignment_header_work.net_sales_amount_of_outside_tax,
net_sales_amount_of_inside_tax = sales_info_alignment_header_work.net_sales_amount_of_inside_tax,
net_sales_amount_of_tax_free = sales_info_alignment_header_work.net_sales_amount_of_tax_free,
net_sales_outside_tax = sales_info_alignment_header_work.net_sales_outside_tax,
net_sales_inside_tax = sales_info_alignment_header_work.net_sales_inside_tax,
net_sales_quantity = sales_info_alignment_header_work.net_sales_quantity,
outside_sales_amount_of_outside_tax = sales_info_alignment_header_work.outside_sales_amount_of_outside_tax,
outside_sales_amount_of_inside_tax = sales_info_alignment_header_work.outside_sales_amount_of_inside_tax,
outside_sales_amount_of_tax_free = sales_info_alignment_header_work.outside_sales_amount_of_tax_free,
outside_sales_outside_tax = sales_info_alignment_header_work.outside_sales_outside_tax,
outside_sales_inside_tax = sales_info_alignment_header_work.outside_sales_inside_tax,
outside_sales_quantity = sales_info_alignment_header_work.outside_sales_quantity,
total_amount = sales_info_alignment_header_work.total_amount,
discount_amount = sales_info_alignment_header_work.discount_amount,
discount_tax_inclusive = sales_info_alignment_header_work.discount_tax_inclusive,
is_revenue_stamp = sales_info_alignment_header_work.is_revenue_stamp,
order_line_count = sales_info_alignment_header_work.order_line_count,
pay_line_count = sales_info_alignment_header_work.pay_line_count,
is_total_display = sales_info_alignment_header_work.is_total_display,
is_reduced_tax_rate_trade = sales_info_alignment_header_work.is_reduced_tax_rate_trade,
is_tax_free = sales_info_alignment_header_work.is_tax_free,
customers_num = sales_info_alignment_header_work.customers_num,
deliver_date = CAST(NULLIF(sales_info_alignment_header_work.deliver_date, '') AS TIMESTAMP),
sale_attribute_cd1 = sales_info_alignment_header_work.sale_attribute_cd1,
sale_attribute_cd2 = sales_info_alignment_header_work.sale_attribute_cd2,
sale_attribute_cd3 = sales_info_alignment_header_work.sale_attribute_cd3,
sale_attribute_cd4 = sales_info_alignment_header_work.sale_attribute_cd4,
campaign_no = sales_info_alignment_header_work.campaign_no,
closing_date = CAST(NULLIF(sales_info_alignment_header_work.closing_date, '') AS DATE),
return_date = CAST(NULLIF(sales_info_alignment_header_work.return_date, '') AS DATE),
order_number = sales_info_alignment_header_work.order_number,
employee_meal = sales_info_alignment_header_work.employee_meal,
employee_code = sales_info_alignment_header_work.employee_code,
table_no = sales_info_alignment_header_work.table_no,
acceptance_time = CAST(NULLIF(sales_info_alignment_header_work.acceptance_time, '') AS TIMESTAMP),
menu_cook_cmp_time = CAST(NULLIF(sales_info_alignment_header_work.menu_cook_cmp_time, '') AS TIMESTAMP),
menu_offer_cmp_time = CAST(NULLIF(sales_info_alignment_header_work.menu_offer_cmp_time, '') AS TIMESTAMP),
service_charge_amount_outside_tax = sales_info_alignment_header_work.service_charge_amount_outside_tax,
service_charge_amount_inside_tax = sales_info_alignment_header_work.service_charge_amount_inside_tax,
service_charge_tax_exclusive = sales_info_alignment_header_work.service_charge_tax_exclusive,
service_charge_tax_inclusive = sales_info_alignment_header_work.service_charge_tax_inclusive,
service_charge_amount1 = sales_info_alignment_header_work.service_charge_amount1,
service_charge_amount2 = sales_info_alignment_header_work.service_charge_amount2,
service_charge_minus_amount = sales_info_alignment_header_work.service_charge_minus_amount,
service_charge_minus_tax_inclusive = sales_info_alignment_header_work.service_charge_minus_tax_inclusive,
service_charge_target = sales_info_alignment_header_work.service_charge_target,
service_charge_button = sales_info_alignment_header_work.service_charge_button,
service_charge1_button = sales_info_alignment_header_work.service_charge1_button,
service_charge2_button = sales_info_alignment_header_work.service_charge2_button,
eat_in_amount = sales_info_alignment_header_work.eat_in_amount,
takeout_amount = sales_info_alignment_header_work.takeout_amount,
vein_employee_cd = sales_info_alignment_header_work.vein_employee_cd,
is_vein_authentication = sales_info_alignment_header_work.is_vein_authentication,
out_calc_flg = sales_info_alignment_header_work.out_calc_flg,
sale_goods_flg = sales_info_alignment_header_work.sale_goods_flg,
point_linkage_kind = sales_info_alignment_header_work.point_linkage_kind,
numeric_reserve1 = sales_info_alignment_header_work.numeric_reserve1,
numeric_reserve2 = sales_info_alignment_header_work.numeric_reserve2,
numeric_reserve3 = sales_info_alignment_header_work.numeric_reserve3,
numeric_reserve4 = sales_info_alignment_header_work.numeric_reserve4,
numeric_reserve5 = sales_info_alignment_header_work.numeric_reserve5,
numeric_reserve6 = sales_info_alignment_header_work.numeric_reserve6,
numeric_reserve7 = sales_info_alignment_header_work.numeric_reserve7,
numeric_reserve8 = sales_info_alignment_header_work.numeric_reserve8,
numeric_reserve9 = sales_info_alignment_header_work.numeric_reserve9,
numeric_reserve10 = sales_info_alignment_header_work.numeric_reserve10,
string_reserve1 = sales_info_alignment_header_work.string_reserve1,
string_reserve2 = sales_info_alignment_header_work.string_reserve2,
string_reserve3 = sales_info_alignment_header_work.string_reserve3,
string_reserve4 = sales_info_alignment_header_work.string_reserve4,
string_reserve5 = sales_info_alignment_header_work.string_reserve5,
string_reserve6 = sales_info_alignment_header_work.string_reserve6,
string_reserve7 = sales_info_alignment_header_work.string_reserve7,
string_reserve8 = sales_info_alignment_header_work.string_reserve8,
string_reserve9 = sales_info_alignment_header_work.string_reserve9,
string_reserve10 = sales_info_alignment_header_work.string_reserve10,
dwh_updated_user = sales_info_alignment_header_work.dwh_updated_user,
dwh_updated_datetime = sales_info_alignment_header_work.dwh_updated_datetime
WHEN NOT MATCHED THEN INSERT VALUES (
sales_info_alignment_header_work.shop_cd,
sales_info_alignment_header_work.register_num,
CAST(NULLIF(sales_info_alignment_header_work.business_date, '') AS DATE),
sales_info_alignment_header_work.receipt_num,
sales_info_alignment_header_work.chit_num,
CAST(NULLIF(sales_info_alignment_header_work.system_datetime, '') AS TIMESTAMP),
sales_info_alignment_header_work.open_count,
sales_info_alignment_header_work.is_void,
sales_info_alignment_header_work.check_kind,
sales_info_alignment_header_work.return_kind,
sales_info_alignment_header_work.sub_check_kind,
sales_info_alignment_header_work.sales_group_cd,
sales_info_alignment_header_work.deposit_kind,
sales_info_alignment_header_work.operator_cd,
sales_info_alignment_header_work.operator_name,
sales_info_alignment_header_work.sales_man_cd,
sales_info_alignment_header_work.sales_man_name,
sales_info_alignment_header_work.return_employee_cd,
sales_info_alignment_header_work.return_employee_name,
sales_info_alignment_header_work.void_employee_cd,
sales_info_alignment_header_work.void_employee_name,
sales_info_alignment_header_work.staff_sale_employee_cd,
sales_info_alignment_header_work.staff_sale_employee_name,
sales_info_alignment_header_work.customer_cd,
sales_info_alignment_header_work.customer_layer_cd,
sales_info_alignment_header_work.customer_layer_cd2,
sales_info_alignment_header_work.purchase_motive_cd,
sales_info_alignment_header_work.return_reason_cd,
sales_info_alignment_header_work.void_reason_cd,
sales_info_alignment_header_work.net_sales_amount_of_outside_tax,
sales_info_alignment_header_work.net_sales_amount_of_inside_tax,
sales_info_alignment_header_work.net_sales_amount_of_tax_free,
sales_info_alignment_header_work.net_sales_outside_tax,
sales_info_alignment_header_work.net_sales_inside_tax,
sales_info_alignment_header_work.net_sales_quantity,
sales_info_alignment_header_work.outside_sales_amount_of_outside_tax,
sales_info_alignment_header_work.outside_sales_amount_of_inside_tax,
sales_info_alignment_header_work.outside_sales_amount_of_tax_free,
sales_info_alignment_header_work.outside_sales_outside_tax,
sales_info_alignment_header_work.outside_sales_inside_tax,
sales_info_alignment_header_work.outside_sales_quantity,
sales_info_alignment_header_work.total_amount,
sales_info_alignment_header_work.discount_amount,
sales_info_alignment_header_work.discount_tax_inclusive,
sales_info_alignment_header_work.is_revenue_stamp,
sales_info_alignment_header_work.order_line_count,
sales_info_alignment_header_work.pay_line_count,
sales_info_alignment_header_work.is_total_display,
sales_info_alignment_header_work.is_reduced_tax_rate_trade,
sales_info_alignment_header_work.is_tax_free,
sales_info_alignment_header_work.customers_num,
CAST(NULLIF(sales_info_alignment_header_work.deliver_date, '') AS TIMESTAMP),
sales_info_alignment_header_work.sale_attribute_cd1,
sales_info_alignment_header_work.sale_attribute_cd2,
sales_info_alignment_header_work.sale_attribute_cd3,
sales_info_alignment_header_work.sale_attribute_cd4,
sales_info_alignment_header_work.campaign_no,
CAST(NULLIF(sales_info_alignment_header_work.closing_date, '') AS DATE),
CAST(NULLIF(sales_info_alignment_header_work.return_date, '') AS DATE),
sales_info_alignment_header_work.order_number,
sales_info_alignment_header_work.employee_meal,
sales_info_alignment_header_work.employee_code,
sales_info_alignment_header_work.table_no,
CAST(NULLIF(sales_info_alignment_header_work.acceptance_time, '') AS TIMESTAMP),
CAST(NULLIF(sales_info_alignment_header_work.menu_cook_cmp_time, '') AS TIMESTAMP),
CAST(NULLIF(sales_info_alignment_header_work.menu_offer_cmp_time, '') AS TIMESTAMP),
sales_info_alignment_header_work.service_charge_amount_outside_tax,
sales_info_alignment_header_work.service_charge_amount_inside_tax,
sales_info_alignment_header_work.service_charge_tax_exclusive,
sales_info_alignment_header_work.service_charge_tax_inclusive,
sales_info_alignment_header_work.service_charge_amount1,
sales_info_alignment_header_work.service_charge_amount2,
sales_info_alignment_header_work.service_charge_minus_amount,
sales_info_alignment_header_work.service_charge_minus_tax_inclusive,
sales_info_alignment_header_work.service_charge_target,
sales_info_alignment_header_work.service_charge_button,
sales_info_alignment_header_work.service_charge1_button,
sales_info_alignment_header_work.service_charge2_button,
sales_info_alignment_header_work.eat_in_amount,
sales_info_alignment_header_work.takeout_amount,
sales_info_alignment_header_work.vein_employee_cd,
sales_info_alignment_header_work.is_vein_authentication,
sales_info_alignment_header_work.out_calc_flg,
sales_info_alignment_header_work.sale_goods_flg,
sales_info_alignment_header_work.point_linkage_kind,
sales_info_alignment_header_work.numeric_reserve1,
sales_info_alignment_header_work.numeric_reserve2,
sales_info_alignment_header_work.numeric_reserve3,
sales_info_alignment_header_work.numeric_reserve4,
sales_info_alignment_header_work.numeric_reserve5,
sales_info_alignment_header_work.numeric_reserve6,
sales_info_alignment_header_work.numeric_reserve7,
sales_info_alignment_header_work.numeric_reserve8,
sales_info_alignment_header_work.numeric_reserve9,
sales_info_alignment_header_work.numeric_reserve10,
sales_info_alignment_header_work.string_reserve1,
sales_info_alignment_header_work.string_reserve2,
sales_info_alignment_header_work.string_reserve3,
sales_info_alignment_header_work.string_reserve4,
sales_info_alignment_header_work.string_reserve5,
sales_info_alignment_header_work.string_reserve6,
sales_info_alignment_header_work.string_reserve7,
sales_info_alignment_header_work.string_reserve8,
sales_info_alignment_header_work.string_reserve9,
sales_info_alignment_header_work.string_reserve10,
sales_info_alignment_header_work.dwh_created_user,
sales_info_alignment_header_work.dwh_created_datetime,
sales_info_alignment_header_work.dwh_updated_user,
sales_info_alignment_header_work.dwh_updated_datetime
);