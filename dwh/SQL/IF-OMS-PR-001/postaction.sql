MERGE INTO dwh_raw.category USING dwh_raw.category_work
ON category.category_code = category_work.category_code
WHEN MATCHED THEN UPDATE SET
category_name_pc = category_work.category_name_pc,
category_name_sp = category_work.category_name_sp,
parent_category_code = category_work.parent_category_code,
path = category_work.path,
depth = category_work.depth,
display_order = category_work.display_order,
commodity_count = category_work.commodity_count,
last_related_count_datetime = CAST(NULLIF(category_work.last_related_count_datetime, '') AS TIMESTAMP),
public_commodity_count = category_work.public_commodity_count,
last_public_count_datetime = CAST(NULLIF(category_work.last_public_count_datetime, '') AS TIMESTAMP),
orm_rowid = category_work.orm_rowid,
created_user = category_work.created_user,
created_datetime = CAST(NULLIF(category_work.created_datetime, '') AS TIMESTAMP),
updated_user = category_work.updated_user,
updated_datetime = CAST(NULLIF(category_work.updated_datetime, '') AS TIMESTAMP),
dwh_updated_user = category_work.dwh_updated_user,
dwh_updated_datetime = category_work.dwh_updated_datetime
WHEN NOT MATCHED THEN INSERT VALUES (
category_work.category_code,
category_work.category_name_pc,
category_work.category_name_sp,
category_work.parent_category_code,
category_work.path,
category_work.depth,
category_work.display_order,
category_work.commodity_count,
CAST(NULLIF(category_work.last_related_count_datetime, '') AS TIMESTAMP),
category_work.public_commodity_count,
CAST(NULLIF(category_work.last_public_count_datetime, '') AS TIMESTAMP),
category_work.orm_rowid,
category_work.created_user,
CAST(NULLIF(category_work.created_datetime, '') AS TIMESTAMP),
category_work.updated_user,
CAST(NULLIF(category_work.updated_datetime, '') AS TIMESTAMP),
category_work.dwh_created_user,
category_work.dwh_created_datetime,
category_work.dwh_updated_user,
category_work.dwh_updated_datetime
);