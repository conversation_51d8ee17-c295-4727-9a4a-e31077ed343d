MERGE INTO dwh_raw.mgroup USING dwh_raw.mgroup_work
ON mgroup.lgroup = mgroup_work.lgroup AND
 mgroup.mgroup = mgroup_work.mgroup
WHEN MATCHED THEN UPDATE SET
mgroup_name = mgroup_work.mgroup_name,
lgroup_nk = mgroup_work.lgroup_nk,
insert_date = CAST(NULLIF(mgroup_work.insert_date, '') AS TIMESTAMP),
insert_id = mgroup_work.insert_id,
modify_date = CAST(NULLIF(mgroup_work.modify_date, '') AS TIMESTAMP),
modify_id = mgroup_work.modify_id,
dwh_updated_user = mgroup_work.dwh_updated_user,
dwh_updated_datetime = mgroup_work.dwh_updated_datetime
WHEN NOT MATCHED THEN INSERT VALUES (
mgroup_work.lgroup,
mgroup_work.mgroup,
mgroup_work.mgroup_name,
mgroup_work.lgroup_nk,
CAST(NULLIF(mgroup_work.insert_date, '') AS TIMESTAMP),
mgroup_work.insert_id,
CAST(NULLIF(mgroup_work.modify_date, '') AS TIMESTAMP),
mgroup_work.modify_id,
mgroup_work.dwh_created_user,
mgroup_work.dwh_created_datetime,
mgroup_work.dwh_updated_user,
mgroup_work.dwh_updated_datetime
);