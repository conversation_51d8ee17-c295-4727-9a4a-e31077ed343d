MERGE INTO dwh_raw.regular_sale_commodity USING dwh_raw.regular_sale_commodity_work
ON regular_sale_commodity.shop_code = regular_sale_commodity_work.shop_code AND
 regular_sale_commodity.regular_sale_code = regular_sale_commodity_work.regular_sale_code AND
 regular_sale_commodity.regular_sale_composition_no = regular_sale_commodity_work.regular_sale_composition_no AND
 regular_sale_commodity.sku_code = regular_sale_commodity_work.sku_code
WHEN MATCHED THEN UPDATE SET
commodity_code = regular_sale_commodity_work.commodity_code,
display_order = regular_sale_commodity_work.display_order,
regular_sale_commodity_type = regular_sale_commodity_work.regular_sale_commodity_type,
regular_sale_commodity_point = regular_sale_commodity_work.regular_sale_commodity_point,
difference_price = regular_sale_commodity_work.difference_price,
orm_rowid = regular_sale_commodity_work.orm_rowid,
created_user = regular_sale_commodity_work.created_user,
created_datetime = CAST(NULLIF(regular_sale_commodity_work.created_datetime, '') AS TIMESTAMP),
updated_user = regular_sale_commodity_work.updated_user,
updated_datetime = CAST(NULLIF(regular_sale_commodity_work.updated_datetime, '') AS TIMESTAMP),
dwh_updated_user = regular_sale_commodity_work.dwh_updated_user,
dwh_updated_datetime = regular_sale_commodity_work.dwh_updated_datetime
WHEN NOT MATCHED THEN INSERT VALUES (
regular_sale_commodity_work.shop_code,
regular_sale_commodity_work.regular_sale_code,
regular_sale_commodity_work.regular_sale_composition_no,
regular_sale_commodity_work.sku_code,
regular_sale_commodity_work.commodity_code,
regular_sale_commodity_work.display_order,
regular_sale_commodity_work.regular_sale_commodity_type,
regular_sale_commodity_work.regular_sale_commodity_point,
regular_sale_commodity_work.difference_price,
regular_sale_commodity_work.orm_rowid,
regular_sale_commodity_work.created_user,
CAST(NULLIF(regular_sale_commodity_work.created_datetime, '') AS TIMESTAMP),
regular_sale_commodity_work.updated_user,
CAST(NULLIF(regular_sale_commodity_work.updated_datetime, '') AS TIMESTAMP),
regular_sale_commodity_work.dwh_created_user,
regular_sale_commodity_work.dwh_created_datetime,
regular_sale_commodity_work.dwh_updated_user,
regular_sale_commodity_work.dwh_updated_datetime
);