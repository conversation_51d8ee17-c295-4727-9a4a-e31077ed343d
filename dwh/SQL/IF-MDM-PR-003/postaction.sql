MERGE INTO dwh_raw.period_price_linkage USING dwh_raw.period_price_linkage_work
ON period_price_linkage.mdm_integration_management_cd = period_price_linkage_work.mdm_integration_management_cd AND
 period_price_linkage.apply_start_date = period_price_linkage_work.apply_start_date
WHEN MATCHED THEN UPDATE SET
tax_exc = period_price_linkage_work.tax_exc,
tax_inc = period_price_linkage_work.tax_inc,
tax = period_price_linkage_work.tax,
tax_rate = period_price_linkage_work.tax_rate,
mdm_integration_management_cd_nk = period_price_linkage_work.mdm_integration_management_cd_nk,
insert_date = CAST(NULLIF(period_price_linkage_work.insert_date, '') AS TIMESTAMP),
insert_id = period_price_linkage_work.insert_id,
modify_date = CAST(NULLIF(period_price_linkage_work.modify_date, '') AS TIMESTAMP),
modify_id = period_price_linkage_work.modify_id,
dwh_updated_user = period_price_linkage_work.dwh_updated_user,
dwh_updated_datetime = period_price_linkage_work.dwh_updated_datetime
WHEN NOT MATCHED THEN INSERT VALUES (
period_price_linkage_work.mdm_integration_management_cd,
period_price_linkage_work.tax_exc,
period_price_linkage_work.tax_inc,
period_price_linkage_work.tax,
period_price_linkage_work.tax_rate,
CAST(NULLIF(period_price_linkage_work.apply_start_date, '') AS TIMESTAMP),
period_price_linkage_work.mdm_integration_management_cd_nk,
CAST(NULLIF(period_price_linkage_work.insert_date, '') AS TIMESTAMP),
period_price_linkage_work.insert_id,
CAST(NULLIF(period_price_linkage_work.modify_date, '') AS TIMESTAMP),
period_price_linkage_work.modify_id,
period_price_linkage_work.dwh_created_user,
period_price_linkage_work.dwh_created_datetime,
period_price_linkage_work.dwh_updated_user,
period_price_linkage_work.dwh_updated_datetime
);