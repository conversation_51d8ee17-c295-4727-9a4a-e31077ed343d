MERGE INTO dwh_raw.coupon USING dwh_raw.coupon_work
ON coupon.coupon_management_code = coupon_work.coupon_management_code
WHEN MATCHED THEN UPDATE SET
coupon_code = coupon_work.coupon_code,
coupon_name = coupon_work.coupon_name,
coupon_invalid_flag = coupon_work.coupon_invalid_flag,
coupon_type = coupon_work.coupon_type,
coupon_issue_type = coupon_work.coupon_issue_type,
coupon_use_limit = coupon_work.coupon_use_limit,
coupon_use_purchase_price = coupon_work.coupon_use_purchase_price,
coupon_discount_type = coupon_work.coupon_discount_type,
coupon_discount_rate = coupon_work.coupon_discount_rate,
coupon_discount_price = coupon_work.coupon_discount_price,
coupon_start_datetime = CAST(NULLIF(coupon_work.coupon_start_datetime, '') AS TIMESTAMP),
coupon_end_datetime = CAST(NULLIF(coupon_work.coupon_end_datetime, '') AS TIMESTAMP),
coupon_limit_display_period = coupon_work.coupon_limit_display_period,
coupon_limit_display = coupon_work.coupon_limit_display,
coupon_description = coupon_work.coupon_description,
coupon_message = coupon_work.coupon_message,
coupon_kbn = coupon_work.coupon_kbn,
coupon_post_in_charge = coupon_work.coupon_post_in_charge,
coupon_commodity_flag = coupon_work.coupon_commodity_flag,
marketing_channel_list = coupon_work.marketing_channel_list,
goods_group = coupon_work.goods_group,
commodity_category_code = coupon_work.commodity_category_code,
commodity_series = coupon_work.commodity_series,
orm_rowid = coupon_work.orm_rowid,
created_user = coupon_work.created_user,
created_datetime = CAST(NULLIF(coupon_work.created_datetime, '') AS TIMESTAMP),
updated_user = coupon_work.updated_user,
updated_datetime = CAST(NULLIF(coupon_work.updated_datetime, '') AS TIMESTAMP),
dwh_updated_user = coupon_work.dwh_updated_user,
dwh_updated_datetime = coupon_work.dwh_updated_datetime
WHEN NOT MATCHED THEN INSERT VALUES (
coupon_work.coupon_management_code,
coupon_work.coupon_code,
coupon_work.coupon_name,
coupon_work.coupon_invalid_flag,
coupon_work.coupon_type,
coupon_work.coupon_issue_type,
coupon_work.coupon_use_limit,
coupon_work.coupon_use_purchase_price,
coupon_work.coupon_discount_type,
coupon_work.coupon_discount_rate,
coupon_work.coupon_discount_price,
CAST(NULLIF(coupon_work.coupon_start_datetime, '') AS TIMESTAMP),
CAST(NULLIF(coupon_work.coupon_end_datetime, '') AS TIMESTAMP),
coupon_work.coupon_limit_display_period,
coupon_work.coupon_limit_display,
coupon_work.coupon_description,
coupon_work.coupon_message,
coupon_work.coupon_kbn,
coupon_work.coupon_post_in_charge,
coupon_work.coupon_commodity_flag,
coupon_work.marketing_channel_list,
coupon_work.goods_group,
coupon_work.commodity_category_code,
coupon_work.commodity_series,
coupon_work.orm_rowid,
coupon_work.created_user,
CAST(NULLIF(coupon_work.created_datetime, '') AS TIMESTAMP),
coupon_work.updated_user,
CAST(NULLIF(coupon_work.updated_datetime, '') AS TIMESTAMP),
coupon_work.dwh_created_user,
coupon_work.dwh_created_datetime,
coupon_work.dwh_updated_user,
coupon_work.dwh_updated_datetime
);