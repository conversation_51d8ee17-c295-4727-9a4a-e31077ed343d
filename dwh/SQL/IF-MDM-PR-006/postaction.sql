MERGE INTO dwh_raw.product_series USING dwh_raw.product_series_work
ON product_series.product_series = product_series_work.product_series
WHEN MATCHED THEN UPDATE SET
product_series_name = product_series_work.product_series_name,
product_series_nk = product_series_work.product_series_nk,
insert_date = CAST(NULLIF(product_series_work.insert_date, '') AS TIMESTAMP),
insert_id = product_series_work.insert_id,
modify_date = CAST(NULLIF(product_series_work.modify_date, '') AS TIMESTAMP),
modify_id = product_series_work.modify_id,
dwh_updated_user = product_series_work.dwh_updated_user,
dwh_updated_datetime = product_series_work.dwh_updated_datetime
WHEN NOT MATCHED THEN INSERT VALUES (
product_series_work.product_series,
product_series_work.product_series_name,
product_series_work.product_series_nk,
CAST(NULLIF(product_series_work.insert_date, '') AS TIMESTAMP),
product_series_work.insert_id,
CAST(NULLIF(product_series_work.modify_date, '') AS TIMESTAMP),
product_series_work.modify_id,
product_series_work.dwh_created_user,
product_series_work.dwh_created_datetime,
product_series_work.dwh_updated_user,
product_series_work.dwh_updated_datetime
);