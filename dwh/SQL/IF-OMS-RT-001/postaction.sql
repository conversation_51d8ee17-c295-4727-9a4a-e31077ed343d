MERGE INTO dwh_raw.returns_header USING dwh_raw.returns_header_work
ON returns_header.henpin_request_no = returns_header_work.henpin_request_no
WHEN MATCHED THEN UPDATE SET
order_no = returns_header_work.order_no,
customer_code = returns_header_work.customer_code,
neo_customer_no = returns_header_work.neo_customer_no,
henpin_confirm_status = returns_header_work.henpin_confirm_status,
henpin_request_datetime = CAST(NULLIF(returns_header_work.henpin_request_datetime, '') AS TIMESTAMP),
henpin_confirm_datetime = CAST(NULLIF(returns_header_work.henpin_confirm_datetime, '') AS TIMESTAMP),
henpin_recieve_user_code = returns_header_work.henpin_recieve_user_code,
henpin_change_kbn = returns_header_work.henpin_change_kbn,
henpin_reason_kbn = returns_header_work.henpin_reason_kbn,
henpin_bill_cancel_flg = returns_header_work.henpin_bill_cancel_flg,
henpin_shipping_flg = returns_header_work.henpin_shipping_flg,
henpin_souko_kbn = returns_header_work.henpin_souko_kbn,
total_shipping_amount = returns_header_work.total_shipping_amount,
adjustment_amount = returns_header_work.adjustment_amount,
bill_price = returns_header_work.bill_price,
bill_price_bf_henpin = returns_header_work.bill_price_bf_henpin,
appropriation_amount_bf_henpin = returns_header_work.appropriation_amount_bf_henpin,
deposit_occur_amount = returns_header_work.deposit_occur_amount,
delivery_note_republish_flg = returns_header_work.delivery_note_republish_flg,
credit_cancel_flg = returns_header_work.credit_cancel_flg,
request_af_henpin_confirm_kbn = returns_header_work.request_af_henpin_confirm_kbn,
amzn_refund_flg = returns_header_work.amzn_refund_flg,
amzn_refund_id = returns_header_work.amzn_refund_id,
amzn_refund_status = returns_header_work.amzn_refund_status,
amzn_refund_initiated_datetime = CAST(NULLIF(returns_header_work.amzn_refund_initiated_datetime, '') AS TIMESTAMP),
amzn_refunded_datetime = CAST(NULLIF(returns_header_work.amzn_refunded_datetime, '') AS TIMESTAMP),
refund_amount = returns_header_work.refund_amount,
bank_name = returns_header_work.bank_name,
bank_branch_name = returns_header_work.bank_branch_name,
account_type = returns_header_work.account_type,
account_no = returns_header_work.account_no,
account_name = returns_header_work.account_name,
registered_mail_address = returns_header_work.registered_mail_address,
registered_mail_name = returns_header_work.registered_mail_name,
registered_remarks = returns_header_work.registered_remarks,
appropriation_date = CAST(NULLIF(returns_header_work.appropriation_date, '') AS DATE),
appropriation_amount = returns_header_work.appropriation_amount,
wms_contact_flg = returns_header_work.wms_contact_flg,
wms_auto_confirm_flg = returns_header_work.wms_auto_confirm_flg,
sales_recording_date = CAST(NULLIF(returns_header_work.sales_recording_date, '') AS DATE),
sales_recording_flg = returns_header_work.sales_recording_flg,
inquiry_kanri_no = returns_header_work.inquiry_kanri_no,
cancel_flg = returns_header_work.cancel_flg,
change_user_code = returns_header_work.change_user_code,
before_grant_point_total = returns_header_work.before_grant_point_total,
before_reduction_point_total = returns_header_work.before_reduction_point_total,
after_grant_plan_point_prod = returns_header_work.after_grant_plan_point_prod,
after_grant_plan_point_other = returns_header_work.after_grant_plan_point_other,
after_grant_plan_point_total = returns_header_work.after_grant_plan_point_total,
after_grant_point_prod = returns_header_work.after_grant_point_prod,
after_grant_point_other = returns_header_work.after_grant_point_other,
after_grant_point_total = returns_header_work.after_grant_point_total,
after_reduction_plan_point_total = returns_header_work.after_reduction_plan_point_total,
after_reduction_point_total = returns_header_work.after_reduction_point_total,
orm_rowid = returns_header_work.orm_rowid,
created_user = returns_header_work.created_user,
created_datetime = CAST(NULLIF(returns_header_work.created_datetime, '') AS TIMESTAMP),
updated_user = returns_header_work.updated_user,
updated_datetime = CAST(NULLIF(returns_header_work.updated_datetime, '') AS TIMESTAMP),
dwh_updated_user = returns_header_work.dwh_updated_user,
dwh_updated_datetime = returns_header_work.dwh_updated_datetime
WHEN NOT MATCHED THEN INSERT VALUES (
returns_header_work.henpin_request_no,
returns_header_work.order_no,
returns_header_work.customer_code,
returns_header_work.neo_customer_no,
returns_header_work.henpin_confirm_status,
CAST(NULLIF(returns_header_work.henpin_request_datetime, '') AS TIMESTAMP),
CAST(NULLIF(returns_header_work.henpin_confirm_datetime, '') AS TIMESTAMP),
returns_header_work.henpin_recieve_user_code,
returns_header_work.henpin_change_kbn,
returns_header_work.henpin_reason_kbn,
returns_header_work.henpin_bill_cancel_flg,
returns_header_work.henpin_shipping_flg,
returns_header_work.henpin_souko_kbn,
returns_header_work.total_shipping_amount,
returns_header_work.adjustment_amount,
returns_header_work.bill_price,
returns_header_work.bill_price_bf_henpin,
returns_header_work.appropriation_amount_bf_henpin,
returns_header_work.deposit_occur_amount,
returns_header_work.delivery_note_republish_flg,
returns_header_work.credit_cancel_flg,
returns_header_work.request_af_henpin_confirm_kbn,
returns_header_work.amzn_refund_flg,
returns_header_work.amzn_refund_id,
returns_header_work.amzn_refund_status,
CAST(NULLIF(returns_header_work.amzn_refund_initiated_datetime, '') AS TIMESTAMP),
CAST(NULLIF(returns_header_work.amzn_refunded_datetime, '') AS TIMESTAMP),
returns_header_work.refund_amount,
returns_header_work.bank_name,
returns_header_work.bank_branch_name,
returns_header_work.account_type,
returns_header_work.account_no,
returns_header_work.account_name,
returns_header_work.registered_mail_address,
returns_header_work.registered_mail_name,
returns_header_work.registered_remarks,
CAST(NULLIF(returns_header_work.appropriation_date, '') AS DATE),
returns_header_work.appropriation_amount,
returns_header_work.wms_contact_flg,
returns_header_work.wms_auto_confirm_flg,
CAST(NULLIF(returns_header_work.sales_recording_date, '') AS DATE),
returns_header_work.sales_recording_flg,
returns_header_work.inquiry_kanri_no,
returns_header_work.cancel_flg,
returns_header_work.change_user_code,
returns_header_work.before_grant_point_total,
returns_header_work.before_reduction_point_total,
returns_header_work.after_grant_plan_point_prod,
returns_header_work.after_grant_plan_point_other,
returns_header_work.after_grant_plan_point_total,
returns_header_work.after_grant_point_prod,
returns_header_work.after_grant_point_other,
returns_header_work.after_grant_point_total,
returns_header_work.after_reduction_plan_point_total,
returns_header_work.after_reduction_point_total,
returns_header_work.orm_rowid,
returns_header_work.created_user,
CAST(NULLIF(returns_header_work.created_datetime, '') AS TIMESTAMP),
returns_header_work.updated_user,
CAST(NULLIF(returns_header_work.updated_datetime, '') AS TIMESTAMP),
returns_header_work.dwh_created_user,
returns_header_work.dwh_created_datetime,
returns_header_work.dwh_updated_user,
returns_header_work.dwh_updated_datetime
);