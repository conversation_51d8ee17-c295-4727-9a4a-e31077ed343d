MERGE INTO dwh_raw.out_indicate_detail USING dwh_raw.out_indicate_detail_work
ON out_indicate_detail.accept_no = out_indicate_detail_work.accept_no AND
 out_indicate_detail.seq = out_indicate_detail_work.seq AND
 out_indicate_detail.prod_no = out_indicate_detail_work.prod_no
WHEN MATCHED THEN UPDATE SET
qty = out_indicate_detail_work.qty,
chit_print_date = CAST(NULLIF(out_indicate_detail_work.chit_print_date, '') AS DATE),
yamato_bar_code = out_indicate_detail_work.yamato_bar_code,
gyosha_flg = out_indicate_detail_work.gyosha_flg,
invoice_branch_number = out_indicate_detail_work.invoice_branch_number,
bumon_kbn = out_indicate_detail_work.bumon_kbn,
prod_price = out_indicate_detail_work.prod_price,
order_type = out_indicate_detail_work.order_type,
dwh_updated_user = out_indicate_detail_work.dwh_updated_user,
dwh_updated_datetime = out_indicate_detail_work.dwh_updated_datetime
WHEN NOT MATCHED THEN INSERT VALUES (
out_indicate_detail_work.accept_no,
out_indicate_detail_work.seq,
out_indicate_detail_work.prod_no,
out_indicate_detail_work.qty,
CAST(NULLIF(out_indicate_detail_work.chit_print_date, '') AS DATE),
out_indicate_detail_work.yamato_bar_code,
out_indicate_detail_work.gyosha_flg,
out_indicate_detail_work.invoice_branch_number,
out_indicate_detail_work.bumon_kbn,
out_indicate_detail_work.prod_price,
out_indicate_detail_work.order_type,
out_indicate_detail_work.dwh_created_user,
out_indicate_detail_work.dwh_created_datetime,
out_indicate_detail_work.dwh_updated_user,
out_indicate_detail_work.dwh_updated_datetime
);