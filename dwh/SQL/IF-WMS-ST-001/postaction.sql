MERGE INTO dwh_raw.stock_all USING dwh_raw.stock_all_work
ON stock_all.center_code = stock_all_work.center_code AND
 stock_all.stock_kind = stock_all_work.stock_kind AND
 stock_all.sh_control_number = stock_all_work.sh_control_number
WHEN MATCHED THEN UPDATE SET
stock_quantity = stock_all_work.stock_quantity,
allocated_quantity = stock_all_work.allocated_quantity,
dwh_updated_user = stock_all_work.dwh_updated_user,
dwh_updated_datetime = stock_all_work.dwh_updated_datetime
WHEN NOT MATCHED THEN INSERT VALUES (
stock_all_work.center_code,
stock_all_work.stock_kind,
stock_all_work.sh_control_number,
stock_all_work.stock_quantity,
stock_all_work.allocated_quantity,
stock_all_work.dwh_created_user,
stock_all_work.dwh_created_datetime,
stock_all_work.dwh_updated_user,
stock_all_work.dwh_updated_datetime
);