CREATE TABLE campaign_instructions (
    campaign_instructions_code VARCHAR(64) NOT NULL,
    campaign_instructions_name <PERSON><PERSON>HA<PERSON>(200) NOT NULL,
    campaign_type VARCHAR(8),
    delete_flg DECIMAL(1) NOT NULL,
    campaign_priority DECIMAL(5),
    campaign_applied_scope VARCHAR(8) NOT NULL,
    campaign_use_limit VARCHAR(8) NOT NULL,
    oneshot_order_limit DECIMAL(8),
    campaign_quantity_limit DECIMAL(8),
    campaign_start_date DATE NOT NULL,
    campaign_end_date DATE NOT NULL,
    present_use_flg DECIMAL(1) NOT NULL,
    campaign_customer_flg DECIMAL(1) NOT NULL,
    campaign_combi_limit_flg DECIMAL(1) NOT NULL,
    permanent_campaign_flg DECIMAL(1) NOT NULL,
    baitai_code VARCHAR(40),
    campaign_description VARCHAR(400),
    change_user_code DECIMAL(38),
    orm_rowid DECIMAL(38) NOT NULL,
    created_user VARCHAR(400) NOT NULL,
    created_datetime TIMESTAMP NOT NULL,
    updated_user VARCHAR(400) NOT NULL,
    updated_datetime TIMESTAMP NOT NULL,
    dwh_created_user VARCHAR(400),
    dwh_created_datetime TIMESTAMP,
    dwh_updated_user VARCHAR(400),
    dwh_updated_datetime TIMESTAMP
);
CREATE TABLE campaign_order (
    campaign_instructions_code VARCHAR(64) NOT NULL,
    campaign_group_no DECIMAL(8) NOT NULL,
    joken_type VARCHAR(4) NOT NULL,
    campaign_joken_no DECIMAL(8) NOT NULL,
    joken_kind1 VARCHAR(4),
    joken_kind2 VARCHAR(12),
    joken VARCHAR(64),
    joken_min DECIMAL(8),
    joken_max DECIMAL(8),
    regular_kaiji DECIMAL(5),
    joken_month_num DECIMAL(3),
    commodity_name VARCHAR(400),
    orm_rowid DECIMAL(38) NOT NULL,
    created_user VARCHAR(400) NOT NULL,
    created_datetime TIMESTAMP NOT NULL,
    updated_user VARCHAR(400) NOT NULL,
    updated_datetime TIMESTAMP NOT NULL,
    dwh_created_user VARCHAR(400),
    dwh_created_datetime TIMESTAMP,
    dwh_updated_user VARCHAR(400),
    dwh_updated_datetime TIMESTAMP
);
CREATE TABLE campaign_order_group (
    campaign_instructions_code VARCHAR(64) NOT NULL,
    campaign_group_no DECIMAL(8) NOT NULL,
    campaign_joken_disp VARCHAR(200),
    exclude_joken_disp VARCHAR(200),
    orm_rowid DECIMAL(38) NOT NULL,
    created_user VARCHAR(400) NOT NULL,
    created_datetime TIMESTAMP NOT NULL,
    updated_user VARCHAR(400) NOT NULL,
    updated_datetime TIMESTAMP NOT NULL,
    dwh_created_user VARCHAR(400),
    dwh_created_datetime TIMESTAMP,
    dwh_updated_user VARCHAR(400),
    dwh_updated_datetime TIMESTAMP
);
CREATE TABLE campaign_instructions_commodity (
    campaign_instructions_code VARCHAR(64) NOT NULL,
    shop_code VARCHAR(64) NOT NULL,
    commodity_code VARCHAR(64) NOT NULL,
    joken_type VARCHAR(4) NOT NULL,
    orm_rowid DECIMAL(38) NOT NULL,
    created_user VARCHAR(400) NOT NULL,
    created_datetime TIMESTAMP NOT NULL,
    updated_user VARCHAR(400) NOT NULL,
    updated_datetime TIMESTAMP NOT NULL,
    dwh_created_user VARCHAR(400),
    dwh_created_datetime TIMESTAMP,
    dwh_updated_user VARCHAR(400),
    dwh_updated_datetime TIMESTAMP
);
CREATE TABLE campaign_promotion (
    campaign_instructions_code VARCHAR(64) NOT NULL,
    promotion_no DECIMAL(9) NOT NULL,
    promotion_type VARCHAR(8) NOT NULL,
    shop_code VARCHAR(64),
    commodity_code VARCHAR(64),
    commodity_name VARCHAR(400),
    present_qt DECIMAL(3),
    discount_rate DECIMAL(3),
    discount_amount DECIMAL(8),
    discount_retail_price DECIMAL(8),
    shipping_charge DECIMAL(8),
    orm_rowid DECIMAL(38) NOT NULL,
    created_user VARCHAR(400) NOT NULL,
    created_datetime TIMESTAMP NOT NULL,
    updated_user VARCHAR(400) NOT NULL,
    updated_datetime TIMESTAMP NOT NULL,
    dwh_created_user VARCHAR(400),
    dwh_created_datetime TIMESTAMP,
    dwh_updated_user VARCHAR(400),
    dwh_updated_datetime TIMESTAMP
);
CREATE TABLE campaign_combi_limit (
    campaign_instructions_code VARCHAR(64) NOT NULL,
    campaign_combi_limit_code VARCHAR(64) NOT NULL,
    orm_rowid DECIMAL(38) NOT NULL,
    created_user VARCHAR(400) NOT NULL,
    created_datetime TIMESTAMP NOT NULL,
    updated_user VARCHAR(400) NOT NULL,
    updated_datetime TIMESTAMP NOT NULL,
    dwh_created_user VARCHAR(400),
    dwh_created_datetime TIMESTAMP,
    dwh_updated_user VARCHAR(400),
    dwh_updated_datetime TIMESTAMP
);
CREATE TABLE coupon (
    coupon_management_code VARCHAR(64) NOT NULL,
    coupon_code VARCHAR(64),
    coupon_name VARCHAR(200) NOT NULL,
    coupon_invalid_flag DECIMAL(1) NOT NULL,
    coupon_type DECIMAL(1) NOT NULL,
    coupon_issue_type DECIMAL(1),
    coupon_use_limit DECIMAL(1) NOT NULL,
    coupon_use_purchase_price DECIMAL(8) NOT NULL,
    coupon_discount_type DECIMAL(1) NOT NULL,
    coupon_discount_rate DECIMAL(3),
    coupon_discount_price DECIMAL(8),
    coupon_start_datetime TIMESTAMP NOT NULL,
    coupon_end_datetime TIMESTAMP NOT NULL,
    coupon_limit_display_period DECIMAL(3),
    coupon_limit_display VARCHAR(200),
    coupon_description VARCHAR(800),
    coupon_message VARCHAR(800),
    coupon_kbn VARCHAR(8),
    coupon_post_in_charge VARCHAR(64) NOT NULL,
    coupon_commodity_flag DECIMAL(1) NOT NULL,
    marketing_channel_list VARCHAR(400),
    goods_group VARCHAR(64),
    commodity_category_code VARCHAR(64),
    commodity_series VARCHAR(20),
    orm_rowid DECIMAL(38) NOT NULL,
    created_user VARCHAR(400) NOT NULL,
    created_datetime TIMESTAMP NOT NULL,
    updated_user VARCHAR(400) NOT NULL,
    updated_datetime TIMESTAMP NOT NULL,
    dwh_created_user VARCHAR(400),
    dwh_created_datetime TIMESTAMP,
    dwh_updated_user VARCHAR(400),
    dwh_updated_datetime TIMESTAMP
);
CREATE TABLE coupon_commodity (
    coupon_management_code VARCHAR(64) NOT NULL,
    shop_code VARCHAR(64) NOT NULL,
    commodity_code VARCHAR(64) NOT NULL,
    orm_rowid DECIMAL(38) NOT NULL,
    created_user VARCHAR(400) NOT NULL,
    created_datetime TIMESTAMP NOT NULL,
    updated_user VARCHAR(400) NOT NULL,
    updated_datetime TIMESTAMP NOT NULL,
    dwh_created_user VARCHAR(400),
    dwh_created_datetime TIMESTAMP,
    dwh_updated_user VARCHAR(400),
    dwh_updated_datetime TIMESTAMP
);
CREATE TABLE order_header (
    order_no VARCHAR(64) NOT NULL,
    shop_code VARCHAR(64) NOT NULL,
    order_datetime TIMESTAMP NOT NULL,
    customer_code VARCHAR(64) NOT NULL,
    neo_customer_no VARCHAR(48),
    guest_flg DECIMAL(1) NOT NULL,
    last_name VARCHAR(80) NOT NULL,
    first_name VARCHAR(80),
    last_name_kana VARCHAR(160) NOT NULL,
    first_name_kana VARCHAR(160),
    email VARCHAR(1024),
    birth_date DATE NOT NULL,
    sex DECIMAL(1) NOT NULL,
    postal_code VARCHAR(28) NOT NULL,
    prefecture_code VARCHAR(8) NOT NULL,
    address1 VARCHAR(16) NOT NULL,
    address2 VARCHAR(200) NOT NULL,
    address3 VARCHAR(1020) NOT NULL,
    address4 VARCHAR(400),
    corporation_post_name VARCHAR(160),
    phone_number VARCHAR(64) NOT NULL,
    advance_later_flg DECIMAL(1) NOT NULL,
    payment_method_no DECIMAL(8) NOT NULL,
    payment_method_type VARCHAR(8) NOT NULL,
    payment_method_name VARCHAR(100),
    ext_payment_method_type VARCHAR(8) NOT NULL,
    payment_commission DECIMAL(8) NOT NULL,
    payment_commission_tax_gr_code VARCHAR(32) NOT NULL,
    payment_commission_tax_no DECIMAL(3) NOT NULL,
    payment_commission_tax_rate DECIMAL(3) NOT NULL,
    payment_commission_tax DECIMAL(10) NOT NULL,
    payment_commission_tax_type DECIMAL(1) NOT NULL,
    coupon_management_code VARCHAR(64),
    coupon_code VARCHAR(64),
    coupon_name VARCHAR(200),
    coupon_type DECIMAL(1),
    coupon_use_purchase_price DECIMAL(8),
    coupon_discount_type DECIMAL(1),
    coupon_discount_price DECIMAL(8),
    coupon_discount_rate DECIMAL(3),
    coupon_used_amount DECIMAL(8) NOT NULL,
    coupon_start_datetime TIMESTAMP,
    coupon_end_datetime TIMESTAMP,
    coupon_kbn VARCHAR(8),
    goods_group VARCHAR(64),
    commodity_category_code VARCHAR(64),
    commodity_series VARCHAR(20),
    coupon_commodity_code_display VARCHAR(80),
    baitai_name VARCHAR(200),
    used_point DECIMAL(8),
    total_amount DECIMAL(8) NOT NULL,
    ec_promotion_id VARCHAR(1024),
    ec_promotion_name VARCHAR(1024),
    ec_promotion_discount_price DECIMAL(8),
    ec_campaign_id VARCHAR(1024),
    ec_campaign_name VARCHAR(1024),
    payment_date DATE,
    payment_limit_date DATE,
    payment_status DECIMAL(1) NOT NULL,
    ext_payment_status VARCHAR(8) NOT NULL,
    customer_group_code VARCHAR(64),
    data_transport_status DECIMAL(1) NOT NULL,
    order_status DECIMAL(1) NOT NULL,
    ext_order_status VARCHAR(8) NOT NULL,
    tax_reference_date DATE NOT NULL,
    cancel_date DATE,
    client_group VARCHAR(8),
    caution VARCHAR(800),
    message VARCHAR(800),
    payment_order_id VARCHAR(400),
    cvs_code VARCHAR(8),
    payment_receipt_no VARCHAR(256),
    payment_receipt_url VARCHAR(2000),
    receipt_no VARCHAR(80),
    customer_no VARCHAR(200),
    confirm_no VARCHAR(200),
    career_key VARCHAR(4),
    order_create_error_code VARCHAR(32) NOT NULL,
    order_display_status DECIMAL(1),
    order_kind_kbn VARCHAR(4) NOT NULL,
    marketing_channel VARCHAR(8) NOT NULL,
    original_order_no VARCHAR(64),
    external_order_no VARCHAR(200),
    order_recieve_datetime TIMESTAMP NOT NULL,
    order_update_datetime TIMESTAMP NOT NULL,
    order_update_reason_kbn VARCHAR(8),
    cancel_reason_kbn VARCHAR(8),
    uncollectible_date DATE,
    order_total_price DECIMAL(10) NOT NULL,
    account_receivable_balance DECIMAL(10) NOT NULL,
    appropriate_amount DECIMAL(10) NOT NULL,
    bill_address_kbn VARCHAR(4) NOT NULL,
    receipt_flg DECIMAL(1) NOT NULL,
    receipt_to VARCHAR(200),
    receipt_detail VARCHAR(200),
    bill_price DECIMAL(10) NOT NULL,
    bill_no VARCHAR(68),
    bill_print_count DECIMAL(3) NOT NULL,
    authority_result_kbn VARCHAR(4),
    authority_no VARCHAR(128),
    card_password VARCHAR(128),
    authority_approval_no VARCHAR(40),
    authority_date DATE,
    authority_price DECIMAL(10),
    authority_cancel_approval_no VARCHAR(40),
    authority_cancel_date DATE,
    credit_payment_no VARCHAR(40),
    credit_payment_date DATE,
    credit_payment_price DECIMAL(10),
    credit_cancel_payment_no VARCHAR(40),
    credit_cancel_payment_date DATE,
    credit_result_kbn VARCHAR(4),
    card_brand VARCHAR(8),
    credit_card_kanri_no VARCHAR(48),
    credit_card_kanri_detail_no VARCHAR(16),
    credit_card_no VARCHAR(200),
    credit_card_meigi VARCHAR(600),
    credit_card_valid_year VARCHAR(16),
    credit_card_valid_month VARCHAR(8),
    credit_card_pay_count VARCHAR(8),
    payment_bar_code VARCHAR(176),
    amzn_charge_permission_id VARCHAR(65535),
    amzn_charge_id VARCHAR(65535),
    amzn_charge_status VARCHAR(8),
    amzn_authorization_datetime TIMESTAMP,
    amzn_capture_initiated_datetime TIMESTAMP,
    amzn_captured_datetime TIMESTAMP,
    amzn_canceled_datetime TIMESTAMP,
    order_user_code DECIMAL(38),
    order_user VARCHAR(80),
    change_user_code DECIMAL(38),
    change_user VARCHAR(80),
    demand_kbn VARCHAR(4) NOT NULL,
    demand1_ref_date DATE,
    demand1_date DATE,
    demand1_limit_date DATE,
    demand1_amount DECIMAL(8),
    demand1_bar_code VARCHAR(176),
    demand2_ref_date DATE,
    demand2_date DATE,
    demand2_limit_date DATE,
    demand2_amount DECIMAL(8),
    demand2_bar_code VARCHAR(176),
    demand3_ref_date DATE,
    demand3_date DATE,
    demand3_limit_date DATE,
    demand3_amount DECIMAL(8),
    demand3_bar_code VARCHAR(176),
    kashidaore_date DATE,
    demand_exclude_reason_kbn VARCHAR(8),
    demand_exclude_start_date DATE,
    demand_exclude_end_date DATE,
    bill_sei_kj VARCHAR(200) NOT NULL,
    bill_mei_kj VARCHAR(200),
    bill_sei_kn VARCHAR(400) NOT NULL,
    bill_mei_kn VARCHAR(400),
    bill_tel_no VARCHAR(64) NOT NULL,
    bill_zipcd VARCHAR(28) NOT NULL,
    bill_addr1 VARCHAR(16) NOT NULL,
    bill_addr2 VARCHAR(200) NOT NULL,
    bill_addr3 VARCHAR(1020) NOT NULL,
    bill_addr4 VARCHAR(400),
    bill_corporation_post_name VARCHAR(160),
    nohinsyo_uketsuke_tanto VARCHAR(1200),
    grant_plan_point_prod DECIMAL(10),
    grant_plan_point_other DECIMAL(10),
    grant_plan_point_total DECIMAL(10),
    grant_point_prod DECIMAL(10),
    grant_point_other DECIMAL(10),
    grant_point_total DECIMAL(10),
    reduction_plan_point_total DECIMAL(10),
    reduction_point_total DECIMAL(10),
    subtotal_before_campaign DECIMAL(10),
    subtotal_after_campaign DECIMAL(10),
    total_before_campaign DECIMAL(10),
    orm_rowid DECIMAL(38) NOT NULL,
    created_user VARCHAR(400) NOT NULL,
    created_datetime TIMESTAMP NOT NULL,
    updated_user VARCHAR(400) NOT NULL,
    updated_datetime TIMESTAMP NOT NULL,
    dwh_created_user VARCHAR(400),
    dwh_created_datetime TIMESTAMP,
    dwh_updated_user VARCHAR(400),
    dwh_updated_datetime TIMESTAMP
);
CREATE TABLE order_detail (
    order_no VARCHAR(64) NOT NULL,
    order_detail_no DECIMAL(16) NOT NULL,
    shop_code VARCHAR(64) NOT NULL,
    sku_code VARCHAR(96) NOT NULL,
    commodity_code VARCHAR(64) NOT NULL,
    commodity_name VARCHAR(400) NOT NULL,
    commodity_kind VARCHAR(8) NOT NULL,
    baitai_code VARCHAR(40) NOT NULL,
    baitai_name VARCHAR(200) NOT NULL,
    hinban_code VARCHAR(96) NOT NULL,
    standard_detail1_name VARCHAR(80),
    standard_detail2_name VARCHAR(80),
    purchasing_amount DECIMAL(8) NOT NULL,
    unit_price DECIMAL(8) NOT NULL,
    retail_price DECIMAL(8) NOT NULL,
    retail_tax DECIMAL(10) NOT NULL,
    commodity_tax_group_code VARCHAR(32) NOT NULL,
    commodity_tax_no DECIMAL(3) NOT NULL,
    commodity_tax_rate DECIMAL(3) NOT NULL,
    commodity_tax DECIMAL(10) NOT NULL,
    commodity_tax_type DECIMAL(1) NOT NULL,
    campaign_code VARCHAR(64),
    campaign_name VARCHAR(200),
    campaign_instructions_code VARCHAR(64),
    campaign_instructions_name VARCHAR(200),
    campaign_discount_rate DECIMAL(3),
    campaign_discount_price DECIMAL(8),
    present_campaign_instructions_code VARCHAR(64),
    present_order_detail_no DECIMAL(16),
    age_limit_code DECIMAL(8),
    age_limit_name VARCHAR(200),
    age DECIMAL(3),
    age_limit_confirm_type DECIMAL(1),
    applied_point_rate DECIMAL(8),
    benefits_code VARCHAR(40),
    benefits_name VARCHAR(200),
    benefits_commodity_code VARCHAR(64),
    stock_management_type DECIMAL(1) NOT NULL,
    stock_allocated_kbn VARCHAR(8) NOT NULL,
    allocated_warehouse_code VARCHAR(24),
    allocated_quantity DECIMAL(8) NOT NULL,
    arrival_reserved_quantity DECIMAL(8) NOT NULL,
    cancel_quantity DECIMAL(5) NOT NULL,
    henpin_qt DECIMAL(5) NOT NULL,
    coupon_management_code VARCHAR(64),
    coupon_code VARCHAR(64),
    coupon_name VARCHAR(200),
    coupon_discount_rate DECIMAL(3),
    coupon_discount_price DECIMAL(8),
    ec_promotion_id VARCHAR(1024),
    ec_promotion_name VARCHAR(1024),
    ec_promotion_discount_price DECIMAL(8),
    ec_campaign_id VARCHAR(1024),
    ec_campaign_name VARCHAR(1024),
    adjustment_price DECIMAL(8),
    keihi_hurikae_target_flg DECIMAL(1) NOT NULL,
    member_price_applied_flg DECIMAL(1) NOT NULL,
    shipping_charge_target_flg DECIMAL(1) NOT NULL,
    regular_contract_no VARCHAR(56),
    regular_contract_detail_no DECIMAL(3),
    regular_kaiji DECIMAL(5) NOT NULL,
    regular_check_memo VARCHAR(4000),
    total_commodity_buy_count DECIMAL(5) NOT NULL,
    total_commodity_regular_kaiji DECIMAL(5) NOT NULL,
    regular_total_commodity_regular_kaiji DECIMAL(5) NOT NULL,
    commodity_category_code VARCHAR(64),
    total_category_buy_count DECIMAL(5) NOT NULL,
    total_categoryregular_kaiji DECIMAL(5) NOT NULL,
    regular_total_categoryregular_kaiji DECIMAL(5) NOT NULL,
    commodity_subcategory_code VARCHAR(64),
    total_subcategory_buy_count DECIMAL(5) NOT NULL,
    total_subcategoryregular_kaiji DECIMAL(5) NOT NULL,
    regular_total_subcategoryregular_kaiji DECIMAL(5) NOT NULL,
    commodity_subsubcategory_code VARCHAR(64),
    total_subsubcategory_buy_count DECIMAL(5) NOT NULL,
    total_subsubcategoryregular_kaiji DECIMAL(5) NOT NULL,
    regular_total_subsubcategoryregular_kaiji DECIMAL(5) NOT NULL,
    grant_plan_point_prod_detail DECIMAL(10),
    reduction_plan_point_prod_detail DECIMAL(10),
    orm_rowid DECIMAL(38) NOT NULL,
    created_user VARCHAR(400) NOT NULL,
    created_datetime TIMESTAMP NOT NULL,
    updated_user VARCHAR(400) NOT NULL,
    updated_datetime TIMESTAMP NOT NULL,
    dwh_created_user VARCHAR(400),
    dwh_created_datetime TIMESTAMP,
    dwh_updated_user VARCHAR(400),
    dwh_updated_datetime TIMESTAMP
);
CREATE TABLE commodity_header (
    shop_code VARCHAR(64) NOT NULL,
    commodity_code VARCHAR(64) NOT NULL,
    commodity_name VARCHAR(400) NOT NULL,
    commodity_type DECIMAL(1) NOT NULL,
    represent_sku_code VARCHAR(96) NOT NULL,
    represent_sku_unit_price DECIMAL(8) NOT NULL,
    stock_status_no DECIMAL(8),
    stock_management_type DECIMAL(1) NOT NULL,
    age_limit_code DECIMAL(8) NOT NULL,
    commodity_tax_type DECIMAL(1) NOT NULL,
    tax_group_code VARCHAR(32) NOT NULL,
    short_description VARCHAR(400),
    commodity_search_words VARCHAR(2000),
    prior_printing_description VARCHAR(4000),
    posterior_printing_description VARCHAR(4000),
    delivery_description VARCHAR(4000),
    sale_start_datetime TIMESTAMP NOT NULL,
    sale_end_datetime TIMESTAMP NOT NULL,
    discount_price_start_datetime TIMESTAMP,
    discount_price_end_datetime TIMESTAMP,
    reservation_start_datetime TIMESTAMP,
    reservation_end_datetime TIMESTAMP,
    prior_printing_start_date TIMESTAMP,
    prior_printing_end_date TIMESTAMP,
    posterior_printing_start_date TIMESTAMP,
    posterior_printing_end_date TIMESTAMP,
    delivery_type_no DECIMAL(8) NOT NULL,
    sales_method_type DECIMAL(1) NOT NULL,
    manufacturer_model_no VARCHAR(200),
    link_url VARCHAR(1024),
    recommend_commodity_rank DECIMAL(8),
    commodity_popular_rank DECIMAL(8),
    commodity_standard1_name VARCHAR(80),
    commodity_standard2_name VARCHAR(80),
    commodity_point_rate DECIMAL(3),
    commodity_point_start_datetime TIMESTAMP,
    commodity_point_end_datetime TIMESTAMP,
    sale_flg DECIMAL(1) NOT NULL,
    noshi_effective_flg DECIMAL(1),
    arrival_goods_flg DECIMAL(1),
    oneshot_order_limit DECIMAL(8),
    standard_image_type DECIMAL(1),
    purchasing_confirm_flg_pc DECIMAL(1),
    purchasing_confirm_flg_sp DECIMAL(1),
    commodity_kind VARCHAR(8) NOT NULL,
    keihi_hurikae_target_flg DECIMAL(1) NOT NULL,
    charge_user_code DECIMAL(38),
    commodity_remark VARCHAR(200),
    channel_cc_sale_flg DECIMAL(1) NOT NULL,
    channel_ec_sale_flg DECIMAL(1) NOT NULL,
    shipping_charge_target_flg DECIMAL(1) NOT NULL,
    first_purchase_limit_flg DECIMAL(1) NOT NULL,
    purchase_hold_flg DECIMAL(1) NOT NULL,
    commodity_exclude_flg DECIMAL(1) NOT NULL,
    commodity_subsubcategory_code VARCHAR(64),
    pack_calc_pattern DECIMAL(2) NOT NULL,
    pad_type DECIMAL(2) NOT NULL,
    fall_down_flg DECIMAL(1) NOT NULL,
    height DECIMAL(5),
    width DECIMAL(5),
    deepness DECIMAL(5),
    weight DECIMAL(7),
    tracking_out_flg DECIMAL(1) NOT NULL,
    mdm_management_code DECIMAL(20),
    commodity_segment VARCHAR(20),
    business_segment VARCHAR(20),
    commodity_group VARCHAR(20),
    commodity_series VARCHAR(20),
    core_department VARCHAR(8),
    accounting_pattern_type VARCHAR(8),
    return_enabled_flg VARCHAR(4),
    exchange_enabled_flg VARCHAR(4),
    exterior_box_weight VARCHAR(40),
    nekoposu_volume_rate DECIMAL(3),
    warehouse_assembly_flg VARCHAR(4),
    mail_delivery_flg VARCHAR(4),
    before_renewal_commodity_code VARCHAR(800),
    preorder_enable_days DECIMAL(2),
    main_product_no VARCHAR(96),
    product_no VARCHAR(96),
    orm_rowid DECIMAL(38) NOT NULL,
    created_user VARCHAR(400) NOT NULL,
    created_datetime TIMESTAMP NOT NULL,
    updated_user VARCHAR(400) NOT NULL,
    updated_datetime TIMESTAMP NOT NULL,
    dwh_created_user VARCHAR(400),
    dwh_created_datetime TIMESTAMP,
    dwh_updated_user VARCHAR(400),
    dwh_updated_datetime TIMESTAMP
);
CREATE TABLE commodity_detail (
    shop_code VARCHAR(64) NOT NULL,
    sku_code VARCHAR(96) NOT NULL,
    commodity_code VARCHAR(64) NOT NULL,
    unit_price DECIMAL(8) NOT NULL,
    discount_price DECIMAL(8),
    reservation_price DECIMAL(8),
    jan_code VARCHAR(64),
    standard_detail1_name VARCHAR(80),
    standard_detail2_name VARCHAR(80),
    hinban_code VARCHAR(96) NOT NULL,
    hinban_kind VARCHAR(8) NOT NULL,
    member_price_applied_flg DECIMAL(1) NOT NULL,
    member_price_discount_rate DECIMAL(3) NOT NULL,
    member_price DECIMAL(8) NOT NULL,
    air_transport_flg DECIMAL(1) NOT NULL,
    commodity_prod_pack_type VARCHAR(8) NOT NULL,
    delivery_note_no_disp_flg DECIMAL(1) NOT NULL,
    reduction_point DECIMAL(10),
    orm_rowid DECIMAL(38) NOT NULL,
    created_user VARCHAR(400) NOT NULL,
    created_datetime TIMESTAMP NOT NULL,
    updated_user VARCHAR(400) NOT NULL,
    updated_datetime TIMESTAMP NOT NULL,
    dwh_created_user VARCHAR(400),
    dwh_created_datetime TIMESTAMP,
    dwh_updated_user VARCHAR(400),
    dwh_updated_datetime TIMESTAMP
);
CREATE TABLE set_commodity_composition (
    shop_code VARCHAR(64) NOT NULL,
    commodity_code VARCHAR(64) NOT NULL,
    child_commodity_code VARCHAR(64) NOT NULL,
    composition_quantity DECIMAL(2) NOT NULL,
    composition_order DECIMAL(2) NOT NULL,
    orm_rowid DECIMAL(38) NOT NULL,
    created_user VARCHAR(400) NOT NULL,
    created_datetime TIMESTAMP NOT NULL,
    updated_user VARCHAR(400) NOT NULL,
    updated_datetime TIMESTAMP NOT NULL,
    dwh_created_user VARCHAR(400),
    dwh_created_datetime TIMESTAMP,
    dwh_updated_user VARCHAR(400),
    dwh_updated_datetime TIMESTAMP
);
CREATE TABLE regular_sale_base (
    shop_code VARCHAR(64) NOT NULL,
    regular_sale_code VARCHAR(64) NOT NULL,
    sku_code VARCHAR(96) NOT NULL,
    commodity_code VARCHAR(64) NOT NULL,
    regular_cycle_kind_list VARCHAR(400),
    regular_cycle_days_list VARCHAR(100),
    regular_cycle_months_list VARCHAR(400),
    regular_sale_stop_from DECIMAL(5),
    regular_sale_stop_to DECIMAL(5),
    orm_rowid DECIMAL(38) NOT NULL,
    created_user VARCHAR(400) NOT NULL,
    created_datetime TIMESTAMP NOT NULL,
    updated_user VARCHAR(400) NOT NULL,
    updated_datetime TIMESTAMP NOT NULL,
    dwh_created_user VARCHAR(400),
    dwh_created_datetime TIMESTAMP,
    dwh_updated_user VARCHAR(400),
    dwh_updated_datetime TIMESTAMP
);
CREATE TABLE regular_sale_composition (
    shop_code VARCHAR(64) NOT NULL,
    regular_sale_code VARCHAR(64) NOT NULL,
    regular_sale_composition_no VARCHAR(32) NOT NULL,
    regular_sale_composition_name VARCHAR(200),
    regular_order_count_min_limit DECIMAL(5) NOT NULL,
    regular_order_count_max_limit DECIMAL(5),
    regular_order_count_interval DECIMAL(5) NOT NULL,
    retail_price DECIMAL(8) NOT NULL,
    regular_sale_commodity_point DECIMAL(3),
    display_order DECIMAL(8),
    orm_rowid DECIMAL(38) NOT NULL,
    created_user VARCHAR(400) NOT NULL,
    created_datetime TIMESTAMP NOT NULL,
    updated_user VARCHAR(400) NOT NULL,
    updated_datetime TIMESTAMP NOT NULL,
    dwh_created_user VARCHAR(400),
    dwh_created_datetime TIMESTAMP,
    dwh_updated_user VARCHAR(400),
    dwh_updated_datetime TIMESTAMP
);
CREATE TABLE regular_sale_payment (
    shop_code VARCHAR(64) NOT NULL,
    regular_sale_code VARCHAR(64) NOT NULL,
    payment_method_no DECIMAL(8) NOT NULL,
    orm_rowid DECIMAL(38) NOT NULL,
    created_user VARCHAR(400) NOT NULL,
    created_datetime TIMESTAMP NOT NULL,
    updated_user VARCHAR(400) NOT NULL,
    updated_datetime TIMESTAMP NOT NULL,
    dwh_created_user VARCHAR(400),
    dwh_created_datetime TIMESTAMP,
    dwh_updated_user VARCHAR(400),
    dwh_updated_datetime TIMESTAMP
);
CREATE TABLE regular_sale_commodity (
    shop_code VARCHAR(64) NOT NULL,
    regular_sale_code VARCHAR(64) NOT NULL,
    regular_sale_composition_no VARCHAR(32) NOT NULL,
    sku_code VARCHAR(96) NOT NULL,
    commodity_code VARCHAR(64) NOT NULL,
    display_order DECIMAL(8) NOT NULL,
    regular_sale_commodity_type VARCHAR(4),
    regular_sale_commodity_point DECIMAL(3),
    difference_price DECIMAL(8),
    orm_rowid DECIMAL(38) NOT NULL,
    created_user VARCHAR(400) NOT NULL,
    created_datetime TIMESTAMP NOT NULL,
    updated_user VARCHAR(400) NOT NULL,
    updated_datetime TIMESTAMP NOT NULL,
    dwh_created_user VARCHAR(400),
    dwh_created_datetime TIMESTAMP,
    dwh_updated_user VARCHAR(400),
    dwh_updated_datetime TIMESTAMP
);
CREATE TABLE sales_info_alignment_payment (
    shop_cd VARCHAR(48) NOT NULL,
    register_num DECIMAL(11) NOT NULL,
    business_date DATE NOT NULL,
    receipt_num DECIMAL(11) NOT NULL,
    line_num DECIMAL(11) NOT NULL,
    pay_kind DECIMAL(4) NOT NULL,
    pay_cd VARCHAR(40),
    credit_linkage_kind DECIMAL(4),
    claim_kind DECIMAL(4),
    pay_amount DECIMAL(11) NOT NULL,
    change_amount DECIMAL(11) NOT NULL,
    surplus_amount DECIMAL(11) NOT NULL,
    coupon_num VARCHAR(40),
    ticket_quantity DECIMAL(11),
    ticket_bar_cd VARCHAR(128),
    is_worn_cash DECIMAL(4),
    is_emoney_cash_back DECIMAL(4),
    numeric_reserve1 DECIMAL(11),
    numeric_reserve2 DECIMAL(11),
    numeric_reserve3 DECIMAL(11),
    numeric_reserve4 DECIMAL(11),
    numeric_reserve5 DECIMAL(11),
    numeric_reserve6 DECIMAL(11),
    numeric_reserve7 DECIMAL(11),
    numeric_reserve8 DECIMAL(11),
    numeric_reserve9 DECIMAL(11),
    numeric_reserve10 DECIMAL(11),
    string_reserve1 VARCHAR(1020),
    string_reserve2 VARCHAR(1020),
    string_reserve3 VARCHAR(1020),
    string_reserve4 VARCHAR(1020),
    string_reserve5 VARCHAR(1020),
    string_reserve6 VARCHAR(1020),
    string_reserve7 VARCHAR(1020),
    string_reserve8 VARCHAR(1020),
    string_reserve9 VARCHAR(1020),
    string_reserve10 VARCHAR(1020),
    dwh_created_user VARCHAR(400),
    dwh_created_datetime TIMESTAMP,
    dwh_updated_user VARCHAR(400),
    dwh_updated_datetime TIMESTAMP
);
CREATE TABLE sales_info_alignment_header (
    shop_cd VARCHAR(48) NOT NULL,
    register_num DECIMAL(11) NOT NULL,
    business_date DATE NOT NULL,
    receipt_num DECIMAL(11) NOT NULL,
    chit_num VARCHAR(80) NOT NULL,
    system_datetime TIMESTAMP NOT NULL,
    open_count DECIMAL(4) NOT NULL,
    is_void DECIMAL(4) NOT NULL,
    check_kind DECIMAL(4) NOT NULL,
    return_kind DECIMAL(4) NOT NULL,
    sub_check_kind DECIMAL(4) NOT NULL,
    sales_group_cd DECIMAL(4) NOT NULL,
    deposit_kind DECIMAL(4),
    operator_cd VARCHAR(40) NOT NULL,
    operator_name VARCHAR(200) NOT NULL,
    sales_man_cd VARCHAR(40) NOT NULL,
    sales_man_name VARCHAR(200) NOT NULL,
    return_employee_cd VARCHAR(40),
    return_employee_name VARCHAR(200),
    void_employee_cd VARCHAR(40),
    void_employee_name VARCHAR(200),
    staff_sale_employee_cd VARCHAR(40),
    staff_sale_employee_name VARCHAR(200),
    customer_cd VARCHAR(80),
    customer_layer_cd VARCHAR(16),
    customer_layer_cd2 VARCHAR(16),
    purchase_motive_cd VARCHAR(16),
    return_reason_cd VARCHAR(16),
    void_reason_cd VARCHAR(16),
    net_sales_amount_of_outside_tax DECIMAL(11) NOT NULL,
    net_sales_amount_of_inside_tax DECIMAL(11) NOT NULL,
    net_sales_amount_of_tax_free DECIMAL(11) NOT NULL,
    net_sales_outside_tax DECIMAL(11) NOT NULL,
    net_sales_inside_tax DECIMAL(11) NOT NULL,
    net_sales_quantity DECIMAL(11) NOT NULL,
    outside_sales_amount_of_outside_tax DECIMAL(11) NOT NULL,
    outside_sales_amount_of_inside_tax DECIMAL(11) NOT NULL,
    outside_sales_amount_of_tax_free DECIMAL(11) NOT NULL,
    outside_sales_outside_tax DECIMAL(11) NOT NULL,
    outside_sales_inside_tax DECIMAL(11) NOT NULL,
    outside_sales_quantity DECIMAL(11) NOT NULL,
    total_amount DECIMAL(11) NOT NULL,
    discount_amount DECIMAL(11) NOT NULL,
    discount_tax_inclusive DECIMAL(11) NOT NULL,
    is_revenue_stamp DECIMAL(4) NOT NULL,
    order_line_count DECIMAL(11) NOT NULL,
    pay_line_count DECIMAL(4) NOT NULL,
    is_total_display DECIMAL(4) NOT NULL,
    is_reduced_tax_rate_trade DECIMAL(4) NOT NULL,
    is_tax_free DECIMAL(4) NOT NULL,
    customers_num DECIMAL(11) NOT NULL,
    deliver_date TIMESTAMP,
    sale_attribute_cd1 VARCHAR(16),
    sale_attribute_cd2 VARCHAR(16),
    sale_attribute_cd3 VARCHAR(16),
    sale_attribute_cd4 VARCHAR(40),
    campaign_no VARCHAR(32),
    closing_date DATE,
    return_date DATE,
    order_number VARCHAR(40),
    employee_meal DECIMAL(4) NOT NULL,
    employee_code VARCHAR(40),
    table_no VARCHAR(48),
    acceptance_time TIMESTAMP,
    menu_cook_cmp_time TIMESTAMP,
    menu_offer_cmp_time TIMESTAMP,
    service_charge_amount_outside_tax DECIMAL(11) NOT NULL,
    service_charge_amount_inside_tax DECIMAL(11) NOT NULL,
    service_charge_tax_exclusive DECIMAL(11) NOT NULL,
    service_charge_tax_inclusive DECIMAL(11) NOT NULL,
    service_charge_amount1 DECIMAL(11) NOT NULL,
    service_charge_amount2 DECIMAL(11) NOT NULL,
    service_charge_minus_amount DECIMAL(11) NOT NULL,
    service_charge_minus_tax_inclusive DECIMAL(11) NOT NULL,
    service_charge_target DECIMAL(4) NOT NULL,
    service_charge_button DECIMAL(4) NOT NULL,
    service_charge1_button DECIMAL(4) NOT NULL,
    service_charge2_button DECIMAL(4) NOT NULL,
    eat_in_amount DECIMAL(11) NOT NULL,
    takeout_amount DECIMAL(11) NOT NULL,
    vein_employee_cd VARCHAR(40),
    is_vein_authentication DECIMAL(4),
    out_calc_flg DECIMAL(4),
    sale_goods_flg VARCHAR(16),
    point_linkage_kind DECIMAL(4),
    numeric_reserve1 DECIMAL(11),
    numeric_reserve2 DECIMAL(11),
    numeric_reserve3 DECIMAL(11),
    numeric_reserve4 DECIMAL(11),
    numeric_reserve5 DECIMAL(11),
    numeric_reserve6 DECIMAL(11),
    numeric_reserve7 DECIMAL(11),
    numeric_reserve8 DECIMAL(11),
    numeric_reserve9 DECIMAL(11),
    numeric_reserve10 DECIMAL(11),
    string_reserve1 VARCHAR(1020),
    string_reserve2 VARCHAR(1020),
    string_reserve3 VARCHAR(1020),
    string_reserve4 VARCHAR(1020),
    string_reserve5 VARCHAR(1020),
    string_reserve6 VARCHAR(1020),
    string_reserve7 VARCHAR(1020),
    string_reserve8 VARCHAR(1020),
    string_reserve9 VARCHAR(1020),
    string_reserve10 VARCHAR(1020),
    dwh_created_user VARCHAR(400),
    dwh_created_datetime TIMESTAMP,
    dwh_updated_user VARCHAR(400),
    dwh_updated_datetime TIMESTAMP
);
CREATE TABLE sales_info_alignment_detail (
    shop_cd VARCHAR(48) NOT NULL,
    register_num DECIMAL(11) NOT NULL,
    business_date DATE NOT NULL,
    receipt_num DECIMAL(11) NOT NULL,
    line_num DECIMAL(11) NOT NULL,
    line_kind DECIMAL(4) NOT NULL,
    is_in_store_marking DECIMAL(4),
    brand_cd VARCHAR(20),
    dept_cd VARCHAR(16),
    class_cd VARCHAR(20),
    sub_class_cd VARCHAR(20),
    item_cd VARCHAR(120),
    item_name VARCHAR(360),
    dept_group_cd VARCHAR(16),
    parent_item_cd VARCHAR(120),
    jan_cd VARCHAR(80),
    item_num VARCHAR(80),
    color_cd VARCHAR(20),
    size_cd VARCHAR(20),
    year_cd VARCHAR(16),
    season_cd VARCHAR(16),
    attribute_cd1 VARCHAR(16),
    attribute_cd2 VARCHAR(16),
    attribute_cd3 VARCHAR(16),
    attribute_cd4 VARCHAR(16),
    attribute_cd5 VARCHAR(16),
    scan_bar_cd1 VARCHAR(80),
    scan_bar_cd2 VARCHAR(80),
    discount_cd VARCHAR(24),
    bmplan_cd VARCHAR(44),
    coupon_num VARCHAR(40),
    is_include_sales DECIMAL(4),
    item_kind DECIMAL(4),
    tax_kind DECIMAL(4),
    tax_rate DECIMAL(4,1),
    is_reduced_tax_rate DECIMAL(4),
    is_proper_item DECIMAL(4),
    is_change_price DECIMAL(4),
    master_unit_price DECIMAL(11),
    body_price DECIMAL(11),
    fixed_price DECIMAL(11),
    unit_price DECIMAL(11),
    quantity DECIMAL(11),
    amount DECIMAL(11) NOT NULL,
    tax_inclusive DECIMAL(11),
    tax_outside DECIMAL(11),
    tax DECIMAL(11),
    disc_rate DECIMAL(4),
    line_minus_amount DECIMAL(11),
    line_minus_tax_inclusive DECIMAL(11),
    bmset_minus_amount DECIMAL(11),
    bmset_minus_tax_inclusive DECIMAL(11),
    point_minus_amount DECIMAL(11),
    point_minus_tax_inclusive DECIMAL(11),
    coupon_minus_amount DECIMAL(11),
    coupon_minus_tax_inclusive DECIMAL(11),
    sub_total_minus_amount DECIMAL(11),
    sub_total_minus_tax_inclusive DECIMAL(11),
    before_disc_tax_inclusive DECIMAL(11),
    sales_man_cd VARCHAR(40),
    stock_kind DECIMAL(4),
    is_inventory_counted DECIMAL(4),
    is_promotion_ticket DECIMAL(4),
    promotion_ticket_bar_cd VARCHAR(128),
    is_promotion_ticket_allow_combination DECIMAL(4),
    oes_item_flag DECIMAL(4) NOT NULL,
    oes_slip_no VARCHAR(24),
    oes_slip_sub_no VARCHAR(8),
    grand_classification DECIMAL(4) NOT NULL,
    menu_classification VARCHAR(4),
    oes_line_num DECIMAL(11),
    oes_quantity DECIMAL(11),
    oes_minus DECIMAL(11),
    pos_minus DECIMAL(11),
    takeout_flag DECIMAL(4),
    service_charge_minus_amount DECIMAL(11),
    service_charge_minus_tax_inclusive DECIMAL(11),
    service_charge_flag DECIMAL(4) NOT NULL,
    service_charge1_flag DECIMAL(4) NOT NULL,
    service_charge2_flag DECIMAL(4) NOT NULL,
    service_charge1_manually_flag DECIMAL(4) NOT NULL,
    service_charge2_manually_flag DECIMAL(4) NOT NULL,
    oes_service_charge1 DECIMAL(4) NOT NULL,
    oes_service_charge2 DECIMAL(4) NOT NULL,
    cooking_directions_time DECIMAL(11),
    cooking_complete_time DECIMAL(11),
    offer_complete_time DECIMAL(11),
    acceptance_time TIMESTAMP,
    menu_cook_cmp_time TIMESTAMP,
    menu_offer_cmp_time TIMESTAMP,
    disc_amount_limit DECIMAL(11) NOT NULL,
    is_follow_disc DECIMAL(4),
    is_allow_credit DECIMAL(4),
    is_employee_acnt_recv DECIMAL(4),
    employee_cd VARCHAR(32),
    sub_menu_kind VARCHAR(4),
    grand_menu_code VARCHAR(80),
    grand_menu_index DECIMAL(11),
    select_kind VARCHAR(4),
    is_quantity_count DECIMAL(4),
    is_order_entry DECIMAL(4),
    modifier_kind DECIMAL(4),
    return_target_line_num DECIMAL(11),
    numeric_reserve1 DECIMAL(11),
    numeric_reserve2 DECIMAL(11),
    numeric_reserve3 DECIMAL(11),
    numeric_reserve4 DECIMAL(11),
    numeric_reserve5 DECIMAL(11),
    numeric_reserve6 DECIMAL(11),
    numeric_reserve7 DECIMAL(11),
    numeric_reserve8 DECIMAL(11),
    numeric_reserve9 DECIMAL(11),
    numeric_reserve10 DECIMAL(11),
    string_reserve1 VARCHAR(1020),
    string_reserve2 VARCHAR(1020),
    string_reserve3 VARCHAR(1020),
    string_reserve4 VARCHAR(1020),
    string_reserve5 VARCHAR(1020),
    string_reserve6 VARCHAR(1020),
    string_reserve7 VARCHAR(1020),
    string_reserve8 VARCHAR(1020),
    string_reserve9 VARCHAR(1020),
    string_reserve10 VARCHAR(1020),
    dwh_created_user VARCHAR(400),
    dwh_created_datetime TIMESTAMP,
    dwh_updated_user VARCHAR(400),
    dwh_updated_datetime TIMESTAMP
);
CREATE TABLE shop_mst_raw (
    shop_cd VARCHAR(48) NOT NULL,
    group_cd VARCHAR(40),
    area_cd VARCHAR(40),
    district_cd VARCHAR(40),
    shop_kind VARCHAR(8) NOT NULL,
    shop_name_full VARCHAR(240) NOT NULL,
    shop_name_half VARCHAR(240),
    shop_name_short VARCHAR(240),
    shop_name_english VARCHAR(240),
    zip_cd VARCHAR(60),
    prefecture_cd VARCHAR(40),
    address1 VARCHAR(240),
    address2 VARCHAR(240),
    address3 VARCHAR(240),
    address4 VARCHAR(240),
    tel_num VARCHAR(60),
    fax_num VARCHAR(60),
    tax_inclusive_round_kind DECIMAL(4),
    tax_exclusive_round_kind DECIMAL(4),
    condition_kind VARCHAR(8) NOT NULL,
    condition_detail_kind VARCHAR(8) NOT NULL,
    channel_kind VARCHAR(40),
    is_stock_control DECIMAL(4) NOT NULL,
    begin_date DATE,
    end_date DATE,
    shop_floor_space DECIMAL(6,2),
    is_abandon_price_change DECIMAL(4) NOT NULL,
    is_abandon_stock_transfer DECIMAL(4) NOT NULL,
    form_management_kind VARCHAR(8),
    is_shop_terminal DECIMAL(4) NOT NULL,
    allow_transfer_group_cd VARCHAR(20),
    main_brand_cd VARCHAR(20) NOT NULL,
    disc_round_position DECIMAL(4) NOT NULL,
    disc_round_kind DECIMAL(4) NOT NULL,
    is_emp_salse_place DECIMAL(4) NOT NULL,
    is_move_operation DECIMAL(4) NOT NULL,
    is_sales_register DECIMAL(4) NOT NULL,
    is_stock_display DECIMAL(4) NOT NULL,
    brock_cd VARCHAR(80),
    pricechange_disc_round_position DECIMAL(4),
    pricechange_disc_round_kind DECIMAL(4),
    numeric_reserve1 DECIMAL(11),
    numeric_reserve2 DECIMAL(11),
    numeric_reserve3 DECIMAL(11),
    numeric_reserve4 DECIMAL(11),
    numeric_reserve5 DECIMAL(11),
    string_reserve1 VARCHAR(80),
    string_reserve2 VARCHAR(80),
    string_reserve3 VARCHAR(80),
    string_reserve4 VARCHAR(80),
    string_reserve5 VARCHAR(80),
    is_deleted DECIMAL(4),
    spare_numeric_reserve1 DECIMAL(11),
    spare_numeric_reserve2 DECIMAL(11),
    spare_numeric_reserve3 DECIMAL(11),
    apare_numeric_reserve4 DECIMAL(11),
    spare_numeric_reserve5 DECIMAL(11),
    spare_string_reserve1 VARCHAR(80),
    spare_string_reserve2 VARCHAR(80),
    spare_string_reserve3 VARCHAR(80),
    spare_string_reserve4 VARCHAR(80),
    spare_string_reserve5 VARCHAR(80),
    spare_string_reserve6 VARCHAR(80),
    spare_string_reserve7 VARCHAR(80),
    dwh_created_user VARCHAR(400),
    dwh_created_datetime TIMESTAMP,
    dwh_updated_user VARCHAR(400),
    dwh_updated_datetime TIMESTAMP
);
CREATE TABLE arrival_schedule_other (
    stock_arrival_date DATE NOT NULL,
    warehouse_cd VARCHAR(12) NOT NULL,
    po_no VARCHAR(48) NOT NULL,
    warehouse_management_no VARCHAR(40) NOT NULL,
    order_count DECIMAL(7) NOT NULL,
    agent_cd VARCHAR(8) NOT NULL,
    order_registrant_name VARCHAR(80),
    comment_code VARCHAR(200),
    dwh_created_user VARCHAR(400),
    dwh_created_datetime TIMESTAMP,
    dwh_updated_user VARCHAR(400),
    dwh_updated_datetime TIMESTAMP
);
CREATE TABLE arrival_achievements (
    warehouse_cd VARCHAR(16) NOT NULL,
    receiving_stock_date DATE NOT NULL,
    receiving_stock_former_cd VARCHAR(16),
    receiving_stock_slip_no VARCHAR(40) NOT NULL,
    receiving_stock_slip_row_no DECIMAL(4) NOT NULL,
    po_no VARCHAR(48) NOT NULL,
    product_cd VARCHAR(100) NOT NULL,
    receiving_stock_cnt DECIMAL(13,3) NOT NULL,
    dwh_created_user VARCHAR(400),
    dwh_created_datetime TIMESTAMP,
    dwh_updated_user VARCHAR(400),
    dwh_updated_datetime TIMESTAMP
);
CREATE TABLE accounting_data_mail_order (
    input_no VARCHAR(32) NOT NULL,
    input_sys_type VARCHAR(12) NOT NULL,
    corp_cd VARCHAR(48) NOT NULL,
    vote_employee_cd VARCHAR(48) NOT NULL,
    vote_dept_cd VARCHAR(48) NOT NULL,
    approval_employee_cd VARCHAR(48) NOT NULL,
    approval_date DATE NOT NULL,
    approval_status_type VARCHAR(4) NOT NULL,
    journal_type VARCHAR(8) NOT NULL,
    slip_date DATE NOT NULL,
    slip_no VARCHAR(32),
    slip_ope_ban_type VARCHAR(4) NOT NULL,
    journal_reference_type VARCHAR(4) NOT NULL,
    input_unit_no VARCHAR(80),
    xml_db_seq_key VARCHAR(16),
    transfer_link_key VARCHAR(48),
    sys_reserve1 VARCHAR(16),
    fixes_reason_code VARCHAR(48),
    business_code VARCHAR(12),
    form_code VARCHAR(48),
    order_top_char VARCHAR(8),
    order_item VARCHAR(16),
    order_other VARCHAR(72),
    wh_code VARCHAR(32),
    join_segment_code_1 VARCHAR(48),
    join_segment_code_2 VARCHAR(48),
    join_segment_code_3 VARCHAR(48),
    counter_corp_cd VARCHAR(48),
    counter_corp_join_segment_code_1 VARCHAR(48),
    counter_corp_join_segment_code_2 VARCHAR(48),
    counter_corp_join_segment_code_3 VARCHAR(48),
    slip_user_open_date_1 DATE,
    slip_user_open_code_1 VARCHAR(96),
    slip_user_open_code_2 TIMESTAMP,
    sys_reserve2 VARCHAR(48),
    slip_remarks VARCHAR(768) NOT NULL,
    approval_remarks VARCHAR(768),
    slip_user_open_area VARCHAR(768),
    slip_user_open_area_2 VARCHAR(240),
    line_num VARCHAR(36) NOT NULL,
    slip_detail_lending_type VARCHAR(4) NOT NULL,
    account_code VARCHAR(48) NOT NULL,
    accounting_dept_code VARCHAR(48) NOT NULL,
    details_type VARCHAR(16),
    details_code VARCHAR(48) NOT NULL,
    items_type VARCHAR(16),
    items_code VARCHAR(48),
    count_ext_code_1 VARCHAR(16),
    count_ext_code_1_type VARCHAR(48),
    count_ext_code_2 VARCHAR(16),
    count_ext_code_2_type VARCHAR(48),
    count_ext_code_3 VARCHAR(16),
    count_ext_code_3_type VARCHAR(48),
    count_ext_code_4 VARCHAR(16),
    count_ext_code_4_type VARCHAR(48),
    count_ext_code_5 VARCHAR(16),
    count_ext_code_5_type VARCHAR(48),
    search_ext_code_1 VARCHAR(16),
    search_ext_code_1_type VARCHAR(48),
    search_ext_code_2 VARCHAR(16),
    search_ext_code_2_type VARCHAR(48),
    search_ext_code_3 VARCHAR(16),
    search_ext_code_3_type VARCHAR(48),
    search_ext_code_4 VARCHAR(16),
    search_ext_code_4_type VARCHAR(48),
    search_ext_code_5 VARCHAR(16),
    search_ext_code_5_type VARCHAR(48),
    business_partner_code VARCHAR(48),
    segment_code VARCHAR(48) NOT NULL,
    cost_burden_center_code VARCHAR(48),
    bill_cash_code VARCHAR(48),
    business_segment_code VARCHAR(48),
    region_segment_code VARCHAR(48),
    customer_segment_code VARCHAR(48),
    user_open_segment_code_1 VARCHAR(48) NOT NULL,
    user_open_segment_code_2 VARCHAR(48),
    match_key VARCHAR(48),
    tran_currency_code VARCHAR(16),
    tran_currency_exchange_rate_type VARCHAR(4),
    tran_currency_rate VARCHAR(52),
    view_currency_exchange_rate_type_1 VARCHAR(4),
    view_currency_rate_1 VARCHAR(52),
    view_currency_exchange_rate_type_2 VARCHAR(4),
    view_currency_rate_2 VARCHAR(52),
    view_currency_exchange_rate_type_3 VARCHAR(4),
    view_currency_rate_3 VARCHAR(52),
    funding_code VARCHAR(48),
    tax_type_code VARCHAR(16),
    sys_reserve3 VARCHAR(16),
    tax_rate_type VARCHAR(24),
    function_currency_amout DECIMAL(20) NOT NULL,
    tran_currency_amout DECIMAL(20),
    reference_tax DECIMAL(20),
    user_open_num_1 VARCHAR(80),
    tax_type VARCHAR(4) NOT NULL,
    history_property_code VARCHAR(144),
    counter_account_code VARCHAR(48),
    sys_reserve4 VARCHAR(48),
    sys_reserve5 VARCHAR(16),
    sys_reserve6 VARCHAR(48),
    sys_reserve7 VARCHAR(16),
    sys_reserve8 VARCHAR(48),
    sys_reserve9 VARCHAR(16),
    sys_reserve10 VARCHAR(48),
    sys_reserve11 VARCHAR(16),
    sys_reserve12 VARCHAR(48),
    sys_reserve13 VARCHAR(16),
    sys_reserve14 VARCHAR(48),
    sys_reserve15 VARCHAR(16),
    sys_reserve16 VARCHAR(48),
    sys_reserve17 VARCHAR(16),
    sys_reserve18 VARCHAR(48),
    sys_reserve19 VARCHAR(48),
    sys_reserve20 VARCHAR(48),
    sys_reserve21 VARCHAR(48),
    sys_reserve22 VARCHAR(48),
    sys_reserve23 VARCHAR(16),
    sys_reserve24 VARCHAR(24),
    sys_reserve25 VARCHAR(16),
    sys_reserve26 VARCHAR(48),
    sys_reserve27 VARCHAR(48),
    sys_reserve28 VARCHAR(48),
    quantity DECIMAL(20),
    unit_cd VARCHAR(48),
    quantity_sub DECIMAL(20),
    unit_cd_sub VARCHAR(48),
    function_currency_price DECIMAL(20),
    tran_currency_price DECIMAL(20),
    ext_num_1 VARCHAR(80),
    ext_num_2 VARCHAR(80),
    ext_num_3 VARCHAR(80),
    user_open_date_1 DATE,
    user_open_code_1 VARCHAR(48),
    user_open_code_2 VARCHAR(48),
    user_open_code_3 VARCHAR(16),
    user_open_code_4 VARCHAR(96),
    user_open_code_5 VARCHAR(96),
    user_open_code_6 VARCHAR(96),
    user_open_code_7 VARCHAR(96),
    user_open_area_1 VARCHAR(288),
    sys_reserve29 VARCHAR(96),
    sys_reserve30 VARCHAR(288),
    user_open_area_2 VARCHAR(144),
    user_open_area_3 VARCHAR(144),
    user_open_code_8 VARCHAR(32),
    user_open_area_5 VARCHAR(96),
    user_open_area_6 VARCHAR(144),
    user_open_area_7 VARCHAR(240),
    user_open_area_8 VARCHAR(288),
    sys_reserve31 VARCHAR(8),
    user_open_date_2 DATE,
    text_description_bill_remarks VARCHAR(768) NOT NULL,
    detail_user_open_area VARCHAR(768),
    detail_user_open_area_2 VARCHAR(1536),
    individual_application_key VARCHAR(48),
    recovery_payment_dept_code VARCHAR(48),
    contract_no VARCHAR(48),
    invoice_no VARCHAR(48),
    recovery_payment_schedule_date DATE,
    bill_cash_closing_date DATE,
    upd_sub_sys_type VARCHAR(4),
    property_control_number VARCHAR(144),
    bill_no VARCHAR(144),
    bill_kind_type VARCHAR(4),
    bill_type VARCHAR(4),
    transition_type VARCHAR(8),
    bill_cash_settlement_date DATE,
    bill_split_type_sys_reserve VARCHAR(4),
    effort_payment_advice_date DATE,
    cash_schedule_date DATE,
    bill_site VARCHAR(16),
    sys_reserve32 VARCHAR(16),
    sys_reserve33 VARCHAR(60),
    bank_account_holder VARCHAR(768),
    payment_place_counter_bank_code VARCHAR(48),
    payment_place VARCHAR(384),
    bill_effort_company_bank_code VARCHAR(48),
    bill_discount_fee DECIMAL(13),
    telegraph_document_transfer_type VARCHAR(4),
    fee_burden_type VARCHAR(4),
    fb_transfer_process_type VARCHAR(4),
    company_bank_account_type VARCHAR(4),
    company_bank_account_no VARCHAR(48),
    counter_bank_account_type VARCHAR(4),
    counter_bank_account_no VARCHAR(48),
    sys_reserve34 VARCHAR(88),
    sys_reserve35 VARCHAR(44),
    dwh_created_user VARCHAR(400),
    dwh_created_datetime TIMESTAMP,
    dwh_updated_user VARCHAR(400),
    dwh_updated_datetime TIMESTAMP
);
CREATE TABLE accounting_data_store_sales (
    input_no VARCHAR(20) NOT NULL,
    input_sys_type VARCHAR(12) NOT NULL,
    corp_cd VARCHAR(8) NOT NULL,
    vote_employee_cd VARCHAR(24) NOT NULL,
    vote_dept_cd VARCHAR(28) NOT NULL,
    approval_employee_cd VARCHAR(24) NOT NULL,
    approval_date DATE NOT NULL,
    approval_status_type DECIMAL(4) NOT NULL,
    journal_type DECIMAL(8) NOT NULL,
    slip_date DATE NOT NULL,
    slip_no VARCHAR(32),
    slip_ope_ban_type DECIMAL(4) NOT NULL,
    journal_reference_type DECIMAL(4) NOT NULL,
    input_unit_no VARCHAR(80),
    xml_db_seq_key VARCHAR(16),
    transfer_link_key VARCHAR(48),
    sys_reserve1 VARCHAR(16),
    fixes_reason_code VARCHAR(48),
    business_code VARCHAR(12),
    form_code VARCHAR(16),
    order_top_char VARCHAR(16),
    order_item VARCHAR(16),
    order_other VARCHAR(72),
    wh_code VARCHAR(32),
    join_segment_code_1 VARCHAR(48),
    join_segment_code_2 VARCHAR(48),
    join_segment_code_3 VARCHAR(48),
    counter_corp_cd VARCHAR(48),
    counter_corp_join_segment_code_1 VARCHAR(48),
    counter_corp_join_segment_code_2 VARCHAR(48),
    counter_corp_join_segment_code_3 VARCHAR(48),
    slip_user_open_date_1 DATE,
    slip_user_open_code_1 VARCHAR(96),
    slip_user_open_code_2 TIMESTAMP,
    sys_reserve2 VARCHAR(48),
    slip_remarks VARCHAR(256) NOT NULL,
    approval_remarks VARCHAR(768),
    slip_user_open_area VARCHAR(768),
    slip_user_open_area_2 VARCHAR(240),
    line_num VARCHAR(16) NOT NULL,
    slip_detail_lending_type DECIMAL(4) NOT NULL,
    account_code VARCHAR(40),
    accounting_dept_code VARCHAR(28) NOT NULL,
    details_type VARCHAR(16),
    details_code VARCHAR(12),
    items_type VARCHAR(16),
    items_code VARCHAR(48),
    count_ext_code_1 VARCHAR(16),
    count_ext_code_1_type VARCHAR(1020),
    count_ext_code_2 VARCHAR(16),
    count_ext_code_2_type VARCHAR(1020),
    count_ext_code_3 VARCHAR(16),
    count_ext_code_3_type VARCHAR(20),
    count_ext_code_4 VARCHAR(16),
    count_ext_code_4_type VARCHAR(20),
    count_ext_code_5 VARCHAR(16),
    count_ext_code_5_type VARCHAR(1020),
    search_ext_code_1 VARCHAR(16),
    search_ext_code_1_type VARCHAR(120),
    search_ext_code_2 VARCHAR(16),
    search_ext_code_2_type VARCHAR(1020),
    search_ext_code_3 VARCHAR(16),
    search_ext_code_3_type VARCHAR(40),
    search_ext_code_4 VARCHAR(16),
    search_ext_code_4_type VARCHAR(48),
    search_ext_code_5 VARCHAR(16),
    search_ext_code_5_type VARCHAR(20),
    business_partner_code VARCHAR(48),
    segment_code VARCHAR(16),
    cost_burden_center_code VARCHAR(48),
    bill_cash_code VARCHAR(48),
    business_segment_code VARCHAR(20),
    region_segment_code VARCHAR(20),
    customer_segment_code VARCHAR(4),
    user_open_segment_code_1 VARCHAR(12) NOT NULL,
    user_open_segment_code_2 VARCHAR(48),
    match_key VARCHAR(48),
    tran_currency_code VARCHAR(16),
    tran_currency_exchange_rate_type VARCHAR(4),
    tran_currency_rate VARCHAR(52),
    view_currency_exchange_rate_type_1 VARCHAR(4),
    view_currency_rate_1 VARCHAR(52),
    view_currency_exchange_rate_type_2 VARCHAR(4),
    view_currency_rate_2 VARCHAR(52),
    view_currency_exchange_rate_type_3 VARCHAR(4),
    view_currency_rate_3 VARCHAR(52),
    funding_code VARCHAR(48),
    tax_type_code VARCHAR(16),
    sys_reserve3 VARCHAR(16),
    tax_rate_type DECIMAL(4,1),
    function_currency_amout DECIMAL(11) NOT NULL,
    tran_currency_amout DECIMAL(20),
    reference_tax DECIMAL(20),
    user_open_num_1 VARCHAR(80),
    tax_type DECIMAL(11) NOT NULL,
    history_property_code VARCHAR(144),
    counter_account_code VARCHAR(48),
    sys_reserve4 VARCHAR(48),
    sys_reserve5 VARCHAR(16),
    sys_reserve6 VARCHAR(48),
    sys_reserve7 VARCHAR(16),
    sys_reserve8 VARCHAR(48),
    sys_reserve9 VARCHAR(16),
    sys_reserve10 VARCHAR(48),
    sys_reserve11 VARCHAR(16),
    sys_reserve12 VARCHAR(48),
    sys_reserve13 VARCHAR(16),
    sys_reserve14 VARCHAR(48),
    sys_reserve15 VARCHAR(16),
    sys_reserve16 VARCHAR(48),
    sys_reserve17 VARCHAR(16),
    sys_reserve18 VARCHAR(48),
    sys_reserve19 VARCHAR(48),
    sys_reserve20 VARCHAR(48),
    sys_reserve21 VARCHAR(48),
    sys_reserve22 VARCHAR(48),
    sys_reserve23 VARCHAR(16),
    sys_reserve24 VARCHAR(24),
    sys_reserve25 VARCHAR(16),
    sys_reserve26 VARCHAR(48),
    sys_reserve27 VARCHAR(48),
    sys_reserve28 VARCHAR(48),
    quantity DECIMAL(11),
    unit_cd VARCHAR(48),
    quantity_sub DECIMAL(20),
    unit_cd_sub VARCHAR(48),
    function_currency_price DECIMAL(20),
    tran_currency_price DECIMAL(20),
    ext_num_1 VARCHAR(80),
    ext_num_2 VARCHAR(80),
    ext_num_3 VARCHAR(80),
    user_open_date_1 DATE,
    user_open_code_1 VARCHAR(48),
    user_open_code_2 VARCHAR(48),
    user_open_code_3 VARCHAR(16),
    user_open_code_4 VARCHAR(96),
    user_open_code_5 VARCHAR(96),
    user_open_code_6 VARCHAR(96),
    user_open_code_7 VARCHAR(96),
    user_open_area_1 VARCHAR(288),
    sys_reserve29 VARCHAR(96),
    sys_reserve30 VARCHAR(288),
    user_open_area_2 VARCHAR(144),
    user_open_area_3 VARCHAR(144),
    user_open_code_8 VARCHAR(32),
    user_open_area_5 VARCHAR(96),
    user_open_area_6 VARCHAR(144),
    user_open_area_7 VARCHAR(240),
    user_open_area_8 VARCHAR(288),
    sys_reserve31 VARCHAR(8),
    user_open_date_2 DATE,
    text_description_bill_remarks VARCHAR(256) NOT NULL,
    detail_user_open_area VARCHAR(768),
    detail_user_open_area_2 VARCHAR(1536),
    individual_application_key VARCHAR(48),
    recovery_payment_dept_code VARCHAR(48),
    contract_no VARCHAR(48),
    invoice_no VARCHAR(48),
    recovery_payment_schedule_date DATE,
    bill_cash_closing_date DATE,
    upd_sub_sys_type VARCHAR(4),
    property_control_number VARCHAR(144),
    bill_no VARCHAR(144),
    bill_kind_type VARCHAR(4),
    bill_type VARCHAR(4),
    transition_type VARCHAR(8),
    bill_cash_settlement_date DATE,
    bill_split_type_sys_reserve VARCHAR(4),
    effort_payment_advice_date DATE,
    cash_schedule_date DATE,
    bill_site VARCHAR(16),
    sys_reserve32 VARCHAR(16),
    sys_reserve33 VARCHAR(60),
    bank_account_holder VARCHAR(768),
    payment_place_counter_bank_code VARCHAR(48),
    payment_place VARCHAR(384),
    bill_effort_company_bank_code VARCHAR(48),
    bill_discount_fee DECIMAL(13),
    telegraph_document_transfer_type VARCHAR(4),
    fee_burden_type VARCHAR(4),
    fb_transfer_process_type VARCHAR(4),
    company_bank_account_type VARCHAR(4),
    company_bank_account_no VARCHAR(48),
    counter_bank_account_type VARCHAR(4),
    counter_bank_account_no VARCHAR(48),
    sys_reserve34 VARCHAR(88),
    sys_reserve35 VARCHAR(44),
    dwh_created_user VARCHAR(400),
    dwh_created_datetime TIMESTAMP,
    dwh_updated_user VARCHAR(400),
    dwh_updated_datetime TIMESTAMP
);
CREATE TABLE out_achievements_store_sales (
    accept_no VARCHAR(88) NOT NULL,
    close_date DATE NOT NULL,
    wh_code VARCHAR(12) NOT NULL,
    goods_code VARCHAR(40) NOT NULL,
    out_qty DECIMAL(9) NOT NULL,
    logimane_slip_no VARCHAR(52) NOT NULL,
    dwh_created_user VARCHAR(400),
    dwh_created_datetime TIMESTAMP,
    dwh_updated_user VARCHAR(400),
    dwh_updated_datetime TIMESTAMP
);
CREATE TABLE out_achievements_mail_order (
    accept_no VARCHAR(88) NOT NULL,
    close_date DATE NOT NULL,
    wh_code VARCHAR(12) NOT NULL,
    goods_code VARCHAR(40) NOT NULL,
    out_qty DECIMAL(9) NOT NULL,
    logimane_slip_no VARCHAR(52) NOT NULL,
    dwh_created_user VARCHAR(400),
    dwh_created_datetime TIMESTAMP,
    dwh_updated_user VARCHAR(400),
    dwh_updated_datetime TIMESTAMP
);
CREATE TABLE arrival_schedule_apparel (
    stock_arrival_date DATE NOT NULL,
    warehouse_cd VARCHAR(12) NOT NULL,
    po_no VARCHAR(48) NOT NULL,
    warehouse_management_no VARCHAR(40) NOT NULL,
    order_count DECIMAL(7) NOT NULL,
    agent_cd VARCHAR(8) NOT NULL,
    order_registrant_name VARCHAR(80),
    comment_code VARCHAR(200),
    dwh_created_user VARCHAR(400),
    dwh_created_datetime TIMESTAMP,
    dwh_updated_user VARCHAR(400),
    dwh_updated_datetime TIMESTAMP
);
CREATE TABLE stock_all (
    center_code VARCHAR(16) NOT NULL,
    stock_kind VARCHAR(8) NOT NULL,
    sh_control_number VARCHAR(100) NOT NULL,
    stock_quantity DECIMAL(7) NOT NULL,
    allocated_quantity DECIMAL(7) NOT NULL,
    dwh_created_user VARCHAR(400),
    dwh_created_datetime TIMESTAMP,
    dwh_updated_user VARCHAR(400),
    dwh_updated_datetime TIMESTAMP
);
CREATE TABLE stock_change (
    wms_stock_io_type VARCHAR(44) NOT NULL,
    slip_date DATE NOT NULL,
    slip_no VARCHAR(52) NOT NULL,
    sh_control_number VARCHAR(100) NOT NULL,
    quantity DECIMAL(13,3) NOT NULL,
    stock_io_type DECIMAL(1) NOT NULL,
    center_code VARCHAR(16) NOT NULL,
    stock_type DECIMAL(1) NOT NULL,
    swh_cmns_target_shop VARCHAR(12),
    center_code_partner VARCHAR(16),
    stock_type_partner VARCHAR(4),
    swh_counter VARCHAR(12),
    receiving_shipping VARCHAR(52),
    receiving_shipping_name VARCHAR(160),
    reason_code_items VARCHAR(12),
    reason_code_name VARCHAR(160),
    reason_code_item VARCHAR(200),
    dwh_created_user VARCHAR(400),
    dwh_created_datetime TIMESTAMP,
    dwh_updated_user VARCHAR(400),
    dwh_updated_datetime TIMESTAMP
);
CREATE TABLE out_indicate_header (
    accept_no VARCHAR(88) NOT NULL,
    cust_no VARCHAR(40),
    record_no VARCHAR(24),
    cust_name VARCHAR(400),
    post_no VARCHAR(32),
    addr1 VARCHAR(200),
    addr2 VARCHAR(200),
    addr3 VARCHAR(200),
    tel_no VARCHAR(60),
    prefecture_code DECIMAL(2),
    cust_flg DECIMAL(1),
    order_date DATE,
    pay_kb VARCHAR(8),
    total_price DECIMAL(10),
    delive_cust_name VARCHAR(400),
    delive_post_no VARCHAR(32),
    delive_addr1 VARCHAR(200),
    delive_addr2 VARCHAR(200),
    delive_addr3 VARCHAR(200),
    delive_tel_no VARCHAR(60),
    gift_flg VARCHAR(4),
    kibou_ymd DATE,
    night_flg VARCHAR(8),
    cosme_price DECIMAL(10),
    health_price DECIMAL(10),
    inner_price DECIMAL(10),
    update_date DATE,
    chit_print_date DATE,
    yamato_bar_code VARCHAR(56),
    gyosha_flg VARCHAR(4),
    status_flg VARCHAR(4),
    slip_ono VARCHAR(80),
    order_no VARCHAR(56),
    clinic_name VARCHAR(400),
    shipment_date DATE,
    shipment_plan_date DATE,
    pack_cnt DECIMAL(10),
    store_code VARCHAR(32),
    period_flg VARCHAR(8),
    delivery_box_gb VARCHAR(8),
    air_delivery_yn VARCHAR(4),
    tax_amt DECIMAL(10),
    conveni_yn VARCHAR(4),
    pudo_yn VARCHAR(4),
    inplan_yn VARCHAR(4),
    over_stock_yn VARCHAR(4),
    reserve_order_yn VARCHAR(4),
    gift_rapping_yn VARCHAR(4),
    kanshi_yn VARCHAR(4),
    fusoku_yn VARCHAR(4),
    airplane_yn VARCHAR(4),
    satofuru_yn VARCHAR(4),
    tokusha_yn VARCHAR(4),
    rakugaki_yn VARCHAR(4),
    multi_sample_yn VARCHAR(4),
    slip_size_code VARCHAR(4),
    wh_code VARCHAR(12),
    agent_cd VARCHAR(52),
    import_yn VARCHAR(4),
    import_date DATE,
    dwh_created_user VARCHAR(400),
    dwh_created_datetime TIMESTAMP,
    dwh_updated_user VARCHAR(400),
    dwh_updated_datetime TIMESTAMP
);
CREATE TABLE out_indicate_detail (
    accept_no VARCHAR(88) NOT NULL,
    seq DECIMAL(5) NOT NULL,
    prod_no DECIMAL(10) NOT NULL,
    qty DECIMAL(7),
    chit_print_date DATE,
    yamato_bar_code VARCHAR(56),
    gyosha_flg VARCHAR(4),
    invoice_branch_number VARCHAR(4),
    bumon_kbn VARCHAR(4),
    prod_price DECIMAL(6),
    order_type VARCHAR(4),
    dwh_created_user VARCHAR(400),
    dwh_created_datetime TIMESTAMP,
    dwh_updated_user VARCHAR(400),
    dwh_updated_datetime TIMESTAMP
);
CREATE TABLE address_item_code_linkage (
    mdm_integration_management_cd DECIMAL(19) NOT NULL,
    address_item_code VARCHAR(60) NOT NULL,
    mdm_integration_management_cd_nk DECIMAL(19),
    insert_date TIMESTAMP NOT NULL,
    insert_id VARCHAR(128) NOT NULL,
    modify_date TIMESTAMP,
    modify_id VARCHAR(128),
    dwh_created_user VARCHAR(400),
    dwh_created_datetime TIMESTAMP,
    dwh_updated_user VARCHAR(400),
    dwh_updated_datetime TIMESTAMP
);
CREATE TABLE accountin_pattern_gb (
    accountin_pattern_gb VARCHAR(8) NOT NULL,
    accountin_pattern_gb_name VARCHAR(240),
    accountin_pattern_gb_nk VARCHAR(8),
    insert_date TIMESTAMP NOT NULL,
    insert_id VARCHAR(128) NOT NULL,
    modify_date TIMESTAMP,
    modify_id VARCHAR(128),
    dwh_created_user VARCHAR(400),
    dwh_created_datetime TIMESTAMP,
    dwh_updated_user VARCHAR(400),
    dwh_updated_datetime TIMESTAMP
);
CREATE TABLE dgroup (
    lgroup VARCHAR(8) NOT NULL,
    mgroup VARCHAR(8) NOT NULL,
    sgroup VARCHAR(8) NOT NULL,
    dgroup VARCHAR(8) NOT NULL,
    dgroup_name VARCHAR(240),
    lgroup_nk VARCHAR(8),
    insert_date TIMESTAMP NOT NULL,
    insert_id VARCHAR(128) NOT NULL,
    modify_date TIMESTAMP,
    modify_id VARCHAR(128),
    dwh_created_user VARCHAR(400),
    dwh_created_datetime TIMESTAMP,
    dwh_updated_user VARCHAR(400),
    dwh_updated_datetime TIMESTAMP
);
CREATE TABLE business_segment (
    business_segment VARCHAR(20) NOT NULL,
    business_segment_name VARCHAR(240),
    business_segment_nk VARCHAR(20),
    insert_date TIMESTAMP NOT NULL,
    insert_id VARCHAR(128) NOT NULL,
    modify_date TIMESTAMP,
    modify_id VARCHAR(128),
    dwh_created_user VARCHAR(400),
    dwh_created_datetime TIMESTAMP,
    dwh_updated_user VARCHAR(400),
    dwh_updated_datetime TIMESTAMP
);
CREATE TABLE product_series (
    product_series VARCHAR(20) NOT NULL,
    product_series_name VARCHAR(240),
    product_series_nk VARCHAR(20),
    insert_date TIMESTAMP NOT NULL,
    insert_id VARCHAR(128) NOT NULL,
    modify_date TIMESTAMP,
    modify_id VARCHAR(128),
    dwh_created_user VARCHAR(400),
    dwh_created_datetime TIMESTAMP,
    dwh_updated_user VARCHAR(400),
    dwh_updated_datetime TIMESTAMP
);
CREATE TABLE product_segment (
    product_segment VARCHAR(20) NOT NULL,
    product_segment_name VARCHAR(240),
    product_segment_nk VARCHAR(20),
    insert_date TIMESTAMP NOT NULL,
    insert_id VARCHAR(128) NOT NULL,
    modify_date TIMESTAMP,
    modify_id VARCHAR(128),
    dwh_created_user VARCHAR(400),
    dwh_created_datetime TIMESTAMP,
    dwh_updated_user VARCHAR(400),
    dwh_updated_datetime TIMESTAMP
);
CREATE TABLE product_cat (
    product_cat VARCHAR(20) NOT NULL,
    product_cat_name VARCHAR(240),
    product_cat_nk VARCHAR(20),
    insert_date TIMESTAMP NOT NULL,
    insert_id VARCHAR(128) NOT NULL,
    modify_date TIMESTAMP,
    modify_id VARCHAR(128),
    dwh_created_user VARCHAR(400),
    dwh_created_datetime TIMESTAMP,
    dwh_updated_user VARCHAR(400),
    dwh_updated_datetime TIMESTAMP
);
CREATE TABLE sgroup (
    lgroup VARCHAR(8) NOT NULL,
    mgroup VARCHAR(8) NOT NULL,
    sgroup VARCHAR(8) NOT NULL,
    sgroup_name VARCHAR(240),
    lgroup_nk VARCHAR(8),
    insert_date TIMESTAMP NOT NULL,
    insert_id VARCHAR(128) NOT NULL,
    modify_date TIMESTAMP,
    modify_id VARCHAR(128),
    dwh_created_user VARCHAR(400),
    dwh_created_datetime TIMESTAMP,
    dwh_updated_user VARCHAR(400),
    dwh_updated_datetime TIMESTAMP
);
CREATE TABLE mgroup (
    lgroup VARCHAR(8) NOT NULL,
    mgroup VARCHAR(8) NOT NULL,
    mgroup_name VARCHAR(240),
    lgroup_nk VARCHAR(8),
    insert_date TIMESTAMP NOT NULL,
    insert_id VARCHAR(128) NOT NULL,
    modify_date TIMESTAMP,
    modify_id VARCHAR(128),
    dwh_created_user VARCHAR(400),
    dwh_created_datetime TIMESTAMP,
    dwh_updated_user VARCHAR(400),
    dwh_updated_datetime TIMESTAMP
);
CREATE TABLE core_department (
    core_department VARCHAR(8) NOT NULL,
    core_department_name VARCHAR(240),
    core_department_nk VARCHAR(8),
    insert_date TIMESTAMP NOT NULL,
    insert_id VARCHAR(128) NOT NULL,
    modify_date TIMESTAMP,
    modify_id VARCHAR(128),
    dwh_created_user VARCHAR(400),
    dwh_created_datetime TIMESTAMP,
    dwh_updated_user VARCHAR(400),
    dwh_updated_datetime TIMESTAMP
);
CREATE TABLE product_type (
    product_type VARCHAR(8) NOT NULL,
    product_type_name VARCHAR(240),
    product_type_nk VARCHAR(8),
    insert_date TIMESTAMP NOT NULL,
    insert_id VARCHAR(128) NOT NULL,
    modify_date TIMESTAMP,
    modify_id VARCHAR(128),
    dwh_created_user VARCHAR(400),
    dwh_created_datetime TIMESTAMP,
    dwh_updated_user VARCHAR(400),
    dwh_updated_datetime TIMESTAMP
);
CREATE TABLE lgroup (
    lgroup VARCHAR(8) NOT NULL,
    lgroup_name VARCHAR(240),
    lgroup_nk VARCHAR(8),
    insert_date TIMESTAMP NOT NULL,
    insert_id VARCHAR(128) NOT NULL,
    modify_date TIMESTAMP,
    modify_id VARCHAR(128),
    dwh_created_user VARCHAR(400),
    dwh_created_datetime TIMESTAMP,
    dwh_updated_user VARCHAR(400),
    dwh_updated_datetime TIMESTAMP
);
CREATE TABLE coupon_customer (
    coupon_management_code VARCHAR(64) NOT NULL,
    customer_code VARCHAR(64) NOT NULL,
    neo_customer_no VARCHAR(48),
    coupon_issue_status DECIMAL(1) NOT NULL,
    coupon_used_count DECIMAL(8) NOT NULL,
    coupon_used_date DATE,
    baitai_code VARCHAR(40),
    orm_rowid DECIMAL(38) NOT NULL,
    created_user VARCHAR(400) NOT NULL,
    created_datetime TIMESTAMP NOT NULL,
    updated_user VARCHAR(400) NOT NULL,
    updated_datetime TIMESTAMP NOT NULL,
    dwh_created_user VARCHAR(400),
    dwh_created_datetime TIMESTAMP,
    dwh_updated_user VARCHAR(400),
    dwh_updated_datetime TIMESTAMP
);
CREATE TABLE campaign_customer (
    campaign_instructions_code VARCHAR(64) NOT NULL,
    customer_code VARCHAR(64) NOT NULL,
    joken_type VARCHAR(4) NOT NULL,
    orm_rowid DECIMAL(38) NOT NULL,
    created_user VARCHAR(400) NOT NULL,
    created_datetime TIMESTAMP NOT NULL,
    updated_user VARCHAR(400) NOT NULL,
    updated_datetime TIMESTAMP NOT NULL,
    dwh_created_user VARCHAR(400),
    dwh_created_datetime TIMESTAMP,
    dwh_updated_user VARCHAR(400),
    dwh_updated_datetime TIMESTAMP
);
CREATE TABLE campaign_count_customer (
    campaign_instructions_code VARCHAR(64) NOT NULL,
    customer_code VARCHAR(64) NOT NULL,
    campaign_used_count DECIMAL(8) NOT NULL,
    orm_rowid DECIMAL(38) NOT NULL,
    created_user VARCHAR(400) NOT NULL,
    created_datetime TIMESTAMP NOT NULL,
    updated_user VARCHAR(400) NOT NULL,
    updated_datetime TIMESTAMP NOT NULL,
    dwh_created_user VARCHAR(400),
    dwh_created_datetime TIMESTAMP,
    dwh_updated_user VARCHAR(400),
    dwh_updated_datetime TIMESTAMP
);
CREATE TABLE campaign_count_commodity (
    campaign_instructions_code VARCHAR(64) NOT NULL,
    customer_code VARCHAR(64) NOT NULL,
    shop_code VARCHAR(64) NOT NULL,
    commodity_code VARCHAR(64) NOT NULL,
    campaign_used_count DECIMAL(8),
    orm_rowid DECIMAL(38) NOT NULL,
    created_user VARCHAR(400) NOT NULL,
    created_datetime TIMESTAMP NOT NULL,
    updated_user VARCHAR(400) NOT NULL,
    updated_datetime TIMESTAMP NOT NULL,
    dwh_created_user VARCHAR(400),
    dwh_created_datetime TIMESTAMP,
    dwh_updated_user VARCHAR(400),
    dwh_updated_datetime TIMESTAMP
);
CREATE TABLE order_campaign_use_amount (
    order_no VARCHAR(64) NOT NULL,
    tax_group_code VARCHAR(32) NOT NULL,
    tax_no DECIMAL(3) NOT NULL,
    use_code_type VARCHAR(4) NOT NULL,
    use_code VARCHAR(64) NOT NULL,
    use_amount DECIMAL(8) NOT NULL,
    tax_rate DECIMAL(3) NOT NULL,
    orm_rowid DECIMAL(38) NOT NULL,
    created_user VARCHAR(400) NOT NULL,
    created_datetime TIMESTAMP NOT NULL,
    updated_user VARCHAR(400) NOT NULL,
    updated_datetime TIMESTAMP NOT NULL,
    dwh_created_user VARCHAR(400),
    dwh_created_datetime TIMESTAMP,
    dwh_updated_user VARCHAR(400),
    dwh_updated_datetime TIMESTAMP
);
CREATE TABLE order_campaign (
    order_no VARCHAR(64) NOT NULL,
    campaign_instructions_code VARCHAR(64) NOT NULL,
    campaign_instructions_name VARCHAR(200) NOT NULL,
    campaign_description VARCHAR(400),
    campaign_end_date TIMESTAMP NOT NULL,
    orm_rowid DECIMAL(38) NOT NULL,
    created_user VARCHAR(400) NOT NULL,
    created_datetime TIMESTAMP NOT NULL,
    updated_user VARCHAR(400) NOT NULL,
    updated_datetime TIMESTAMP NOT NULL,
    dwh_created_user VARCHAR(400),
    dwh_created_datetime TIMESTAMP,
    dwh_updated_user VARCHAR(400),
    dwh_updated_datetime TIMESTAMP
);
CREATE TABLE order_campaign_history (
    order_history_id DECIMAL(38) NOT NULL,
    order_no VARCHAR(64) NOT NULL,
    campaign_instructions_code VARCHAR(64) NOT NULL,
    campaign_instructions_name VARCHAR(200) NOT NULL,
    campaign_description VARCHAR(400),
    campaign_end_date TIMESTAMP NOT NULL,
    orm_rowid DECIMAL(38) NOT NULL,
    created_user VARCHAR(400) NOT NULL,
    created_datetime TIMESTAMP NOT NULL,
    updated_user VARCHAR(400) NOT NULL,
    updated_datetime TIMESTAMP NOT NULL,
    dwh_created_user VARCHAR(400),
    dwh_created_datetime TIMESTAMP,
    dwh_updated_user VARCHAR(400),
    dwh_updated_datetime TIMESTAMP
);
CREATE TABLE card_info (
    customer_code VARCHAR(64) NOT NULL,
    credit_card_kanri_no VARCHAR(48) NOT NULL,
    credit_card_kanri_detail_no VARCHAR(16) NOT NULL,
    credit_card_no VARCHAR(200) NOT NULL,
    card_expire_year VARCHAR(16),
    card_expire_month VARCHAR(8),
    card_holder VARCHAR(600) NOT NULL,
    card_brand VARCHAR(8) NOT NULL,
    default_use_flag DECIMAL(1) NOT NULL,
    change_datetime TIMESTAMP NOT NULL,
    change_channel_kbn VARCHAR(8) NOT NULL,
    card_keep_ng_flg DECIMAL(1) NOT NULL,
    change_reason_kbn VARCHAR(8) NOT NULL,
    change_reason VARCHAR(800) NOT NULL,
    change_user_code DECIMAL(38),
    dhc_card_flg DECIMAL(1) NOT NULL,
    crm_card_id VARCHAR(80),
    crm_card_updated_datetime TIMESTAMP,
    orm_rowid DECIMAL(38) NOT NULL,
    created_user VARCHAR(400) NOT NULL,
    created_datetime TIMESTAMP NOT NULL,
    updated_user VARCHAR(400) NOT NULL,
    updated_datetime TIMESTAMP NOT NULL,
    dwh_created_user VARCHAR(400),
    dwh_created_datetime TIMESTAMP,
    dwh_updated_user VARCHAR(400),
    dwh_updated_datetime TIMESTAMP
);
CREATE TABLE order_payment_mng (
    order_no VARCHAR(64) NOT NULL,
    order_payment_no DECIMAL(8) NOT NULL,
    payment_type VARCHAR(8),
    payment_identify_code VARCHAR(65535),
    payment_process_type VARCHAR(8),
    payment_process_status VARCHAR(8),
    payment_process_price DECIMAL(8),
    payment_process_datetime TIMESTAMP,
    payment_process_send_content VARCHAR(65535),
    payment_process_receive_content VARCHAR(65535),
    henpin_request_no VARCHAR(40),
    orm_rowid DECIMAL(38) NOT NULL,
    created_user VARCHAR(400) NOT NULL,
    created_datetime TIMESTAMP NOT NULL,
    updated_user VARCHAR(400) NOT NULL,
    updated_datetime TIMESTAMP NOT NULL,
    dwh_created_user VARCHAR(400),
    dwh_created_datetime TIMESTAMP,
    dwh_updated_user VARCHAR(400),
    dwh_updated_datetime TIMESTAMP
);
CREATE TABLE regular_sale_cont_composition (
    regular_contract_no VARCHAR(56) NOT NULL,
    regular_contract_detail_no DECIMAL(3) NOT NULL,
    composition_no DECIMAL(2) NOT NULL,
    shop_code VARCHAR(64) NOT NULL,
    parent_commodity_code VARCHAR(64) NOT NULL,
    parent_sku_code VARCHAR(96) NOT NULL,
    child_commodity_code VARCHAR(64) NOT NULL,
    child_sku_code VARCHAR(96) NOT NULL,
    commodity_name VARCHAR(400) NOT NULL,
    composition_quantity DECIMAL(2) NOT NULL,
    regular_sale_composition_no VARCHAR(32) NOT NULL,
    regular_sale_composition_name VARCHAR(200),
    regular_sale_commodity_type VARCHAR(4),
    regular_order_count_min_limit DECIMAL(5),
    regular_order_count_max_limit DECIMAL(5),
    regular_order_count_interval DECIMAL(5),
    orm_rowid DECIMAL(38) NOT NULL,
    created_user VARCHAR(400) NOT NULL,
    created_datetime TIMESTAMP NOT NULL,
    updated_user VARCHAR(400) NOT NULL,
    updated_datetime TIMESTAMP NOT NULL,
    dwh_created_user VARCHAR(400),
    dwh_created_datetime TIMESTAMP,
    dwh_updated_user VARCHAR(400),
    dwh_updated_datetime TIMESTAMP
);
CREATE TABLE category (
    category_code VARCHAR(64) NOT NULL,
    category_name_pc VARCHAR(80) NOT NULL,
    category_name_sp VARCHAR(40),
    parent_category_code VARCHAR(64) NOT NULL,
    path VARCHAR(1024) NOT NULL,
    depth DECIMAL(2) NOT NULL,
    display_order DECIMAL(8) NOT NULL,
    commodity_count DECIMAL(12),
    last_related_count_datetime TIMESTAMP,
    public_commodity_count DECIMAL(12),
    last_public_count_datetime TIMESTAMP,
    orm_rowid DECIMAL(38) NOT NULL,
    created_user VARCHAR(400) NOT NULL,
    created_datetime TIMESTAMP NOT NULL,
    updated_user VARCHAR(400) NOT NULL,
    updated_datetime TIMESTAMP NOT NULL,
    dwh_created_user VARCHAR(400),
    dwh_created_datetime TIMESTAMP,
    dwh_updated_user VARCHAR(400),
    dwh_updated_datetime TIMESTAMP
);
CREATE TABLE category_commodity (
    shop_code VARCHAR(64) NOT NULL,
    category_code VARCHAR(64) NOT NULL,
    commodity_code VARCHAR(64) NOT NULL,
    category_search_path VARCHAR(1024) NOT NULL,
    search_category_code0 VARCHAR(64) NOT NULL,
    search_category_code1 VARCHAR(64) NOT NULL,
    search_category_code2 VARCHAR(64) NOT NULL,
    search_category_code3 VARCHAR(64) NOT NULL,
    search_category_code4 VARCHAR(64) NOT NULL,
    search_category_code5 VARCHAR(64) NOT NULL,
    search_category_code6 VARCHAR(64) NOT NULL,
    orm_rowid DECIMAL(38) NOT NULL,
    created_user VARCHAR(400) NOT NULL,
    created_datetime TIMESTAMP NOT NULL,
    updated_user VARCHAR(400) NOT NULL,
    updated_datetime TIMESTAMP NOT NULL,
    dwh_created_user VARCHAR(400),
    dwh_created_datetime TIMESTAMP,
    dwh_updated_user VARCHAR(400),
    dwh_updated_datetime TIMESTAMP
);
CREATE TABLE returns_header (
    henpin_request_no VARCHAR(40) NOT NULL,
    order_no VARCHAR(64) NOT NULL,
    customer_code VARCHAR(64) NOT NULL,
    neo_customer_no VARCHAR(48),
    henpin_confirm_status VARCHAR(4) NOT NULL,
    henpin_request_datetime TIMESTAMP NOT NULL,
    henpin_confirm_datetime TIMESTAMP,
    henpin_recieve_user_code DECIMAL(38),
    henpin_change_kbn VARCHAR(4) NOT NULL,
    henpin_reason_kbn VARCHAR(8) NOT NULL,
    henpin_bill_cancel_flg DECIMAL(1) NOT NULL,
    henpin_shipping_flg DECIMAL(1) NOT NULL,
    henpin_souko_kbn VARCHAR(4) NOT NULL,
    total_shipping_amount DECIMAL(10) NOT NULL,
    adjustment_amount DECIMAL(10) NOT NULL,
    bill_price DECIMAL(10) NOT NULL,
    bill_price_bf_henpin DECIMAL(10) NOT NULL,
    appropriation_amount_bf_henpin DECIMAL(10) NOT NULL,
    deposit_occur_amount DECIMAL(10) NOT NULL,
    delivery_note_republish_flg DECIMAL(1) NOT NULL,
    credit_cancel_flg DECIMAL(1) NOT NULL,
    request_af_henpin_confirm_kbn VARCHAR(4) NOT NULL,
    amzn_refund_flg DECIMAL(1) NOT NULL,
    amzn_refund_id VARCHAR(65535),
    amzn_refund_status VARCHAR(8),
    amzn_refund_initiated_datetime TIMESTAMP,
    amzn_refunded_datetime TIMESTAMP,
    refund_amount DECIMAL(10),
    bank_name VARCHAR(160),
    bank_branch_name VARCHAR(160),
    account_type VARCHAR(4),
    account_no VARCHAR(60),
    account_name VARCHAR(120),
    registered_mail_address VARCHAR(560),
    registered_mail_name VARCHAR(400),
    registered_remarks VARCHAR(4000),
    appropriation_date DATE,
    appropriation_amount DECIMAL(10),
    wms_contact_flg DECIMAL(1) NOT NULL,
    wms_auto_confirm_flg DECIMAL(1) NOT NULL,
    sales_recording_date DATE,
    sales_recording_flg DECIMAL(1) NOT NULL,
    inquiry_kanri_no VARCHAR(40),
    cancel_flg DECIMAL(1) NOT NULL,
    change_user_code DECIMAL(38),
    before_grant_point_total DECIMAL(10),
    before_reduction_point_total DECIMAL(10),
    after_grant_plan_point_prod DECIMAL(10),
    after_grant_plan_point_other DECIMAL(10),
    after_grant_plan_point_total DECIMAL(10),
    after_grant_point_prod DECIMAL(10),
    after_grant_point_other DECIMAL(10),
    after_grant_point_total DECIMAL(10),
    after_reduction_plan_point_total DECIMAL(10),
    after_reduction_point_total DECIMAL(10),
    orm_rowid DECIMAL(38) NOT NULL,
    created_user VARCHAR(400) NOT NULL,
    created_datetime TIMESTAMP NOT NULL,
    updated_user VARCHAR(400) NOT NULL,
    updated_datetime TIMESTAMP NOT NULL,
    dwh_created_user VARCHAR(400),
    dwh_created_datetime TIMESTAMP,
    dwh_updated_user VARCHAR(400),
    dwh_updated_datetime TIMESTAMP
);
CREATE TABLE returns_detail (
    henpin_request_no VARCHAR(40) NOT NULL,
    shipping_no VARCHAR(64) NOT NULL,
    shipping_detail_no DECIMAL(16) NOT NULL,
    order_no VARCHAR(64) NOT NULL,
    order_detail_no DECIMAL(16) NOT NULL,
    shop_code VARCHAR(64) NOT NULL,
    sku_code VARCHAR(96) NOT NULL,
    commodity_code VARCHAR(64) NOT NULL,
    commodity_name VARCHAR(400) NOT NULL,
    commodity_kind VARCHAR(8) NOT NULL,
    baitai_code VARCHAR(40) NOT NULL,
    baitai_name VARCHAR(200) NOT NULL,
    hinban_code VARCHAR(96) NOT NULL,
    benefits_code VARCHAR(40),
    benefits_name VARCHAR(200),
    henpin_qt DECIMAL(5) NOT NULL,
    regular_contract_no VARCHAR(56),
    unit_price DECIMAL(8) NOT NULL,
    discount_price DECIMAL(8),
    discount_amount DECIMAL(8),
    retail_price DECIMAL(8) NOT NULL,
    retail_tax_group_code VARCHAR(32) NOT NULL,
    retail_tax_no DECIMAL(3) NOT NULL,
    retail_tax_rate DECIMAL(3) NOT NULL,
    retail_tax DECIMAL(10) NOT NULL,
    commodity_tax DECIMAL(10) NOT NULL,
    commodity_tax_type DECIMAL(1) NOT NULL,
    purchasing_amount DECIMAL(8) NOT NULL,
    henpin_price DECIMAL(8) NOT NULL,
    henpin_yoyaku_qt DECIMAL(5) NOT NULL,
    change_qt DECIMAL(5) NOT NULL,
    henpin_support_kind VARCHAR(8),
    wms_henpin_rireki_no VARCHAR(40),
    copy_soko_shiji VARCHAR(160),
    grant_plan_point_prod_detail DECIMAL(10),
    reduction_plan_point_prod_detail DECIMAL(10),
    campaign_instructions_code VARCHAR(64),
    campaign_instructions_name VARCHAR(200),
    orm_rowid DECIMAL(38) NOT NULL,
    created_user VARCHAR(400) NOT NULL,
    created_datetime TIMESTAMP NOT NULL,
    updated_user VARCHAR(400) NOT NULL,
    updated_datetime TIMESTAMP NOT NULL,
    dwh_created_user VARCHAR(400),
    dwh_created_datetime TIMESTAMP,
    dwh_updated_user VARCHAR(400),
    dwh_updated_datetime TIMESTAMP
);
CREATE TABLE sales_record (
    earnings_jisseki_no DECIMAL(15) NOT NULL,
    order_henpin_kbn VARCHAR(4) NOT NULL,
    sales_detail_kbn VARCHAR(8) NOT NULL,
    shipping_no VARCHAR(64),
    shipping_detail_no DECIMAL(16),
    order_no VARCHAR(64),
    order_detail_no DECIMAL(16),
    shipping_date DATE NOT NULL,
    sales_recording_date DATE NOT NULL,
    marketing_channel VARCHAR(8) NOT NULL,
    ext_payment_method_type VARCHAR(8),
    shop_code VARCHAR(64),
    main_product_no VARCHAR(96),
    product_no VARCHAR(96),
    period_flg VARCHAR(8),
    baitai_code VARCHAR(40),
    commodity_code VARCHAR(64),
    hinban_code VARCHAR(96),
    commodity_name VARCHAR(400),
    commodity_kind VARCHAR(8),
    commodity_group VARCHAR(20),
    commodity_series VARCHAR(20),
    commodity_segment VARCHAR(20),
    business_segment VARCHAR(20),
    parent_commodity_code VARCHAR(64),
    commodity_amount DECIMAL(8),
    campaign_instructions_code VARCHAR(64),
    coupon_management_code VARCHAR(64),
    incurred_price DECIMAL(8) NOT NULL,
    tax_group_code VARCHAR(32),
    tax_no DECIMAL(3),
    tax_rate DECIMAL(3),
    commodity_tax_type DECIMAL(1),
    business_partner_code VARCHAR(32),
    sales_bumon_cd VARCHAR(28),
    channel_cd VARCHAR(20),
    sales_link_status DECIMAL(1) NOT NULL,
    sales_link_datetime TIMESTAMP,
    orm_rowid DECIMAL(38) NOT NULL,
    created_user VARCHAR(400) NOT NULL,
    created_datetime TIMESTAMP NOT NULL,
    updated_user VARCHAR(400) NOT NULL,
    updated_datetime TIMESTAMP NOT NULL,
    dwh_created_user VARCHAR(400),
    dwh_created_datetime TIMESTAMP,
    dwh_updated_user VARCHAR(400),
    dwh_updated_datetime TIMESTAMP
);
CREATE TABLE shipping_detail (
    shipping_no VARCHAR(64) NOT NULL,
    shipping_detail_no DECIMAL(16) NOT NULL,
    shop_code VARCHAR(64) NOT NULL,
    sku_code VARCHAR(96) NOT NULL,
    unit_price DECIMAL(8) NOT NULL,
    discount_price DECIMAL(8),
    discount_amount DECIMAL(8),
    retail_price DECIMAL(8) NOT NULL,
    retail_tax_group_code VARCHAR(32) NOT NULL,
    retail_tax_no DECIMAL(3) NOT NULL,
    retail_tax_rate DECIMAL(3) NOT NULL,
    retail_tax DECIMAL(10) NOT NULL,
    purchasing_amount DECIMAL(8) NOT NULL,
    gift_code VARCHAR(64),
    gift_name VARCHAR(160),
    gift_price DECIMAL(8),
    gift_tax_group_code VARCHAR(32),
    gift_tax_no DECIMAL(3),
    gift_tax_rate DECIMAL(3),
    gift_tax DECIMAL(10),
    gift_tax_type DECIMAL(1),
    noshi_code VARCHAR(64),
    noshi_name VARCHAR(160),
    noshi_price DECIMAL(8),
    noshi_tax_group_code VARCHAR(32),
    noshi_tax_no DECIMAL(3),
    noshi_tax_rate DECIMAL(3),
    noshi_tax DECIMAL(10),
    noshi_tax_type DECIMAL(1),
    noshi_nameplate VARCHAR(400),
    noshi_message VARCHAR(800),
    air_transport_flg DECIMAL(1) NOT NULL,
    delivery_note_no_disp_flg DECIMAL(1) NOT NULL,
    hasso_souko_cd VARCHAR(24),
    shipping_hold_kbn VARCHAR(4) NOT NULL,
    shipping_hold_date DATE,
    order_detail_no DECIMAL(16),
    tracking_out_flg DECIMAL(1) NOT NULL,
    souko_shiji VARCHAR(160),
    orm_rowid DECIMAL(38) NOT NULL,
    created_user VARCHAR(400) NOT NULL,
    created_datetime TIMESTAMP NOT NULL,
    updated_user VARCHAR(400) NOT NULL,
    updated_datetime TIMESTAMP NOT NULL,
    dwh_created_user VARCHAR(400),
    dwh_created_datetime TIMESTAMP,
    dwh_updated_user VARCHAR(400),
    dwh_updated_datetime TIMESTAMP
);
CREATE TABLE shipping_detail_composition (
    shipping_no VARCHAR(64) NOT NULL,
    shipping_detail_no DECIMAL(16) NOT NULL,
    composition_no DECIMAL(2) NOT NULL,
    shop_code VARCHAR(64) NOT NULL,
    parent_commodity_code VARCHAR(64) NOT NULL,
    parent_sku_code VARCHAR(96) NOT NULL,
    child_commodity_code VARCHAR(64) NOT NULL,
    child_sku_code VARCHAR(96) NOT NULL,
    commodity_name VARCHAR(400) NOT NULL,
    standard_detail1_name VARCHAR(80),
    standard_detail2_name VARCHAR(80),
    unit_price DECIMAL(8) NOT NULL,
    discount_amount DECIMAL(8),
    retail_price DECIMAL(8) NOT NULL,
    retail_tax DECIMAL(10) NOT NULL,
    commodity_tax_group_code VARCHAR(32) NOT NULL,
    commodity_tax_no DECIMAL(3) NOT NULL,
    commodity_tax_rate DECIMAL(3) NOT NULL,
    commodity_tax DECIMAL(10) NOT NULL,
    commodity_tax_type DECIMAL(1) NOT NULL,
    composition_quantity DECIMAL(2) NOT NULL,
    stock_management_type DECIMAL(1) NOT NULL,
    stock_allocated_kbn VARCHAR(8) NOT NULL,
    allocated_warehouse_code VARCHAR(24),
    allocated_quantity DECIMAL(8) NOT NULL,
    arrival_reserved_quantity DECIMAL(8) NOT NULL,
    orm_rowid DECIMAL(38) NOT NULL,
    created_user VARCHAR(400) NOT NULL,
    created_datetime TIMESTAMP NOT NULL,
    updated_user VARCHAR(400) NOT NULL,
    updated_datetime TIMESTAMP NOT NULL,
    dwh_created_user VARCHAR(400),
    dwh_created_datetime TIMESTAMP,
    dwh_updated_user VARCHAR(400),
    dwh_updated_datetime TIMESTAMP
);
CREATE TABLE stock (
    shop_code VARCHAR(64) NOT NULL,
    sku_code VARCHAR(96) NOT NULL,
    allocated_warehouse_code VARCHAR(24) NOT NULL,
    commodity_code VARCHAR(64) NOT NULL,
    wms_stock_quantity DECIMAL(8),
    stock_quantity DECIMAL(8) NOT NULL,
    allocated_quantity DECIMAL(8) NOT NULL,
    reserved_quantity DECIMAL(8),
    temporary_allocated_quantity DECIMAL(8) NOT NULL,
    arrival_reserved_quantity DECIMAL(8) NOT NULL,
    temporary_reserved_quantity DECIMAL(8) NOT NULL,
    reservation_limit DECIMAL(8),
    stock_threshold DECIMAL(8) NOT NULL,
    stock_arrival_date DATE,
    arrival_quantity DECIMAL(8),
    orm_rowid DECIMAL(38) NOT NULL,
    created_user VARCHAR(400) NOT NULL,
    created_datetime TIMESTAMP NOT NULL,
    updated_user VARCHAR(400) NOT NULL,
    updated_datetime TIMESTAMP NOT NULL,
    dwh_created_user VARCHAR(400),
    dwh_created_datetime TIMESTAMP,
    dwh_updated_user VARCHAR(400),
    dwh_updated_datetime TIMESTAMP
);
CREATE TABLE stock_io_detail (
    stock_io_id DECIMAL(38) NOT NULL,
    shop_code VARCHAR(64) NOT NULL,
    stock_io_date DATE NOT NULL,
    sku_code VARCHAR(96) NOT NULL,
    stock_io_quantity DECIMAL(8) NOT NULL,
    stock_io_type DECIMAL(1) NOT NULL,
    memo VARCHAR(800),
    orm_rowid DECIMAL(38) NOT NULL,
    created_user VARCHAR(400) NOT NULL,
    created_datetime TIMESTAMP NOT NULL,
    updated_user VARCHAR(400) NOT NULL,
    updated_datetime TIMESTAMP NOT NULL,
    dwh_created_user VARCHAR(400),
    dwh_created_datetime TIMESTAMP,
    dwh_updated_user VARCHAR(400),
    dwh_updated_datetime TIMESTAMP
);
CREATE TABLE favorite_product (
    id VARCHAR(72) NOT NULL,
    ownerid VARCHAR(72) NOT NULL,
    isdeleted VARCHAR(20),
    name VARCHAR(320),
    createddate TIMESTAMP NOT NULL,
    createdbyid VARCHAR(72) NOT NULL,
    lastmodifieddate TIMESTAMP NOT NULL,
    lastmodifiedbyid VARCHAR(72) NOT NULL,
    systemmodstamp TIMESTAMP NOT NULL,
    accountid__c VARCHAR(72) NOT NULL,
    productid__c VARCHAR(72) NOT NULL,
    productcode__c VARCHAR(5200),
    productname__c VARCHAR(5200),
    isdeleted__c VARCHAR(20),
    dwh_created_user VARCHAR(400),
    dwh_created_datetime TIMESTAMP,
    dwh_updated_user VARCHAR(400),
    dwh_updated_datetime TIMESTAMP
);
CREATE TABLE member (
    id VARCHAR(72) NOT NULL,
    isdeleted VARCHAR(20),
    masterrecordid VARCHAR(72),
    name VARCHAR(1020),
    lastname VARCHAR(320),
    firstname VARCHAR(160),
    salutation VARCHAR(1020),
    type VARCHAR(1020),
    recordtypeid VARCHAR(72),
    parentid VARCHAR(72),
    personmailingstreet VARCHAR(1020),
    personmailingcity VARCHAR(160),
    personmailingstate VARCHAR(320),
    personmailingstatecode VARCHAR(320),
    personmailingpostalcode VARCHAR(80),
    personmailingcountry VARCHAR(320),
    personmailinglatitude DECIMAL(18),
    personmailinglongitude DECIMAL(18),
    personmailinggeocodeaccuracy VARCHAR(1020),
    personmailingaddress VARCHAR(2060),
    shippingstreet VARCHAR(1020),
    shippingcity VARCHAR(320),
    shippingstate VARCHAR(320),
    shippingpostalcode VARCHAR(80),
    shippingcountry VARCHAR(320),
    shippinglatitude DECIMAL(18),
    shippinglongitude DECIMAL(18),
    shippinggeocodeaccuracy VARCHAR(1020),
    shippingaddress VARCHAR(2060),
    phone VARCHAR(160),
    fax VARCHAR(160),
    accountnumber VARCHAR(160),
    website VARCHAR(1020),
    photourl VARCHAR(1020),
    sic VARCHAR(80),
    industry VARCHAR(1020),
    annualrevenue DECIMAL(15),
    numberofemployees DECIMAL(18),
    ownership VARCHAR(1020),
    tickersymbol VARCHAR(80),
    description VARCHAR(65535),
    rating VARCHAR(1020),
    site VARCHAR(320),
    ownerid VARCHAR(72) NOT NULL,
    createddate TIMESTAMP NOT NULL,
    createdbyid VARCHAR(72) NOT NULL,
    lastmodifieddate TIMESTAMP NOT NULL,
    lastmodifiedbyid VARCHAR(72) NOT NULL,
    systemmodstamp TIMESTAMP NOT NULL,
    lastactivitydate DATE,
    lastvieweddate TIMESTAMP,
    lastreferenceddate TIMESTAMP,
    ispersonaccount VARCHAR(72),
    billingstreet VARCHAR(1020),
    billingcity VARCHAR(320),
    billingstate VARCHAR(320),
    billingpostalcode VARCHAR(80),
    billingcountry VARCHAR(320),
    billinglatitude DECIMAL(18),
    billinglongitude DECIMAL(18),
    billinggeocodeaccuracy VARCHAR(1020),
    billingaddress VARCHAR(2060),
    personotherstreet VARCHAR(1020),
    personothercity VARCHAR(320),
    personotherstate VARCHAR(320),
    personotherpostalcode VARCHAR(80),
    personothercountry VARCHAR(320),
    personotherlatitude DECIMAL(18),
    personotherlongitude DECIMAL(18),
    personothergeocodeaccuracy VARCHAR(1020),
    personotheraddress VARCHAR(2060),
    personmobilephone VARCHAR(160),
    personotherphone VARCHAR(160),
    personassistantphone VARCHAR(160),
    personemail VARCHAR(1024),
    persontitle VARCHAR(320),
    persondepartment VARCHAR(320),
    personassistantname VARCHAR(160),
    personleadsource VARCHAR(1020),
    personbirthdate DATE,
    personhasoptedoutofemail VARCHAR(20),
    personhasoptedoutoffax VARCHAR(20),
    persondonotcall VARCHAR(20),
    personlastcurequestdate TIMESTAMP,
    personlastcuupdatedate TIMESTAMP,
    personemailbouncedreason VARCHAR(1020),
    personemailbounceddate TIMESTAMP,
    personindividualid VARCHAR(72),
    personpronouns VARCHAR(1020),
    persongenderidentity VARCHAR(1020),
    jigsaw VARCHAR(80),
    jigsawcompanyid VARCHAR(80),
    accountsource VARCHAR(1020),
    sicdesc VARCHAR(320),
    gender__c VARCHAR(1020),
    number__c VARCHAR(48) NOT NULL,
    preferredshipmentservice__c VARCHAR(1020),
    accountcloseddate__c DATE,
    accountclosedreason__c VARCHAR(1020),
    shopcardbarcode__c VARCHAR(1020),
    personmailingaddress__c VARCHAR(2060),
    isemployee__c VARCHAR(20),
    isoptedinemalmagazine__c VARCHAR(20),
    emailmagazineunsubscribeddate__c DATE,
    emailmagazinesubscribeddate__c DATE,
    beautycatalogsendtype__c VARCHAR(1020),
    healthcatalogsendtype__c VARCHAR(1020),
    apparelcatalogsendtype__c VARCHAR(1020),
    medicinecatalogsendtype__c VARCHAR(1020),
    petcatalogsendtype__c VARCHAR(1020),
    faxpurchaseordersendtype__c VARCHAR(1020),
    lastnamekana__c VARCHAR(120),
    firstnamekana__c VARCHAR(120),
    rank__c VARCHAR(1020),
    source__c VARCHAR(1020),
    status__c VARCHAR(1020),
    memo__c VARCHAR(1020),
    memoforstore__c VARCHAR(1020),
    isdhccreditcardowner__c VARCHAR(20),
    age__c DECIMAL(18),
    isoptedindm__c VARCHAR(1020),
    isoptedincatalog__c VARCHAR(1020),
    isunmailablepostway__c VARCHAR(20),
    isunmailablepost__c VARCHAR(20),
    isstoporder__c VARCHAR(20),
    isordermonitoring__c VARCHAR(20),
    isrequiredcaution__c VARCHAR(20),
    guestorderonly__c VARCHAR(20),
    customernumber__c VARCHAR(48) NOT NULL,
    isoptedinsurvey__c VARCHAR(20),
    margedaccountid__c VARCHAR(48),
    rankexpirydate__c DATE,
    preferredcontactway__c VARCHAR(1020),
    lineminiappuserid__c VARCHAR(132),
    namekana__c VARCHAR(5200),
    birthdate__c DATE,
    emailmagazineoptedouturl__c VARCHAR(5200),
    personemail__c VARCHAR(1024),
    optinstoreemailmagazinestatus__c VARCHAR(1020),
    storeemailmagazineemail__c VARCHAR(320),
    storeemailmagazineoptedoutkey__c VARCHAR(1020),
    storeemailmagazineoptedouturl__c VARCHAR(5200),
    tonariwaid__c VARCHAR(256),
    storeemailmagazinestorecode__c VARCHAR(12),
    unmailablereason__c VARCHAR(1020),
    dwh_created_user VARCHAR(400),
    dwh_created_datetime TIMESTAMP,
    dwh_updated_user VARCHAR(400),
    dwh_updated_datetime TIMESTAMP
);
CREATE TABLE period_price_linkage (
    mdm_integration_management_cd DECIMAL(19) NOT NULL,
    tax_exc DECIMAL(10),
    tax_inc DECIMAL(10),
    tax DECIMAL(10),
    tax_rate DECIMAL(10),
    apply_start_date TIMESTAMP NOT NULL,
    mdm_integration_management_cd_nk DECIMAL(19),
    insert_date TIMESTAMP NOT NULL,
    insert_id VARCHAR(128) NOT NULL,
    modify_date TIMESTAMP,
    modify_id VARCHAR(128),
    dwh_created_user VARCHAR(400),
    dwh_created_datetime TIMESTAMP,
    dwh_updated_user VARCHAR(400),
    dwh_updated_datetime TIMESTAMP
);
CREATE TABLE product_linkage (
    mdm_integration_management_cd DECIMAL(19) NOT NULL,
    product_picture_id DECIMAL(10),
    product_no DECIMAL(10),
    mail_order_product_cd VARCHAR(40),
    store_sales_product_cd VARCHAR(40),
    warehouse_management_cd VARCHAR(40),
    jan VARCHAR(52),
    jan_issue_flg VARCHAR(4),
    main_product_no DECIMAL(10),
    core_product_name VARCHAR(800),
    web_product_name VARCHAR(800),
    product_name VARCHAR(800),
    registration_name VARCHAR(800),
    law_cat_cd VARCHAR(12),
    product_segment VARCHAR(20),
    business_segment VARCHAR(20),
    product_cat VARCHAR(20),
    product_series VARCHAR(20),
    sale_start_date TIMESTAMP,
    period_set_sales_channel_1 VARCHAR(8),
    sales_channel_1_sale_start_date TIMESTAMP,
    sales_channel_1_sale_end_date TIMESTAMP,
    period_set_sales_channel_2 VARCHAR(8),
    sales_channel_2_sale_start_date TIMESTAMP,
    sales_channel_2_sale_end_date TIMESTAMP,
    period_set_sales_channel_3 VARCHAR(8),
    sales_channel_3_sale_start_date TIMESTAMP,
    sales_channel_3_sale_end_date TIMESTAMP,
    sale_status VARCHAR(4),
    lgroup VARCHAR(8),
    mgroup VARCHAR(8),
    sgroup VARCHAR(8),
    dgroup VARCHAR(8),
    product_type VARCHAR(8),
    core_department VARCHAR(8),
    accountin_pattern_gb VARCHAR(8),
    material VARCHAR(4),
    preferential_product_flg VARCHAR(4),
    set_product_flg VARCHAR(4),
    set_composition_flg VARCHAR(4),
    company_sales_buy_flg VARCHAR(4),
    emprate_pms_flg VARCHAR(4),
    age_limit_cd VARCHAR(4),
    store_po_gb VARCHAR(4),
    web VARCHAR(4),
    callcenter VARCHAR(4),
    before_renewal_product_no VARCHAR(800),
    dep VARCHAR(24),
    representative_product_cd VARCHAR(40),
    order_per_order_max DECIMAL(10),
    buttobi_subsc_bundle_yn VARCHAR(4),
    return_yn VARCHAR(4),
    exch_yn VARCHAR(4),
    lot_management_target_product VARCHAR(4),
    reduction_base VARCHAR(80),
    depth DECIMAL(10),
    width DECIMAL(10),
    height DECIMAL(10),
    trade_cnt DECIMAL(10),
    weight DECIMAL(7,2),
    outerbox_depth DECIMAL(10),
    outerbox_width DECIMAL(10),
    outerbox_height DECIMAL(10),
    outerbox_weight DECIMAL(7,2),
    case_per_include_cnt DECIMAL(10),
    insertion_depth DECIMAL(10),
    insertion_width DECIMAL(10),
    insertion_height DECIMAL(10),
    palette_stack_cnt_face DECIMAL(10),
    palette_stack_cnt_lavel DECIMAL(10),
    contents VARCHAR(24),
    nekoposu_volume_rate DECIMAL(10),
    outside_home_volume_rate DECIMAL(10),
    color_name VARCHAR(160),
    color_cd VARCHAR(12),
    original_color_cd VARCHAR(240),
    size_name VARCHAR(160),
    size_cd VARCHAR(12),
    shape_name VARCHAR(160),
    shape_cd VARCHAR(12),
    season VARCHAR(60),
    unit_cd VARCHAR(12),
    buy_send_reservation_flg VARCHAR(4),
    delivery_notice_undisplay_flg VARCHAR(4),
    airmail_rack_yn VARCHAR(4),
    mail_delivery_flg VARCHAR(4),
    outside_home_receive_service_flg VARCHAR(4),
    out_indicate_warehouse VARCHAR(12),
    warehouse_assembly_set_product_flg VARCHAR(4),
    sagawa_yn_flg VARCHAR(4),
    folding_flg VARCHAR(4),
    vender VARCHAR(24),
    use_point_cnt DECIMAL(10),
    composition_oms_link_flg VARCHAR(4),
    mdm_integration_management_cd_nk DECIMAL(19),
    insert_date TIMESTAMP NOT NULL,
    insert_id VARCHAR(128) NOT NULL,
    modify_date TIMESTAMP,
    modify_id VARCHAR(128),
    dwh_created_user VARCHAR(400),
    dwh_created_datetime TIMESTAMP,
    dwh_updated_user VARCHAR(400),
    dwh_updated_datetime TIMESTAMP
);
CREATE TABLE regular_sale_cont_header (
    regular_contract_no VARCHAR(56) NOT NULL,
    shop_code VARCHAR(64) NOT NULL,
    regular_sale_cont_datetime TIMESTAMP NOT NULL,
    customer_code VARCHAR(64) NOT NULL,
    neo_customer_no VARCHAR(48),
    payment_method_no DECIMAL(8) NOT NULL,
    address_no DECIMAL(8),
    regular_sale_cont_status DECIMAL(1) NOT NULL,
    next_delivery_request_date DATE,
    external_order_no VARCHAR(200),
    order_user_code DECIMAL(38),
    regular_update_datetime TIMESTAMP,
    change_user_code DECIMAL(38),
    regular_update_reason_kbn VARCHAR(8),
    otodoke_hope_time_kbn VARCHAR(8),
    marketing_channel VARCHAR(8),
    delivery_type_no DECIMAL(8),
    shipping_method_flg DECIMAL(1) NOT NULL,
    ext_payment_method_type VARCHAR(8) NOT NULL,
    card_brand VARCHAR(8),
    credit_card_kanri_no VARCHAR(48),
    credit_card_kanri_detail_no VARCHAR(16),
    credit_card_no VARCHAR(200),
    credit_card_meigi VARCHAR(600),
    credit_card_valid_year VARCHAR(16),
    credit_card_valid_month VARCHAR(8),
    credit_card_pay_count VARCHAR(8),
    amzn_charge_permission_id VARCHAR(65535),
    bill_address_kbn VARCHAR(4) NOT NULL,
    bill_print_otodoke_id VARCHAR(40),
    o_name_disp_kbn VARCHAR(4) NOT NULL,
    delivery_note_flg DECIMAL(1) NOT NULL,
    include_flg DECIMAL(1) NOT NULL,
    receipt_flg DECIMAL(1) NOT NULL,
    receipt_to VARCHAR(200),
    receipt_detail VARCHAR(200),
    first_shipping_date DATE,
    lastest_shipping_date DATE,
    first_delivery_date DATE,
    lastest_delivery_date DATE,
    regular_stop_date DATE,
    regular_stop_reason_kbn VARCHAR(8),
    regular_hold_date DATE,
    regular_hold_clear_date DATE,
    regular_kaiji DECIMAL(5) NOT NULL,
    shipped_regular_count DECIMAL(3) NOT NULL,
    delivery_memo VARCHAR(80),
    regular_hold_reason_kbn VARCHAR(8),
    niyose_flg DECIMAL(1) NOT NULL,
    orm_rowid DECIMAL(38) NOT NULL,
    created_user VARCHAR(400) NOT NULL,
    created_datetime TIMESTAMP NOT NULL,
    updated_user VARCHAR(400) NOT NULL,
    updated_datetime TIMESTAMP NOT NULL,
    dwh_created_user VARCHAR(400),
    dwh_created_datetime TIMESTAMP,
    dwh_updated_user VARCHAR(400),
    dwh_updated_datetime TIMESTAMP
);
CREATE TABLE regular_sale_cont_detail (
    regular_contract_no VARCHAR(56) NOT NULL,
    regular_contract_detail_no DECIMAL(3) NOT NULL,
    shop_code VARCHAR(64) NOT NULL,
    sku_code VARCHAR(96) NOT NULL,
    commodity_code VARCHAR(64) NOT NULL,
    contract_amount DECIMAL(8) NOT NULL,
    commodity_name VARCHAR(400) NOT NULL,
    commodity_subcategory_code VARCHAR(64),
    commodity_subcategory_code_name VARCHAR(200),
    baitai_code VARCHAR(40) NOT NULL,
    regular_cycle_delivery_kbn VARCHAR(4) NOT NULL,
    regular_cycle_kijun_date DATE,
    regular_kind VARCHAR(4) NOT NULL,
    regular_cycle_day_int VARCHAR(8),
    regular_cycle_day DECIMAL(2),
    regular_cycle_mon_interval DECIMAL(2),
    regular_cycle_mon_interval_day DECIMAL(2),
    regular_cycle_week_num DECIMAL(2),
    regular_cycle_week_kbn VARCHAR(4),
    regular_cycle_week_mon VARCHAR(4),
    regular_cycle_week_tue VARCHAR(4),
    regular_cycle_week_wed VARCHAR(4),
    regular_cycle_week_thu VARCHAR(4),
    regular_cycle_week_fri VARCHAR(4),
    regular_cycle_week_sat VARCHAR(4),
    regular_cycle_week_sun VARCHAR(4),
    regular_cycle_week_hol VARCHAR(4),
    cycle_disp_name VARCHAR(4000),
    next_shipping_plan_date DATE,
    next_shipping_date DATE NOT NULL,
    next_delivery_plan_date DATE,
    next_delivery_date DATE,
    lastest_delivery_date DATE,
    regular_kaiji DECIMAL(5) NOT NULL,
    shipped_regular_count DECIMAL(3) NOT NULL,
    regular_sale_stop_from DECIMAL(5),
    regular_sale_stop_to DECIMAL(5),
    hasso_souko_cd VARCHAR(24),
    shipping_area VARCHAR(8),
    regular_check_memo VARCHAR(4000),
    regular_memo_hold_flg DECIMAL(1) NOT NULL,
    souko_shiji VARCHAR(160),
    next_regular_sale_stop_status VARCHAR(4) NOT NULL,
    regular_stop_reason_kbn VARCHAR(8),
    orm_rowid DECIMAL(38) NOT NULL,
    created_user VARCHAR(400) NOT NULL,
    created_datetime TIMESTAMP NOT NULL,
    updated_user VARCHAR(400) NOT NULL,
    updated_datetime TIMESTAMP NOT NULL,
    dwh_created_user VARCHAR(400),
    dwh_created_datetime TIMESTAMP,
    dwh_updated_user VARCHAR(400),
    dwh_updated_datetime TIMESTAMP
);
CREATE TABLE shipping_header (
    shipping_no VARCHAR(64) NOT NULL,
    order_no VARCHAR(64) NOT NULL,
    shop_code VARCHAR(64) NOT NULL,
    customer_code VARCHAR(64),
    neo_customer_no VARCHAR(48),
    address_no DECIMAL(8) NOT NULL,
    address_last_name VARCHAR(80) NOT NULL,
    address_first_name VARCHAR(80),
    address_last_name_kana VARCHAR(160) NOT NULL,
    address_first_name_kana VARCHAR(160),
    postal_code VARCHAR(28) NOT NULL,
    prefecture_code VARCHAR(8) NOT NULL,
    address1 VARCHAR(16) NOT NULL,
    address2 VARCHAR(200) NOT NULL,
    address3 VARCHAR(1020) NOT NULL,
    address4 VARCHAR(400),
    corporation_post_name VARCHAR(160),
    phone_number VARCHAR(64),
    delivery_remark VARCHAR(2000),
    acquired_point DECIMAL(9),
    delivery_slip_no VARCHAR(120),
    shipping_charge DECIMAL(8) NOT NULL,
    shipping_charge_tax_type DECIMAL(1) NOT NULL,
    shipping_charge_tax_group_code VARCHAR(32) NOT NULL,
    shipping_charge_tax_no DECIMAL(3) NOT NULL,
    shipping_charge_tax_rate DECIMAL(3) NOT NULL,
    shipping_charge_tax DECIMAL(10) NOT NULL,
    delivery_type_no DECIMAL(8) NOT NULL,
    shipping_method VARCHAR(8) NOT NULL,
    delivery_type_name VARCHAR(160),
    delivery_appointed_date DATE,
    delivery_appointed_time_start DECIMAL(2),
    delivery_appointed_time_end DECIMAL(2),
    arrival_date DATE,
    arrival_time_start DECIMAL(2),
    arrival_time_end DECIMAL(2),
    fixed_sales_status DECIMAL(1) NOT NULL,
    shipping_status DECIMAL(1) NOT NULL,
    shipping_direct_date DATE,
    shipping_date DATE,
    original_shipping_no VARCHAR(64),
    return_item_date DATE,
    return_item_type DECIMAL(1),
    shipping_area VARCHAR(8) NOT NULL,
    delivery_note_flg DECIMAL(1) NOT NULL,
    include_flg DECIMAL(1) NOT NULL,
    delivery_memo VARCHAR(80),
    shipping_bill_price DECIMAL(10) NOT NULL,
    shipping_dokon_shiji_code VARCHAR(320),
    o_name_disp_kbn VARCHAR(4) NOT NULL,
    member_stage VARCHAR(8),
    possession_point DECIMAL(10),
    sales_recording_date DATE,
    prod_pack_type VARCHAR(8),
    shipping_method_kbn VARCHAR(8),
    box_code VARCHAR(8),
    delivery_note_message VARCHAR(65535),
    orm_rowid DECIMAL(38) NOT NULL,
    created_user VARCHAR(400) NOT NULL,
    created_datetime TIMESTAMP NOT NULL,
    updated_user VARCHAR(400) NOT NULL,
    updated_datetime TIMESTAMP NOT NULL,
    dwh_created_user VARCHAR(400),
    dwh_created_datetime TIMESTAMP,
    dwh_updated_user VARCHAR(400),
    dwh_updated_datetime TIMESTAMP
);
CREATE TABLE pos_value_discount_relation (
    shop_cd VARCHAR(48) NOT NULL,
    register_num DECIMAL(10) NOT NULL,
    business_date DATE NOT NULL,
    receipt_num DECIMAL(10) NOT NULL,
    line_num DECIMAL(10) NOT NULL,
    target_line_num DECIMAL(10) NOT NULL,
    discount_cd VARCHAR(24) NOT NULL,
    discount_kind DECIMAL(5) NOT NULL,
    disc_rate DECIMAL(5) NOT NULL,
    minus_amount DECIMAL(10) NOT NULL,
    tax_inclusive DECIMAL(10),
    ins_biz_date DATE,
    upd_biz_date DATE,
    ins_date TIMESTAMP NOT NULL,
    upd_date TIMESTAMP NOT NULL,
    ins_user_id VARCHAR(80) NOT NULL,
    upd_user_id VARCHAR(80) NOT NULL,
    ins_pgm_id VARCHAR(80) NOT NULL,
    upd_pgm_id VARCHAR(80) NOT NULL,
    dwh_created_user VARCHAR(400),
    dwh_created_datetime TIMESTAMP,
    dwh_updated_user VARCHAR(400),
    dwh_updated_datetime TIMESTAMP
);
CREATE TABLE stock_month (
    business_year_month VARCHAR(28) NOT NULL,
    shop_cd VARCHAR(40) NOT NULL,
    item_cd VARCHAR(120) NOT NULL,
    brand_cd VARCHAR(20) NOT NULL,
    proper_item_kind VARCHAR(8) NOT NULL,
    retail_price DECIMAL(10) NOT NULL,
    seling_price DECIMAL(10) NOT NULL,
    good_stock_count DECIMAL(10) NOT NULL,
    defective_stock_count DECIMAL(10) NOT NULL,
    pending_stock_count DECIMAL(10) NOT NULL,
    reserving_stock_count DECIMAL(10) NOT NULL,
    take_out_stock_count DECIMAL(10) NOT NULL,
    shipment_stock_count DECIMAL(10) NOT NULL,
    reservation_stock_count DECIMAL(10) NOT NULL,
    reserve_count1 DECIMAL(10),
    reserve_count2 DECIMAL(10),
    reserve_count3 DECIMAL(10),
    reserve_count4 DECIMAL(10),
    reserve_count5 DECIMAL(10),
    ins_biz_date DATE,
    upd_biz_date DATE,
    ins_date TIMESTAMP NOT NULL,
    upd_date TIMESTAMP NOT NULL,
    ins_user_id VARCHAR(80) NOT NULL,
    upd_user_id VARCHAR(80) NOT NULL,
    ins_pgm_id VARCHAR(80) NOT NULL,
    upd_pgm_id VARCHAR(80) NOT NULL,
    dwh_created_user VARCHAR(400),
    dwh_created_datetime TIMESTAMP,
    dwh_updated_user VARCHAR(400),
    dwh_updated_datetime TIMESTAMP
);
CREATE TABLE secure_move (
    corp_cd VARCHAR(16),
    stock_io_type VARCHAR(4),
    move_date DATE,
    center_code VARCHAR(16),
    stock_group_code VARCHAR(16),
    shop_code_swh VARCHAR(64),
    sh_control_number VARCHAR(40),
    move_quantity DECIMAL(7),
    move_center_code VARCHAR(16),
    move_stock_group_code VARCHAR(16),
    move_shop_code_swh VARCHAR(64),
    upd_user_id VARCHAR(120),
    data_date TIMESTAMP,
    dwh_created_user VARCHAR(400),
    dwh_created_datetime TIMESTAMP,
    dwh_updated_user VARCHAR(400),
    dwh_updated_datetime TIMESTAMP
);
CREATE TABLE stock_list (
    corp_cd VARCHAR(16),
    data_date TIMESTAMP,
    center_code VARCHAR(16),
    stock_group_code VARCHAR(16),
    shipping_code VARCHAR(64),
    product_cd VARCHAR(40),
    first_day_stock_count DECIMAL(7),
    arrival_quantity DECIMAL(7),
    shipping_quantity DECIMAL(7),
    arrival_quantity_irregular DECIMAL(7),
    shipping_quantity_irregular DECIMAL(7),
    carryover_stock_count DECIMAL(7),
    dwh_created_user VARCHAR(400),
    dwh_created_datetime TIMESTAMP,
    dwh_updated_user VARCHAR(400),
    dwh_updated_datetime TIMESTAMP
);
