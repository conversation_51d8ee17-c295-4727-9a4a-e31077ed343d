AWSTemplateFormatVersion: "2010-09-09"

Resources:
  StepFunctionsLogGroup:
    Type: AWS::Logs::LogGroup
    Properties:
      LogGroupName: !Sub /aws/vendedlogs/states/DWH-JN-CDP-CO-001

  MyStateMachine:
    Type: "AWS::StepFunctions::StateMachine"
    Properties:
      StateMachineName: "DWH-JN-CDP-CO-001"
      RoleArn: "arn:aws:iam::879381279381:role/role-prd-dlpf-sfn-dwh"
      LoggingConfiguration:
        Level: ALL
        IncludeExecutionData: true
        Destinations:
          - CloudWatchLogsLogGroup:
              LogGroupArn: !GetAtt StepFunctionsLogGroup.Arn
      DefinitionString:
        Fn::Sub: |
          {
            "StartAt": "Run Glue Job",
            "States": {
              "Run Glue Job": {
                "Type": "Task",
                "Resource": "arn:aws:states:::glue:startJobRun.sync",
                "Parameters": {
                  "JobName": "DWH_JB_DB_TO_FILE",
                  "Arguments": {
                    "--IF_IDS": "\"[\\\"IF-CDP-CO-001\\\",\\\"IF-CDP-CO-002\\\",\\\"IF-CDP-CO-003\\\"]\""
                  }
                },
                "End": true
              }
            }
          }
