AWSTemplateFormatVersion: '2010-09-09'
Resources:
  StepFunctionsLogGroup:
    Type: AWS::Logs::LogGroup
    Properties:
      LogGroupName: !Sub /aws/vendedlogs/states/DWH-JN-CTL-FA-001

  MyStateMachine:
    Type: "AWS::StepFunctions::StateMachine"
    Properties:
      StateMachineName: "DWH-JN-CTL-FA-001"
      RoleArn: "arn:aws:iam::879381279381:role/role-prd-dlpf-sfn-ctl-dwh"
      LoggingConfiguration:
        Level: ALL
        IncludeExecutionData: true
        Destinations:
          - CloudWatchLogsLogGroup:
              LogGroupArn: !GetAtt StepFunctionsLogGroup.Arn
      DefinitionString:
        Fn::Sub: |
          {
            "Comment": "DWH-JN-CTL-FA-001",
            "StartAt": "Choice01",
            "States": {
              "Choice01": {
                "Type": "Choice",
                "Choices": [
                  {
                    "And": [
                      {
                        "Variable": "$.startState",
                        "IsPresent": true
                      },
                      {
                        "Variable": "$.startState",
                        "StringMatches": "DB_TO_FILE"
                      }
                    ],
                    "Next": "DWH-JN-CDP-FA-001"
                  }
                ],
                "Default": "DWH-JN-CRM-FA-001"
              },
              "DWH-JN-CRM-FA-001": {
                "Type": "Task",
                "Resource": "arn:aws:states:::states:startExecution.sync",
                "Parameters": {
                  "StateMachineArn": "arn:aws:states:ap-northeast-1:879381279381:stateMachine:DWH-JN-CRM-FA-001"
                },
                "Next": "DWH-JN-CDP-FA-001"
              },
              "DWH-JN-CDP-FA-001": {
                "Type": "Task",
                "Resource": "arn:aws:states:::states:startExecution.sync",
                "Parameters": {
                  "StateMachineArn": "arn:aws:states:ap-northeast-1:879381279381:stateMachine:DWH-JN-CDP-FA-001"
                },
                "End": true
              }
            }
          }