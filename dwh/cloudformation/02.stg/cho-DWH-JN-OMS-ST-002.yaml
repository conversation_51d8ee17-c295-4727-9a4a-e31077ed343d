AWSTemplateFormatVersion: '2010-09-09'
Resources:
  StepFunctionsLogGroup:
    Type: AWS::Logs::LogGroup
    Properties:
      LogGroupName: !Sub /aws/vendedlogs/states/DWH-JN-OMS-ST-002

  MyStateMachine:
    Type: "AWS::StepFunctions::StateMachine"
    Properties:
      StateMachineName: "DWH-JN-OMS-ST-002"
      RoleArn: "arn:aws:iam::869935081854:role/role-stg-dlpf-sfn-dwh"
      LoggingConfiguration:
        Level: ALL
        IncludeExecutionData: true
        Destinations:
          - CloudWatchLogsLogGroup:
              LogGroupArn: !GetAtt StepFunctionsLogGroup.Arn
      DefinitionString:
        Fn::Sub: |
          {
            "QueryLanguage": "JSONata",
            "Comment": "A description of my state machine",
            "StartAt": "counter",
            "States": {
              "counter": {
                "Type": "Pass",
                "Next": "ファイル存在チェック",
                "Assign": {
                  "retry_count": 0
                }
              },
              "ファイル存在チェック": {
                "Type": "Task",
                "Resource": "arn:aws:states:::lambda:invoke",
                "Output": "{% $states.result.Payload %}",
                "Arguments": {
                  "FunctionName": "arn:aws:lambda:ap-northeast-1:869935081854:function:DWH_LM_S3_FILE_EXISTS_CHECK",
                  "Payload": "{% {\"if_ids\": [\"IF-OMS-ST-002\"], \"jobnet_id\": \"DWH-JN-OMS-ST-002\"} %}"
                },
                "Assign": {
                  "retry_count": "{% $retry_count + 1 %}"
                },
                "Retry": [
                  {
                    "ErrorEquals": [
                      "Lambda.ServiceException",
                      "Lambda.AWSLambdaException",
                      "Lambda.SdkClientException",
                      "Lambda.TooManyRequestsException"
                    ],
                    "IntervalSeconds": 1,
                    "MaxAttempts": 3,
                    "BackoffRate": 2,
                    "JitterStrategy": "FULL"
                  }
                ],
                "Next": "存在判定"
              },
              "存在判定": {
                "Type": "Choice",
                "Default": "再実行",
                "Choices": [
                  {
                    "Next": "ファイル取り込む",
                    "Condition": "{% $states.input.status = 0 %}",
                    "Output": "{% $states.input%}"
                  },
                  {
                    "Condition": "{% $retry_count = {{resolve:ssm:DWH_RETRY_COUNT}} %}",
                    "Next": "ファイル揃えていない"
                  }
                ]
              },
              "ファイル取り込む": {
                "Type": "Task",
                "Resource": "arn:aws:states:::glue:startJobRun.sync",
                "Arguments": {
                  "JobName": "DWH_JB_FILE_LOAD",
                  "Arguments": {
                    "--if_ids": "{% $string($states.input.if_ids) %}",
                    "--if_files": "{% $string($states.input.file_path) %}"
                  }
                },
                "End": true
              },
              "ファイル揃えていない": {
                "Type": "Fail"
              },
              "再実行": {
                "Type": "Wait",
                "Next": "ファイル存在チェック",
                "Seconds": {{resolve:ssm:DWH_RETRY_TIMEOUT}}
              }
            }
          }

