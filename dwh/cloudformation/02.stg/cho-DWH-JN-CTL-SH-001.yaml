AWSTemplateFormatVersion: '2010-09-09'
Resources:
  StepFunctionsLogGroup:
    Type: AWS::Logs::LogGroup
    Properties:
      LogGroupName: !Sub /aws/vendedlogs/states/DWH-JN-CTL-SH-001

  MyStateMachine:
    Type: "AWS::StepFunctions::StateMachine"
    Properties:
      StateMachineName: "DWH-JN-CTL-SH-001"
      RoleArn: "arn:aws:iam::869935081854:role/role-stg-dlpf-sfn-ctl-dwh"
      LoggingConfiguration:
        Level: ALL
        IncludeExecutionData: true
        Destinations:
          - CloudWatchLogsLogGroup:
              LogGroupArn: !GetAtt StepFunctionsLogGroup.Arn
      DefinitionString:
        Fn::Sub: |
          {
            "Comment": "DWH-JN-CTL-SH-001",
            "StartAt": "Choice01",
            "States": {
              "Choice01": {
                "Type": "Choice",
                "Choices": [
                  {
                    "And": [
                      {
                        "Variable": "$.startState",
                        "IsPresent": true
                      },
                      {
                        "Variable": "$.startState",
                        "StringMatches": "DB_TO_FILE"
                      }
                    ],
                    "Next": "DWH-JN-CDP-SH-001"
                  },
                  {
                    "And": [
                      {
                        "Variable": "$.startState",
                        "IsPresent": true
                      },
                      {
                        "Variable": "$.startState",
                        "StringMatches": "COPY_TO_STORAGE"
                      }
                    ],
                    "Next": "DWH-JN-DWH-SH-001"
                  },
                  {
                    "And": [
                      {
                        "Variable": "$.startState",
                        "IsPresent": true
                      },
                      {
                        "Variable": "$.startState",
                        "StringMatches": "COPY_TO_ANALYSIS"
                      }
                    ],
                    "Next": "DWH-JN-DWH-SH-002"
                  }
                ],
                "Default": "DWH-JN-POS-SH-001"
              },
              "DWH-JN-POS-SH-001": {
                "Type": "Task",
                "Resource": "arn:aws:states:::states:startExecution.sync",
                "Parameters": {
                  "StateMachineArn": "arn:aws:states:ap-northeast-1:869935081854:stateMachine:DWH-JN-POS-SH-001"
                },
                "Next": "DWH-JN-CDP-SH-001"
              },
              "DWH-JN-CDP-SH-001": {
                "Type": "Task",
                "Resource": "arn:aws:states:::states:startExecution.sync",
                "Parameters": {
                  "StateMachineArn": "arn:aws:states:ap-northeast-1:869935081854:stateMachine:DWH-JN-CDP-SH-001"
                },
                "Next": "DWH-JN-DWH-SH-001"
              },
              "DWH-JN-DWH-SH-001": {
                "Type": "Task",
                "Resource": "arn:aws:states:::states:startExecution.sync",
                "Parameters": {
                  "StateMachineArn": "arn:aws:states:ap-northeast-1:869935081854:stateMachine:DWH-JN-DWH-SH-001"
                },
                "Next": "DWH-JN-DWH-SH-002"
              },
              "DWH-JN-DWH-SH-002": {
                "Type": "Task",
                "Resource": "arn:aws:states:::states:startExecution.sync",
                "Parameters": {
                  "StateMachineArn": "arn:aws:states:ap-northeast-1:869935081854:stateMachine:DWH-JN-DWH-SH-002"
                },
                "End": true
              }
            }
          }