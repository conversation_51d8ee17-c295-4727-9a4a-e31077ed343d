AWSTemplateFormatVersion: '2010-09-09'
Resources:
  GlueJob:
    Type: AWS::Glue::Job
    Properties:
      Name: DWH_JB_DB_DATA_COPY
      Description: "DWH内データ取り込み"
      Role: "AWSGlueServiceRole-stg-dlpf-glue-job-dwh"
      GlueVersion: "4.0"
      Command:
        Name: "glueetl"
        ScriptLocation: "s3://aws-glue-assets-869935081854-ap-northeast-1/scripts/dwh/DWH_JB_DB_DATA_COPY.py"
        PythonVersion: "3"
      DefaultArguments:
        "--additional-python-modules": "redshift_connector==2.1.5"
        "--enable-metrics": "true"
        "--enable-observability-metrics": "true"
        "--enable-continuous-cloudwatch-log": "true"
        "--enable-glue-datacatalog": "true"
        "--TempDir": "s3://aws-glue-assets-869935081854-ap-northeast-1/temporary/"
        "--spark-event-logs-path": "s3://aws-glue-assets-869935081854-ap-northeast-1/sparkHistoryLogs/"
        "--extra-files": "s3://aws-glue-assets-869935081854-ap-northeast-1/scripts/dwh/"
        "--enable-spark-ui": "true"
      Timeout: 2880
      WorkerType: "G.1X"
      NumberOfWorkers: 10
      Connections:
        Connections:
          - "gluecon-mf-gdpf-stg-tyo-a-redshift-001"
      ExecutionProperty:
        MaxConcurrentRuns: 100
