AWSTemplateFormatVersion: "2010-09-09"

Resources:
  StepFunctionsLogGroup:
    Type: AWS::Logs::LogGroup
    Properties:
      LogGroupName: !Sub /aws/vendedlogs/states/DWH-JN-DWH-SH-002

  MyStateMachine:
    Type: "AWS::StepFunctions::StateMachine"
    Properties:
      StateMachineName: "DWH-JN-DWH-SH-002"
      RoleArn: "arn:aws:iam::869935081854:role/role-stg-dlpf-sfn-dwh"
      LoggingConfiguration:
        Level: ALL
        IncludeExecutionData: true
        Destinations:
          - CloudWatchLogsLogGroup:
              LogGroupArn: !GetAtt StepFunctionsLogGroup.Arn
      DefinitionString:
        Fn::Sub: |
          {
            "StartAt": "Run Glue Job",
            "States": {
              "Run Glue Job": {
                "Type": "Task",
                "Resource": "arn:aws:states:::glue:startJobRun.sync",
                "Parameters": {
                  "JobName": "DWH_JB_DB_DATA_COPY",
                  "Arguments": {
                    "--JOBNET_ID": "DWH-JN-DWH-SH-002"
                  }
                },
                "End": true
              }
            }
          }
