AWSTemplateFormatVersion: '2010-09-09'
Resources:
  StepFunctionsLogGroup:
    Type: AWS::Logs::LogGroup
    Properties:
      LogGroupName: !Sub /aws/vendedlogs/states/DWH-JN-CTL-PR-001

  MyStateMachine:
    Type: "AWS::StepFunctions::StateMachine"
    Properties:
      StateMachineName: "DWH-JN-CTL-PR-001"
      RoleArn: "arn:aws:iam::869935081854:role/role-stg-dlpf-sfn-ctl-dwh"
      LoggingConfiguration:
        Level: ALL
        IncludeExecutionData: true
        Destinations:
          - CloudWatchLogsLogGroup:
              LogGroupArn: !GetAtt StepFunctionsLogGroup.Arn
      DefinitionString:
        Fn::Sub: |
          {
            "Comment": "DWH-JN-CTL-PR-001",
            "StartAt": "Choice01",
            "States": {
              "Choice01": {
                "Type": "Choice",
                "Choices": [
                  {
                    "And": [
                      {
                        "Variable": "$.startState",
                        "IsPresent": true
                      },
                      {
                        "Variable": "$.startState",
                        "StringMatches": "DB_TO_FILE"
                      }
                    ],
                    "Next": "DWH-JN-CDP-PR-001"
                  }
                ],
                "Default": "DWH-JN-MDM-PR-001"
              },
              "DWH-JN-MDM-PR-001": {
                "Type": "Task",
                "Resource": "arn:aws:states:::states:startExecution.sync",
                "Parameters": {
                  "StateMachineArn": "arn:aws:states:ap-northeast-1:869935081854:stateMachine:DWH-JN-MDM-PR-001"
                },
                "Next": "DWH-JN-CDP-PR-001"
              },
              "DWH-JN-CDP-PR-001": {
                "Type": "Task",
                "Resource": "arn:aws:states:::states:startExecution.sync",
                "Parameters": {
                  "StateMachineArn": "arn:aws:states:ap-northeast-1:869935081854:stateMachine:DWH-JN-CDP-PR-001"
                },
                "End": true
              }
            }
          }