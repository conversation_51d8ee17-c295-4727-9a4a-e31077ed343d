AWSTemplateFormatVersion: '2010-09-09'
Resources:
  DWHEBOMSSL001:
    Type: AWS::Events::Rule
    Properties:
      Name: DWH-EB-OMS-SL-001
      ScheduleExpression: cron(30 15 * * ? *)
      State: ENABLED
      EventBusName: default
      Targets:
        - Id: Id225284f8-466b-446e-a9ae-a762ae4f9012
          Arn:
            Fn::Sub: >-
              arn:aws:lambda:ap-northeast-1:886436956581:function:DWH_LM_DUPLICATE_EVENT_CHECK
          RoleArn: >-
            arn:aws:iam::886436956581:role/role-dev-dlpf-evbr-invoke-lambda
          InputTransformer:
            InputPathsMap:
              time: $.time
            InputTemplate: |-
              {
                  "time": <time>,
                  "jobnet_id": "DWH-JN-OMS-SL-001", 
                  "statemachine_arn": "arn:aws:states:ap-northeast-1:886436956581:stateMachine:DWH-JN-OMS-SL-001"
              }

  DWHEBPOSSL001:
    Type: AWS::Events::Rule
    Properties:
      Name: DWH-EB-POS-SL-001
      ScheduleExpression: cron(30 15 * * ? *)
      State: ENABLED
      EventBusName: default
      Targets:
        - Id: Id225284f8-466b-446e-a9ae-a762ae4f9012
          Arn:
            Fn::Sub: >-
              arn:aws:lambda:ap-northeast-1:886436956581:function:DWH_LM_DUPLICATE_EVENT_CHECK
          RoleArn: >-
            arn:aws:iam::886436956581:role/role-dev-dlpf-evbr-invoke-lambda
          InputTransformer:
            InputPathsMap:
              time: $.time
            InputTemplate: |-
              {
                  "time": <time>,
                  "jobnet_id": "DWH-JN-POS-SL-001", 
                  "statemachine_arn": "arn:aws:states:ap-northeast-1:886436956581:stateMachine:DWH-JN-POS-SL-001"
              }

  DWHEBCTLME001:
    Type: AWS::Events::Rule
    Properties:
      Name: DWH-EB-CTL-ME-001
      ScheduleExpression: cron(30 15 * * ? *)
      State: ENABLED
      EventBusName: default
      Targets:
        - Id: Id225284f8-466b-446e-a9ae-a762ae4f9012
          Arn:
            Fn::Sub: >-
              arn:aws:lambda:ap-northeast-1:886436956581:function:DWH_LM_DUPLICATE_EVENT_CHECK
          RoleArn: >-
            arn:aws:iam::886436956581:role/role-dev-dlpf-evbr-invoke-lambda
          InputTransformer:
            InputPathsMap:
              time: $.time
            InputTemplate: |-
              {
                  "time": <time>,
                  "jobnet_id": "DWH-JN-CTL-ME-001", 
                  "statemachine_arn": "arn:aws:states:ap-northeast-1:886436956581:stateMachine:DWH-JN-CTL-ME-001"
              }

  DWHEBCTLME002:
    Type: AWS::Events::Rule
    Properties:
      Name: DWH-EB-CTL-ME-002
      ScheduleExpression: cron(30 15 * * ? *)
      State: ENABLED
      EventBusName: default
      Targets:
        - Id: Id225284f8-466b-446e-a9ae-a762ae4f9012
          Arn:
            Fn::Sub: >-
              arn:aws:lambda:ap-northeast-1:886436956581:function:DWH_LM_DUPLICATE_EVENT_CHECK
          RoleArn: >-
            arn:aws:iam::886436956581:role/role-dev-dlpf-evbr-invoke-lambda
          InputTransformer:
            InputPathsMap:
              time: $.time
            InputTemplate: |-
              {
                  "time": <time>,
                  "jobnet_id": "DWH-JN-CTL-ME-002", 
                  "statemachine_arn": "arn:aws:states:ap-northeast-1:886436956581:stateMachine:DWH-JN-CTL-ME-002"
              }

  DWHEBCTLCP001:
    Type: AWS::Events::Rule
    Properties:
      Name: DWH-EB-CTL-CP-001
      ScheduleExpression: cron(30 15 * * ? *)
      State: ENABLED
      EventBusName: default
      Targets:
        - Id: Id225284f8-466b-446e-a9ae-a762ae4f9012
          Arn:
            Fn::Sub: >-
              arn:aws:lambda:ap-northeast-1:886436956581:function:DWH_LM_DUPLICATE_EVENT_CHECK
          RoleArn: >-
            arn:aws:iam::886436956581:role/role-dev-dlpf-evbr-invoke-lambda
          InputTransformer:
            InputPathsMap:
              time: $.time
            InputTemplate: |-
              {
                  "time": <time>,
                  "jobnet_id": "DWH-JN-CTL-CP-001", 
                  "statemachine_arn": "arn:aws:states:ap-northeast-1:886436956581:stateMachine:DWH-JN-CTL-CP-001"
              }

  DWHEBOMSCP002:
    Type: AWS::Events::Rule
    Properties:
      Name: DWH-EB-OMS-CP-002
      ScheduleExpression: cron(30 15 * * ? *)
      State: ENABLED
      EventBusName: default
      Targets:
        - Id: Id225284f8-466b-446e-a9ae-a762ae4f9012
          Arn:
            Fn::Sub: >-
              arn:aws:lambda:ap-northeast-1:886436956581:function:DWH_LM_DUPLICATE_EVENT_CHECK
          RoleArn: >-
            arn:aws:iam::886436956581:role/role-dev-dlpf-evbr-invoke-lambda
          InputTransformer:
            InputPathsMap:
              time: $.time
            InputTemplate: |-
              {
                  "time": <time>,
                  "jobnet_id": "DWH-JN-OMS-CP-002", 
                  "statemachine_arn": "arn:aws:states:ap-northeast-1:886436956581:stateMachine:DWH-JN-OMS-CP-002"
              }

  DWHEBOMSCP003:
    Type: AWS::Events::Rule
    Properties:
      Name: DWH-EB-OMS-CP-003
      ScheduleExpression: cron(30 15 * * ? *)
      State: ENABLED
      EventBusName: default
      Targets:
        - Id: Id225284f8-466b-446e-a9ae-a762ae4f9012
          Arn:
            Fn::Sub: >-
              arn:aws:lambda:ap-northeast-1:886436956581:function:DWH_LM_DUPLICATE_EVENT_CHECK
          RoleArn: >-
            arn:aws:iam::886436956581:role/role-dev-dlpf-evbr-invoke-lambda
          InputTransformer:
            InputPathsMap:
              time: $.time
            InputTemplate: |-
              {
                  "time": <time>,
                  "jobnet_id": "DWH-JN-OMS-CP-003", 
                  "statemachine_arn": "arn:aws:states:ap-northeast-1:886436956581:stateMachine:DWH-JN-OMS-CP-003"
              }

  DWHEBOMSCP004:
    Type: AWS::Events::Rule
    Properties:
      Name: DWH-EB-OMS-CP-004
      ScheduleExpression: cron(30 15 * * ? *)
      State: ENABLED
      EventBusName: default
      Targets:
        - Id: Id225284f8-466b-446e-a9ae-a762ae4f9012
          Arn:
            Fn::Sub: >-
              arn:aws:lambda:ap-northeast-1:886436956581:function:DWH_LM_DUPLICATE_EVENT_CHECK
          RoleArn: >-
            arn:aws:iam::886436956581:role/role-dev-dlpf-evbr-invoke-lambda
          InputTransformer:
            InputPathsMap:
              time: $.time
            InputTemplate: |-
              {
                  "time": <time>,
                  "jobnet_id": "DWH-JN-OMS-CP-004", 
                  "statemachine_arn": "arn:aws:states:ap-northeast-1:886436956581:stateMachine:DWH-JN-OMS-CP-004"
              }

  DWHEBOMSCO001:
    Type: AWS::Events::Rule
    Properties:
      Name: DWH-EB-OMS-CO-001
      ScheduleExpression: cron(30 15 * * ? *)
      State: ENABLED
      EventBusName: default
      Targets:
        - Id: Id225284f8-466b-446e-a9ae-a762ae4f9012
          Arn:
            Fn::Sub: >-
              arn:aws:lambda:ap-northeast-1:886436956581:function:DWH_LM_DUPLICATE_EVENT_CHECK
          RoleArn: >-
            arn:aws:iam::886436956581:role/role-dev-dlpf-evbr-invoke-lambda
          InputTransformer:
            InputPathsMap:
              time: $.time
            InputTemplate: |-
              {
                  "time": <time>,
                  "jobnet_id": "DWH-JN-OMS-CO-001", 
                  "statemachine_arn": "arn:aws:states:ap-northeast-1:886436956581:stateMachine:DWH-JN-OMS-CO-001"
              }

  DWHEBCMNST001:
    Type: AWS::Events::Rule
    Properties:
      Name: DWH-EB-CMN-ST-001
      ScheduleExpression: cron(30 16 * * ? *)
      State: ENABLED
      EventBusName: default
      Targets:
        - Id: Id225284f8-466b-446e-a9ae-a762ae4f9012
          Arn:
            Fn::Sub: >-
              arn:aws:lambda:ap-northeast-1:886436956581:function:DWH_LM_DUPLICATE_EVENT_CHECK
          RoleArn: >-
            arn:aws:iam::886436956581:role/role-dev-dlpf-evbr-invoke-lambda
          InputTransformer:
            InputPathsMap:
              time: $.time
            InputTemplate: |-
              {
                  "time": <time>,
                  "jobnet_id": "DWH-JN-CMN-ST-001", 
                  "statemachine_arn": "arn:aws:states:ap-northeast-1:886436956581:stateMachine:DWH-JN-CMN-ST-001"
              }

  DWHEBOMSST001:
    Type: AWS::Events::Rule
    Properties:
      Name: DWH-EB-OMS-ST-001
      ScheduleExpression: cron(30 15 * * ? *)
      State: ENABLED
      EventBusName: default
      Targets:
        - Id: Id225284f8-466b-446e-a9ae-a762ae4f9012
          Arn:
            Fn::Sub: >-
              arn:aws:lambda:ap-northeast-1:886436956581:function:DWH_LM_DUPLICATE_EVENT_CHECK
          RoleArn: >-
            arn:aws:iam::886436956581:role/role-dev-dlpf-evbr-invoke-lambda
          InputTransformer:
            InputPathsMap:
              time: $.time
            InputTemplate: |-
              {
                  "time": <time>,
                  "jobnet_id": "DWH-JN-OMS-ST-001", 
                  "statemachine_arn": "arn:aws:states:ap-northeast-1:886436956581:stateMachine:DWH-JN-OMS-ST-001"
              }

  DWHEBOMSST002:
    Type: AWS::Events::Rule
    Properties:
      Name: DWH-EB-OMS-ST-002
      ScheduleExpression: cron(30 15 * * ? *)
      State: ENABLED
      EventBusName: default
      Targets:
        - Id: Id225284f8-466b-446e-a9ae-a762ae4f9012
          Arn:
            Fn::Sub: >-
              arn:aws:lambda:ap-northeast-1:886436956581:function:DWH_LM_DUPLICATE_EVENT_CHECK
          RoleArn: >-
            arn:aws:iam::886436956581:role/role-dev-dlpf-evbr-invoke-lambda
          InputTransformer:
            InputPathsMap:
              time: $.time
            InputTemplate: |-
              {
                  "time": <time>,
                  "jobnet_id": "DWH-JN-OMS-ST-002", 
                  "statemachine_arn": "arn:aws:states:ap-northeast-1:886436956581:stateMachine:DWH-JN-OMS-ST-002"
              }

  DWHEBPOSST001:
    Type: AWS::Events::Rule
    Properties:
      Name: DWH-EB-POS-ST-001
      ScheduleExpression: cron(30 18 * * ? *)
      State: ENABLED
      EventBusName: default
      Targets:
        - Id: Id225284f8-466b-446e-a9ae-a762ae4f9012
          Arn:
            Fn::Sub: >-
              arn:aws:lambda:ap-northeast-1:886436956581:function:DWH_LM_DUPLICATE_EVENT_CHECK
          RoleArn: >-
            arn:aws:iam::886436956581:role/role-dev-dlpf-evbr-invoke-lambda
          InputTransformer:
            InputPathsMap:
              time: $.time
            InputTemplate: |-
              {
                  "time": <time>,
                  "jobnet_id": "DWH-JN-POS-ST-001", 
                  "statemachine_arn": "arn:aws:states:ap-northeast-1:886436956581:stateMachine:DWH-JN-POS-ST-001"
              }

  DWHEBCTLOR001:
    Type: AWS::Events::Rule
    Properties:
      Name: DWH-EB-CTL-OR-001
      ScheduleExpression: cron(30 15 * * ? *)
      State: ENABLED
      EventBusName: default
      Targets:
        - Id: Id225284f8-466b-446e-a9ae-a762ae4f9012
          Arn:
            Fn::Sub: >-
              arn:aws:lambda:ap-northeast-1:886436956581:function:DWH_LM_DUPLICATE_EVENT_CHECK
          RoleArn: >-
            arn:aws:iam::886436956581:role/role-dev-dlpf-evbr-invoke-lambda
          InputTransformer:
            InputPathsMap:
              time: $.time
            InputTemplate: |-
              {
                  "time": <time>,
                  "jobnet_id": "DWH-JN-CTL-OR-001", 
                  "statemachine_arn": "arn:aws:states:ap-northeast-1:886436956581:stateMachine:DWH-JN-CTL-OR-001"
              }

  DWHEBOMSOR002:
    Type: AWS::Events::Rule
    Properties:
      Name: DWH-EB-OMS-OR-002
      ScheduleExpression: cron(30 15 * * ? *)
      State: ENABLED
      EventBusName: default
      Targets:
        - Id: Id225284f8-466b-446e-a9ae-a762ae4f9012
          Arn:
            Fn::Sub: >-
              arn:aws:lambda:ap-northeast-1:886436956581:function:DWH_LM_DUPLICATE_EVENT_CHECK
          RoleArn: >-
            arn:aws:iam::886436956581:role/role-dev-dlpf-evbr-invoke-lambda
          InputTransformer:
            InputPathsMap:
              time: $.time
            InputTemplate: |-
              {
                  "time": <time>,
                  "jobnet_id": "DWH-JN-OMS-OR-002", 
                  "statemachine_arn": "arn:aws:states:ap-northeast-1:886436956581:stateMachine:DWH-JN-OMS-OR-002"
              }

  DWHEBCTLOR002:
    Type: AWS::Events::Rule
    Properties:
      Name: DWH-EB-CTL-OR-002
      ScheduleExpression: cron(30 15 * * ? *)
      State: ENABLED
      EventBusName: default
      Targets:
        - Id: Id225284f8-466b-446e-a9ae-a762ae4f9012
          Arn:
            Fn::Sub: >-
              arn:aws:lambda:ap-northeast-1:886436956581:function:DWH_LM_DUPLICATE_EVENT_CHECK
          RoleArn: >-
            arn:aws:iam::886436956581:role/role-dev-dlpf-evbr-invoke-lambda
          InputTransformer:
            InputPathsMap:
              time: $.time
            InputTemplate: |-
              {
                  "time": <time>,
                  "jobnet_id": "DWH-JN-CTL-OR-002", 
                  "statemachine_arn": "arn:aws:states:ap-northeast-1:886436956581:stateMachine:DWH-JN-CTL-OR-002"
              }

  DWHEBOMSSP001:
    Type: AWS::Events::Rule
    Properties:
      Name: DWH-EB-OMS-SP-001
      ScheduleExpression: cron(30 15 * * ? *)
      State: ENABLED
      EventBusName: default
      Targets:
        - Id: Id225284f8-466b-446e-a9ae-a762ae4f9012
          Arn:
            Fn::Sub: >-
              arn:aws:lambda:ap-northeast-1:886436956581:function:DWH_LM_DUPLICATE_EVENT_CHECK
          RoleArn: >-
            arn:aws:iam::886436956581:role/role-dev-dlpf-evbr-invoke-lambda
          InputTransformer:
            InputPathsMap:
              time: $.time
            InputTemplate: |-
              {
                  "time": <time>,
                  "jobnet_id": "DWH-JN-OMS-SP-001", 
                  "statemachine_arn": "arn:aws:states:ap-northeast-1:886436956581:stateMachine:DWH-JN-OMS-SP-001"
              }

  DWHEBCTLPR001:
    Type: AWS::Events::Rule
    Properties:
      Name: DWH-EB-CTL-PR-001
      ScheduleExpression: cron(30 15 * * ? *)
      State: ENABLED
      EventBusName: default
      Targets:
        - Id: Id225284f8-466b-446e-a9ae-a762ae4f9012
          Arn:
            Fn::Sub: >-
              arn:aws:lambda:ap-northeast-1:886436956581:function:DWH_LM_DUPLICATE_EVENT_CHECK
          RoleArn: >-
            arn:aws:iam::886436956581:role/role-dev-dlpf-evbr-invoke-lambda
          InputTransformer:
            InputPathsMap:
              time: $.time
            InputTemplate: |-
              {
                  "time": <time>,
                  "jobnet_id": "DWH-JN-CTL-PR-001", 
                  "statemachine_arn": "arn:aws:states:ap-northeast-1:886436956581:stateMachine:DWH-JN-CTL-PR-001"
              }

  DWHEBOMSPR001:
    Type: AWS::Events::Rule
    Properties:
      Name: DWH-EB-OMS-PR-001
      ScheduleExpression: cron(30 15 * * ? *)
      State: ENABLED
      EventBusName: default
      Targets:
        - Id: Id225284f8-466b-446e-a9ae-a762ae4f9012
          Arn:
            Fn::Sub: >-
              arn:aws:lambda:ap-northeast-1:886436956581:function:DWH_LM_DUPLICATE_EVENT_CHECK
          RoleArn: >-
            arn:aws:iam::886436956581:role/role-dev-dlpf-evbr-invoke-lambda
          InputTransformer:
            InputPathsMap:
              time: $.time
            InputTemplate: |-
              {
                  "time": <time>,
                  "jobnet_id": "DWH-JN-OMS-PR-001", 
                  "statemachine_arn": "arn:aws:states:ap-northeast-1:886436956581:stateMachine:DWH-JN-OMS-PR-001"
              }

  DWHEBCTLPR002:
    Type: AWS::Events::Rule
    Properties:
      Name: DWH-EB-CTL-PR-002
      ScheduleExpression: cron(30 15 * * ? *)
      State: ENABLED
      EventBusName: default
      Targets:
        - Id: Id225284f8-466b-446e-a9ae-a762ae4f9012
          Arn:
            Fn::Sub: >-
              arn:aws:lambda:ap-northeast-1:886436956581:function:DWH_LM_DUPLICATE_EVENT_CHECK
          RoleArn: >-
            arn:aws:iam::886436956581:role/role-dev-dlpf-evbr-invoke-lambda
          InputTransformer:
            InputPathsMap:
              time: $.time
            InputTemplate: |-
              {
                  "time": <time>,
                  "jobnet_id": "DWH-JN-CTL-PR-002", 
                  "statemachine_arn": "arn:aws:states:ap-northeast-1:886436956581:stateMachine:DWH-JN-CTL-PR-002"
              }

  DWHEBCTLPR003:
    Type: AWS::Events::Rule
    Properties:
      Name: DWH-EB-CTL-PR-003
      ScheduleExpression: cron(30 15 * * ? *)
      State: ENABLED
      EventBusName: default
      Targets:
        - Id: Id225284f8-466b-446e-a9ae-a762ae4f9012
          Arn:
            Fn::Sub: >-
              arn:aws:lambda:ap-northeast-1:886436956581:function:DWH_LM_DUPLICATE_EVENT_CHECK
          RoleArn: >-
            arn:aws:iam::886436956581:role/role-dev-dlpf-evbr-invoke-lambda
          InputTransformer:
            InputPathsMap:
              time: $.time
            InputTemplate: |-
              {
                  "time": <time>,
                  "jobnet_id": "DWH-JN-CTL-PR-003", 
                  "statemachine_arn": "arn:aws:states:ap-northeast-1:886436956581:stateMachine:DWH-JN-CTL-PR-003"
              }

  DWHEBCTLPR004:
    Type: AWS::Events::Rule
    Properties:
      Name: DWH-EB-CTL-PR-004
      ScheduleExpression: cron(30 15 * * ? *)
      State: ENABLED
      EventBusName: default
      Targets:
        - Id: Id225284f8-466b-446e-a9ae-a762ae4f9012
          Arn:
            Fn::Sub: >-
              arn:aws:lambda:ap-northeast-1:886436956581:function:DWH_LM_DUPLICATE_EVENT_CHECK
          RoleArn: >-
            arn:aws:iam::886436956581:role/role-dev-dlpf-evbr-invoke-lambda
          InputTransformer:
            InputPathsMap:
              time: $.time
            InputTemplate: |-
              {
                  "time": <time>,
                  "jobnet_id": "DWH-JN-CTL-PR-004", 
                  "statemachine_arn": "arn:aws:states:ap-northeast-1:886436956581:stateMachine:DWH-JN-CTL-PR-004"
              }

  DWHEBOMSRT001:
    Type: AWS::Events::Rule
    Properties:
      Name: DWH-EB-OMS-RT-001
      ScheduleExpression: cron(30 15 * * ? *)
      State: ENABLED
      EventBusName: default
      Targets:
        - Id: Id225284f8-466b-446e-a9ae-a762ae4f9012
          Arn:
            Fn::Sub: >-
              arn:aws:lambda:ap-northeast-1:886436956581:function:DWH_LM_DUPLICATE_EVENT_CHECK
          RoleArn: >-
            arn:aws:iam::886436956581:role/role-dev-dlpf-evbr-invoke-lambda
          InputTransformer:
            InputPathsMap:
              time: $.time
            InputTemplate: |-
              {
                  "time": <time>,
                  "jobnet_id": "DWH-JN-OMS-RT-001", 
                  "statemachine_arn": "arn:aws:states:ap-northeast-1:886436956581:stateMachine:DWH-JN-OMS-RT-001"
              }

  DWHEBCTLSL001:
    Type: AWS::Events::Rule
    Properties:
      Name: DWH-EB-CTL-SL-001
      ScheduleExpression: cron(30 15 * * ? *)
      State: ENABLED
      EventBusName: default
      Targets:
        - Id: Id225284f8-466b-446e-a9ae-a762ae4f9012
          Arn:
            Fn::Sub: >-
              arn:aws:lambda:ap-northeast-1:886436956581:function:DWH_LM_DUPLICATE_EVENT_CHECK
          RoleArn: >-
            arn:aws:iam::886436956581:role/role-dev-dlpf-evbr-invoke-lambda
          InputTransformer:
            InputPathsMap:
              time: $.time
            InputTemplate: |-
              {
                  "time": <time>,
                  "jobnet_id": "DWH-JN-CTL-SL-001", 
                  "statemachine_arn": "arn:aws:states:ap-northeast-1:886436956581:stateMachine:DWH-JN-CTL-SL-001"
              }

  DWHEBOMSAC001:
    Type: AWS::Events::Rule
    Properties:
      Name: DWH-EB-OMS-AC-001
      ScheduleExpression: cron(00 19 * * ? *)
      State: ENABLED
      EventBusName: default
      Targets:
        - Id: Id225284f8-466b-446e-a9ae-a762ae4f9012
          Arn:
            Fn::Sub: >-
              arn:aws:lambda:ap-northeast-1:886436956581:function:DWH_LM_DUPLICATE_EVENT_CHECK
          RoleArn: >-
            arn:aws:iam::886436956581:role/role-dev-dlpf-evbr-invoke-lambda
          InputTransformer:
            InputPathsMap:
              time: $.time
            InputTemplate: |-
              {
                  "time": <time>,
                  "jobnet_id": "DWH-JN-OMS-AC-001", 
                  "statemachine_arn": "arn:aws:states:ap-northeast-1:886436956581:stateMachine:DWH-JN-OMS-AC-001"
              }

  DWHEBPOSAC001:
    Type: AWS::Events::Rule
    Properties:
      Name: DWH-EB-POS-AC-001
      ScheduleExpression: cron(30 18 * * ? *)
      State: ENABLED
      EventBusName: default
      Targets:
        - Id: Id225284f8-466b-446e-a9ae-a762ae4f9012
          Arn:
            Fn::Sub: >-
              arn:aws:lambda:ap-northeast-1:886436956581:function:DWH_LM_DUPLICATE_EVENT_CHECK
          RoleArn: >-
            arn:aws:iam::886436956581:role/role-dev-dlpf-evbr-invoke-lambda
          InputTransformer:
            InputPathsMap:
              time: $.time
            InputTemplate: |-
              {
                  "time": <time>,
                  "jobnet_id": "DWH-JN-POS-AC-001", 
                  "statemachine_arn": "arn:aws:states:ap-northeast-1:886436956581:stateMachine:DWH-JN-POS-AC-001"
              }

  DWHEBOMSSP002:
    Type: AWS::Events::Rule
    Properties:
      Name: DWH-EB-OMS-SP-002
      ScheduleExpression: cron(30 15 * * ? *)
      State: ENABLED
      EventBusName: default
      Targets:
        - Id: Id225284f8-466b-446e-a9ae-a762ae4f9012
          Arn:
            Fn::Sub: >-
              arn:aws:lambda:ap-northeast-1:886436956581:function:DWH_LM_DUPLICATE_EVENT_CHECK
          RoleArn: >-
            arn:aws:iam::886436956581:role/role-dev-dlpf-evbr-invoke-lambda
          InputTransformer:
            InputPathsMap:
              time: $.time
            InputTemplate: |-
              {
                  "time": <time>,
                  "jobnet_id": "DWH-JN-OMS-SP-002", 
                  "statemachine_arn": "arn:aws:states:ap-northeast-1:886436956581:stateMachine:DWH-JN-OMS-SP-002"
              }

  DWHEBWMSSP001:
    Type: AWS::Events::Rule
    Properties:
      Name: DWH-EB-WMS-SP-001
      ScheduleExpression: cron(30 15 * * ? *)
      State: ENABLED
      EventBusName: default
      Targets:
        - Id: Id225284f8-466b-446e-a9ae-a762ae4f9012
          Arn:
            Fn::Sub: >-
              arn:aws:lambda:ap-northeast-1:886436956581:function:DWH_LM_DUPLICATE_EVENT_CHECK
          RoleArn: >-
            arn:aws:iam::886436956581:role/role-dev-dlpf-evbr-invoke-lambda
          InputTransformer:
            InputPathsMap:
              time: $.time
            InputTemplate: |-
              {
                  "time": <time>,
                  "jobnet_id": "DWH-JN-WMS-SP-001", 
                  "statemachine_arn": "arn:aws:states:ap-northeast-1:886436956581:stateMachine:DWH-JN-WMS-SP-001"
              }

  DWHEBCTLSH001:
    Type: AWS::Events::Rule
    Properties:
      Name: DWH-EB-CTL-SH-001
      ScheduleExpression: cron(30 18 * * ? *)
      State: ENABLED
      EventBusName: default
      Targets:
        - Id: Id225284f8-466b-446e-a9ae-a762ae4f9012
          Arn:
            Fn::Sub: >-
              arn:aws:lambda:ap-northeast-1:886436956581:function:DWH_LM_DUPLICATE_EVENT_CHECK
          RoleArn: >-
            arn:aws:iam::886436956581:role/role-dev-dlpf-evbr-invoke-lambda
          InputTransformer:
            InputPathsMap:
              time: $.time
            InputTemplate: |-
              {
                  "time": <time>,
                  "jobnet_id": "DWH-JN-CTL-SH-001", 
                  "statemachine_arn": "arn:aws:states:ap-northeast-1:886436956581:stateMachine:DWH-JN-CTL-SH-001"
              }

  DWHEBADDIS001:
    Type: AWS::Events::Rule
    Properties:
      Name: DWH-EB-ADD-IS-001
      ScheduleExpression: cron(30 15 * * ? *)
      State: ENABLED
      EventBusName: default
      Targets:
        - Id: Id225284f8-466b-446e-a9ae-a762ae4f9012
          Arn:
            Fn::Sub: >-
              arn:aws:lambda:ap-northeast-1:886436956581:function:DWH_LM_DUPLICATE_EVENT_CHECK
          RoleArn: >-
            arn:aws:iam::886436956581:role/role-dev-dlpf-evbr-invoke-lambda
          InputTransformer:
            InputPathsMap:
              time: $.time
            InputTemplate: |-
              {
                  "time": <time>,
                  "jobnet_id": "DWH-JN-ADD-IS-001", 
                  "statemachine_arn": "arn:aws:states:ap-northeast-1:886436956581:stateMachine:DWH-JN-ADD-IS-001"
              }

  DWHEBWMSIS001:
    Type: AWS::Events::Rule
    Properties:
      Name: DWH-EB-WMS-IS-001
      ScheduleExpression: cron(30 15 * * ? *)
      State: ENABLED
      EventBusName: default
      Targets:
        - Id: Id225284f8-466b-446e-a9ae-a762ae4f9012
          Arn:
            Fn::Sub: >-
              arn:aws:lambda:ap-northeast-1:886436956581:function:DWH_LM_DUPLICATE_EVENT_CHECK
          RoleArn: >-
            arn:aws:iam::886436956581:role/role-dev-dlpf-evbr-invoke-lambda
          InputTransformer:
            InputPathsMap:
              time: $.time
            InputTemplate: |-
              {
                  "time": <time>,
                  "jobnet_id": "DWH-JN-WMS-IS-001", 
                  "statemachine_arn": "arn:aws:states:ap-northeast-1:886436956581:stateMachine:DWH-JN-WMS-IS-001"
              }

  DWHEBAPPIS001:
    Type: AWS::Events::Rule
    Properties:
      Name: DWH-EB-APP-IS-001
      ScheduleExpression: cron(30 15 * * ? *)
      State: ENABLED
      EventBusName: default
      Targets:
        - Id: Id225284f8-466b-446e-a9ae-a762ae4f9012
          Arn:
            Fn::Sub: >-
              arn:aws:lambda:ap-northeast-1:886436956581:function:DWH_LM_DUPLICATE_EVENT_CHECK
          RoleArn: >-
            arn:aws:iam::886436956581:role/role-dev-dlpf-evbr-invoke-lambda
          InputTransformer:
            InputPathsMap:
              time: $.time
            InputTemplate: |-
              {
                  "time": <time>,
                  "jobnet_id": "DWH-JN-APP-IS-001", 
                  "statemachine_arn": "arn:aws:states:ap-northeast-1:886436956581:stateMachine:DWH-JN-APP-IS-001"
              }

  DWHEBCTLFA001:
    Type: AWS::Events::Rule
    Properties:
      Name: DWH-EB-CTL-FA-001
      ScheduleExpression: cron(30 15 * * ? *)
      State: ENABLED
      EventBusName: default
      Targets:
        - Id: Id225284f8-466b-446e-a9ae-a762ae4f9012
          Arn:
            Fn::Sub: >-
              arn:aws:lambda:ap-northeast-1:886436956581:function:DWH_LM_DUPLICATE_EVENT_CHECK
          RoleArn: >-
            arn:aws:iam::886436956581:role/role-dev-dlpf-evbr-invoke-lambda
          InputTransformer:
            InputPathsMap:
              time: $.time
            InputTemplate: |-
              {
                  "time": <time>,
                  "jobnet_id": "DWH-JN-CTL-FA-001", 
                  "statemachine_arn": "arn:aws:states:ap-northeast-1:886436956581:stateMachine:DWH-JN-CTL-FA-001"
              }

  DWHEBWMSST001:
    Type: AWS::Events::Rule
    Properties:
      Name: DWH-EB-WMS-ST-001
      ScheduleExpression: cron(30 15 * * ? *)
      State: ENABLED
      EventBusName: default
      Targets:
        - Id: Id225284f8-466b-446e-a9ae-a762ae4f9012
          Arn:
            Fn::Sub: >-
              arn:aws:lambda:ap-northeast-1:886436956581:function:DWH_LM_DUPLICATE_EVENT_CHECK
          RoleArn: >-
            arn:aws:iam::886436956581:role/role-dev-dlpf-evbr-invoke-lambda
          InputTransformer:
            InputPathsMap:
              time: $.time
            InputTemplate: |-
              {
                  "time": <time>,
                  "jobnet_id": "DWH-JN-WMS-ST-001", 
                  "statemachine_arn": "arn:aws:states:ap-northeast-1:886436956581:stateMachine:DWH-JN-WMS-ST-001"
              }

  DWHEBWMSST002:
    Type: AWS::Events::Rule
    Properties:
      Name: DWH-EB-WMS-ST-002
      ScheduleExpression: cron(30 15 * * ? *)
      State: ENABLED
      EventBusName: default
      Targets:
        - Id: Id225284f8-466b-446e-a9ae-a762ae4f9012
          Arn:
            Fn::Sub: >-
              arn:aws:lambda:ap-northeast-1:886436956581:function:DWH_LM_DUPLICATE_EVENT_CHECK
          RoleArn: >-
            arn:aws:iam::886436956581:role/role-dev-dlpf-evbr-invoke-lambda
          InputTransformer:
            InputPathsMap:
              time: $.time
            InputTemplate: |-
              {
                  "time": <time>,
                  "jobnet_id": "DWH-JN-WMS-ST-002", 
                  "statemachine_arn": "arn:aws:states:ap-northeast-1:886436956581:stateMachine:DWH-JN-WMS-ST-002"
              }

  DWHEBCMNST002:
    Type: AWS::Events::Rule
    Properties:
      Name: DWH-EB-CMN-ST-002
      ScheduleExpression: cron(30 16 * * ? *)
      State: ENABLED
      EventBusName: default
      Targets:
        - Id: Id225284f8-466b-446e-a9ae-a762ae4f9012
          Arn:
            Fn::Sub: >-
              arn:aws:lambda:ap-northeast-1:886436956581:function:DWH_LM_DUPLICATE_EVENT_CHECK
          RoleArn: >-
            arn:aws:iam::886436956581:role/role-dev-dlpf-evbr-invoke-lambda
          InputTransformer:
            InputPathsMap:
              time: $.time
            InputTemplate: |-
              {
                  "time": <time>,
                  "jobnet_id": "DWH-JN-CMN-ST-002", 
                  "statemachine_arn": "arn:aws:states:ap-northeast-1:886436956581:stateMachine:DWH-JN-CMN-ST-002"
              }

