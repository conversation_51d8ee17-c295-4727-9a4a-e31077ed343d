AWSTemplateFormatVersion: '2010-09-09'
Resources:
  StepFunctionsLogGroup:
    Type: AWS::Logs::LogGroup
    Properties:
      LogGroupName: !Sub /aws/vendedlogs/states/DWH-JN-CTL-CO-001

  MyStateMachine:
    Type: "AWS::StepFunctions::StateMachine"
    Properties:
      StateMachineName: "DWH-JN-CTL-CO-001"
      RoleArn: "arn:aws:iam::886436956581:role/role-dev-dlpf-sfn-ctl-dwh"
      LoggingConfiguration:
        Level: ALL
        IncludeExecutionData: true
        Destinations:
          - CloudWatchLogsLogGroup:
              LogGroupArn: !GetAtt StepFunctionsLogGroup.Arn
      DefinitionString:
        Fn::Sub: |
          {
            "Comment": "DWH-JN-CTL-CO-001",
            "StartAt": "Choice01",
            "States": {
              "Choice01": {
                "Type": "Choice",
                "Choices": [
                  {
                    "And": [
                      {
                        "Variable": "$.startState",
                        "IsPresent": true
                      },
                      {
                        "Variable": "$.startState",
                        "StringMatches": "DB_TO_FILE"
                      }
                    ],
                    "Next": "DWH-JN-CDP-CO-001"
                  }
                ],
                "Default": "DWH-JN-OMS-CO-001"
              },
              "DWH-JN-OMS-CO-001": {
                "Type": "Task",
                "Resource": "arn:aws:states:::states:startExecution.sync",
                "Parameters": {
                  "StateMachineArn": "arn:aws:states:ap-northeast-1:886436956581:stateMachine:DWH-JN-OMS-CO-001"
                },
                "Next": "DWH-JN-CDP-CO-001"
              },
              "DWH-JN-CDP-CO-001": {
                "Type": "Task",
                "Resource": "arn:aws:states:::states:startExecution.sync",
                "Parameters": {
                  "StateMachineArn": "arn:aws:states:ap-northeast-1:886436956581:stateMachine:DWH-JN-CDP-CO-001"
                },
                "End": true
              }
            }
          }