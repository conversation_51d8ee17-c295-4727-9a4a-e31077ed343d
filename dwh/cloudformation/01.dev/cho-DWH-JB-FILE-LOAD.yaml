AWSTemplateFormatVersion: '2010-09-09'
Resources:
  GlueJob:
    Type: AWS::Glue::Job
    Properties:
      Name: DWH_JB_FILE_LOAD
      Description: "ファイル取り込む"
      Role: "AWSGlueServiceRole-dev-dlpf-glue-job-dwh"
      GlueVersion: "4.0"
      Command:
        Name: "glueetl"
        ScriptLocation: "s3://aws-glue-assets-886436956581-ap-northeast-1/scripts/dwh/DWH_JB_FILE_LOAD.py"
        PythonVersion: "3"
      DefaultArguments:
        "--additional-python-modules": "redshift_connector"
        "--enable-metrics": "true"
        "--enable-observability-metrics": "true"
        "--enable-continuous-cloudwatch-log": "true"
        "--enable-glue-datacatalog": "true"
        "--TempDir": "s3://aws-glue-assets-886436956581-ap-northeast-1/temporary/"
        "--spark-event-logs-path": "s3://aws-glue-assets-886436956581-ap-northeast-1/sparkHistoryLogs/"
        "--extra-files": "s3://aws-glue-assets-886436956581-ap-northeast-1/scripts/dwh/"
        "--enable-spark-ui": "true"
      Timeout: 2880
      WorkerType: "G.1X"
      NumberOfWorkers: 2
      Connections:
        Connections:
          - "gluecon-mf-gdpf-dev-tyo-a-redshift-001"
      ExecutionProperty:
        MaxConcurrentRuns: 100
