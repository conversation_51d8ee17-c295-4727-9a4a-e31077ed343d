AWSTemplateFormatVersion: '2010-09-09'
Resources:
  StepFunctionsLogGroup:
    Type: AWS::Logs::LogGroup
    Properties:
      LogGroupName: !Sub /aws/vendedlogs/states/DWH-JN-CTL-PR-004

  MyStateMachine:
    Type: "AWS::StepFunctions::StateMachine"
    Properties:
      StateMachineName: "DWH-JN-CTL-PR-004"
      RoleArn: "arn:aws:iam::886436956581:role/role-dev-dlpf-sfn-ctl-dwh"
      LoggingConfiguration:
        Level: ALL
        IncludeExecutionData: true
        Destinations:
          - CloudWatchLogsLogGroup:
              LogGroupArn: !GetAtt StepFunctionsLogGroup.Arn
      DefinitionString:
        Fn::Sub: |
          {
            "Comment": "DWH-JN-CTL-PR-004",
            "StartAt": "Choice01",
            "States": {
              "Choice01": {
                "Type": "Choice",
                "Choices": [
                  {
                    "And": [
                      {
                        "Variable": "$.startState",
                        "IsPresent": true
                      },
                      {
                        "Variable": "$.startState",
                        "StringMatches": "DB_TO_FILE"
                      }
                    ],
                    "Next": "DWH-JN-CDP-PR-013"
                  }
                ],
                "Default": "DWH-JN-OMS-PR-004"
              },
              "DWH-JN-OMS-PR-004": {
                "Type": "Task",
                "Resource": "arn:aws:states:::states:startExecution.sync",
                "Parameters": {
                  "StateMachineArn": "arn:aws:states:ap-northeast-1:886436956581:stateMachine:DWH-JN-OMS-PR-004"
                },
                "Next": "DWH-JN-CDP-PR-013"
              },
              "DWH-JN-CDP-PR-013": {
                "Type": "Task",
                "Resource": "arn:aws:states:::states:startExecution.sync",
                "Parameters": {
                  "StateMachineArn": "arn:aws:states:ap-northeast-1:886436956581:stateMachine:DWH-JN-CDP-PR-013"
                },
                "End": true
              }
            }
          }