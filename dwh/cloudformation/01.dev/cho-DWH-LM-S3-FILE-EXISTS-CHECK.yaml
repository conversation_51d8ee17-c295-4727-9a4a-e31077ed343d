AWSTemplateFormatVersion: '2010-09-09'
Resources:
  MyLambdaFunction:
    Type: 'AWS::Lambda::Function'
    Properties:
      FunctionName: DWH_LM_S3_FILE_EXISTS_CHECK
      Handler: lambda_function.lambda_handler
      Runtime: python3.13
      Role: 'arn:aws:iam::886436956581:role/role-dev-dlpf-s3file-exists-check-dwh'
      Code:
        S3Bucket: 'aws-glue-assets-886436956581-ap-northeast-1'
        S3Key: 'scripts/dwh/DWH_LM_S3_FILE_EXISTS_CHECK.zip'
      Environment: 
        Variables:
          S3_BUCKET_NAME: 's3-dev-dlpf-if-886436956581'
