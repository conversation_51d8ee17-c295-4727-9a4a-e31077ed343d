AWSTemplateFormatVersion: "2010-09-09"

Resources:
  StepFunctionsLogGroup:
    Type: AWS::Logs::LogGroup
    Properties:
      LogGroupName: !Sub /aws/vendedlogs/states/DWH-JN-CDP-OR-001

  MyStateMachine:
    Type: "AWS::StepFunctions::StateMachine"
    Properties:
      StateMachineName: "DWH-JN-CDP-OR-001"
      RoleArn: "arn:aws:iam::886436956581:role/role-dev-dlpf-sfn-dwh"
      LoggingConfiguration:
        Level: ALL
        IncludeExecutionData: true
        Destinations:
          - CloudWatchLogsLogGroup:
              LogGroupArn: !GetAtt StepFunctionsLogGroup.Arn
      DefinitionString:
        Fn::Sub: |
          {
            "StartAt": "Run Glue Job",
            "States": {
              "Run Glue Job": {
                "Type": "Task",
                "Resource": "arn:aws:states:::glue:startJobRun.sync",
                "Parameters": {
                  "JobName": "DWH_JB_DB_TO_FILE",
                  "Arguments": {
                    "--IF_IDS": "\"[\\\"IF-CDP-OR-001\\\",\\\"IF-CDP-OR-002\\\"]\""
                  }
                },
                "End": true
              }
            }
          }
