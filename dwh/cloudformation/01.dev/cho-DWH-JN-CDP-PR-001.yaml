AWSTemplateFormatVersion: "2010-09-09"

Resources:
  StepFunctionsLogGroup:
    Type: AWS::Logs::LogGroup
    Properties:
      LogGroupName: !Sub /aws/vendedlogs/states/DWH-JN-CDP-PR-001

  MyStateMachine:
    Type: "AWS::StepFunctions::StateMachine"
    Properties:
      StateMachineName: "DWH-JN-CDP-PR-001"
      RoleArn: "arn:aws:iam::886436956581:role/role-dev-dlpf-sfn-dwh"
      LoggingConfiguration:
        Level: ALL
        IncludeExecutionData: true
        Destinations:
          - CloudWatchLogsLogGroup:
              LogGroupArn: !GetAtt StepFunctionsLogGroup.Arn
      DefinitionString:
        Fn::Sub: |
          {
            "StartAt": "Run Glue Job",
            "States": {
              "Run Glue Job": {
                "Type": "Task",
                "Resource": "arn:aws:states:::glue:startJobRun.sync",
                "Parameters": {
                  "JobName": "DWH_JB_DB_TO_FILE",
                  "Arguments": {
                    "--IF_IDS": "\"[\\\"IF-CDP-PR-001\\\",\\\"IF-CDP-PR-002\\\",\\\"IF-CDP-PR-003\\\",\\\"IF-CDP-PR-004\\\",\\\"IF-CDP-PR-005\\\",\\\"IF-CDP-PR-006\\\",\\\"IF-CDP-PR-007\\\",\\\"IF-CDP-PR-008\\\",\\\"IF-CDP-PR-009\\\",\\\"IF-CDP-PR-010\\\"]\""
                  }
                },
                "End": true
              }
            }
          }
